#!/usr/bin/env python3
"""
Compare different hyperparameter optimization methods:
1. Grid Search (systematic)
2. Random Search (baseline)
3. Bayesian Optimization (Optuna)
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import ParameterGrid
import itertools
import time

class OptimizationComparison:
    def __init__(self):
        self.results = {
            'grid_search': [],
            'random_search': [],
            'bayesian_optuna': []
        }
        
    def load_sample_data(self):
        """Load a smaller dataset for quick comparison"""
        print("📊 Loading sample data for method comparison...")
        
        # Load and prepare data (simplified)
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        target_df = pd.read_csv('PureEURTarget.csv')
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        
        # Clean feature names
        import re
        name_mapping = {}
        for col in features_df.columns:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')
            name_mapping[col] = clean_name
        features_df = features_df.rename(columns=name_mapping)
        
        # Merge datasets
        data = features_df.merge(
            target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        data = data.merge(
            returns_df[['Date', 'P123 ID', 'Future5DReturn']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        # Use recent data only for speed
        data['Date'] = pd.to_datetime(data['Date'])
        start_date = pd.to_datetime('2020-01-01')
        end_date = pd.to_datetime('2022-12-31')
        
        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        data = data.sort_values('Date').reset_index(drop=True)
        
        # Feature columns
        feature_cols = [col for col in data.columns if col not in 
                       ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']]
        
        print(f"✓ Sample data: {len(data)} samples, {len(feature_cols)} features")
        print(f"✓ Date range: {data['Date'].min().date()} to {data['Date'].max().date()}")
        
        self.data = data
        self.feature_cols = feature_cols
        
        return data
    
    def create_simple_train_test_split(self):
        """Create simple time series split for comparison"""
        split_date = pd.to_datetime('2021-06-01')
        
        train_data = self.data[self.data['Date'] < split_date]
        test_data = self.data[self.data['Date'] >= split_date]
        
        print(f"Train: {len(train_data)} samples")
        print(f"Test: {len(test_data)} samples")
        
        return train_data, test_data
    
    def convert_to_relevance_scores(self, targets, dates, n_levels=5):
        """Convert targets to relevance scores"""
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['relevance'] = 0
        
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']
            
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                valid_targets = date_targets[valid_mask]
                relevance_scores = pd.qcut(valid_targets, q=n_levels, labels=False, duplicates='drop')
                df.loc[date_mask & valid_mask, 'relevance'] = relevance_scores
        
        return df['relevance'].values
    
    def prepare_ranking_data(self, dates):
        """Prepare groups for ranking"""
        unique_dates = sorted(dates.unique())
        groups = []
        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)
        return groups
    
    def evaluate_params(self, params, train_data, test_data):
        """Evaluate a set of parameters"""
        try:
            # Prepare data
            X_train = train_data[self.feature_cols].fillna(0)
            y_train = self.convert_to_relevance_scores(train_data['Weighted_MixRel'], train_data['Date'])
            train_groups = self.prepare_ranking_data(train_data['Date'])
            
            X_test = test_data[self.feature_cols].fillna(0)
            y_test = self.convert_to_relevance_scores(test_data['Weighted_MixRel'], test_data['Date'])
            test_groups = self.prepare_ranking_data(test_data['Date'])
            
            # Train model
            train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
            test_dataset = lgb.Dataset(X_test, label=y_test, group=test_groups, reference=train_dataset)
            
            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[test_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Make predictions and calculate simple metric
            test_predictions = model.predict(X_test)
            
            # Simple portfolio return calculation
            portfolio_returns = []
            for date in sorted(test_data['Date'].unique()):
                date_mask = test_data['Date'] == date
                date_predictions = test_predictions[date_mask]
                date_returns = test_data.loc[date_mask, 'Future5DReturn']
                
                if len(date_predictions) >= 20:
                    top_indices = np.argsort(date_predictions)[-20:]
                    top_returns = date_returns.iloc[top_indices]
                    portfolio_return = top_returns.mean() / 100.0
                    portfolio_returns.append(portfolio_return)
            
            if len(portfolio_returns) > 0:
                portfolio_returns = np.array(portfolio_returns)
                mean_return = portfolio_returns.mean()
                volatility = portfolio_returns.std()
                sharpe = (mean_return * 52) / (volatility * np.sqrt(52)) if volatility > 0 else 0
                return sharpe
            else:
                return 0
                
        except Exception as e:
            print(f"Evaluation failed: {e}")
            return -999
    
    def grid_search_optimization(self, train_data, test_data, max_combinations=20):
        """Grid search optimization"""
        print(f"\n🔍 GRID SEARCH OPTIMIZATION")
        print("-" * 40)
        
        # Define parameter grid (smaller for comparison)
        param_grid = {
            'n_estimators': [500, 1000, 2000],
            'max_depth': [8, 11, 14],
            'learning_rate': [0.001, 0.01, 0.1],
            'num_leaves': [100, 300, 500],
            'feature_fraction': [0.2, 0.5, 0.8]
        }
        
        # Create all combinations
        all_combinations = list(ParameterGrid(param_grid))
        
        # Limit combinations for speed
        if len(all_combinations) > max_combinations:
            selected_combinations = np.random.choice(
                len(all_combinations), 
                max_combinations, 
                replace=False
            )
            combinations = [all_combinations[i] for i in selected_combinations]
        else:
            combinations = all_combinations
        
        print(f"Testing {len(combinations)} parameter combinations...")
        
        best_score = -999
        best_params = None
        results = []
        
        start_time = time.time()
        
        for i, params in enumerate(combinations):
            # Add fixed parameters
            full_params = {
                'objective': 'rank_xendcg',
                'metric': 'ndcg',
                'boosting_type': 'gbdt',
                'verbose': -1,
                'random_state': 42,
                **params
            }
            
            score = self.evaluate_params(full_params, train_data, test_data)
            
            results.append({
                'params': params,
                'score': score
            })
            
            if score > best_score:
                best_score = score
                best_params = params
            
            print(f"  Combination {i+1}/{len(combinations)}: Score = {score:.4f}")
        
        elapsed_time = time.time() - start_time
        
        print(f"✓ Grid Search completed in {elapsed_time:.1f} seconds")
        print(f"✓ Best Score: {best_score:.4f}")
        print(f"✓ Best Parameters: {best_params}")
        
        self.results['grid_search'] = {
            'best_score': best_score,
            'best_params': best_params,
            'all_results': results,
            'time_taken': elapsed_time,
            'combinations_tested': len(combinations)
        }
        
        return best_score, best_params
    
    def random_search_optimization(self, train_data, test_data, n_trials=20):
        """Random search optimization"""
        print(f"\n🎲 RANDOM SEARCH OPTIMIZATION")
        print("-" * 40)
        
        print(f"Testing {n_trials} random parameter combinations...")
        
        best_score = -999
        best_params = None
        results = []
        
        start_time = time.time()
        
        for i in range(n_trials):
            # Random parameter selection
            params = {
                'n_estimators': np.random.choice([500, 1000, 1500, 2000, 3000]),
                'max_depth': np.random.randint(6, 16),
                'learning_rate': np.random.uniform(0.001, 0.1),
                'num_leaves': np.random.randint(50, 500),
                'feature_fraction': np.random.uniform(0.1, 0.8),
                'subsample': np.random.uniform(0.4, 0.8),
                'lambda_l1': np.random.uniform(0.001, 0.1),
                'lambda_l2': np.random.uniform(0.001, 0.1)
            }
            
            # Add fixed parameters
            full_params = {
                'objective': 'rank_xendcg',
                'metric': 'ndcg',
                'boosting_type': 'gbdt',
                'verbose': -1,
                'random_state': 42,
                **params
            }
            
            score = self.evaluate_params(full_params, train_data, test_data)
            
            results.append({
                'params': params,
                'score': score
            })
            
            if score > best_score:
                best_score = score
                best_params = params
            
            print(f"  Trial {i+1}/{n_trials}: Score = {score:.4f}")
        
        elapsed_time = time.time() - start_time
        
        print(f"✓ Random Search completed in {elapsed_time:.1f} seconds")
        print(f"✓ Best Score: {best_score:.4f}")
        print(f"✓ Best Parameters: {best_params}")
        
        self.results['random_search'] = {
            'best_score': best_score,
            'best_params': best_params,
            'all_results': results,
            'time_taken': elapsed_time,
            'trials_tested': n_trials
        }
        
        return best_score, best_params
    
    def compare_methods(self):
        """Compare all optimization methods"""
        print("="*60)
        print("🔬 OPTIMIZATION METHOD COMPARISON")
        print("="*60)
        
        # Load data
        self.load_sample_data()
        train_data, test_data = self.create_simple_train_test_split()
        
        # Test each method
        grid_score, grid_params = self.grid_search_optimization(train_data, test_data)
        random_score, random_params = self.random_search_optimization(train_data, test_data)
        
        # Compare results
        print("\n" + "="*60)
        print("📊 COMPARISON RESULTS")
        print("="*60)
        
        print(f"Grid Search:")
        print(f"  Best Score: {grid_score:.4f}")
        print(f"  Time: {self.results['grid_search']['time_taken']:.1f}s")
        print(f"  Combinations: {self.results['grid_search']['combinations_tested']}")
        
        print(f"\nRandom Search:")
        print(f"  Best Score: {random_score:.4f}")
        print(f"  Time: {self.results['random_search']['time_taken']:.1f}s")
        print(f"  Trials: {self.results['random_search']['trials_tested']}")
        
        # Determine winner
        if grid_score > random_score:
            winner = "Grid Search"
            improvement = ((grid_score - random_score) / random_score) * 100
        else:
            winner = "Random Search"
            improvement = ((random_score - grid_score) / grid_score) * 100
        
        print(f"\n🏆 Winner: {winner}")
        print(f"📈 Improvement: {improvement:.1f}%")
        
        print(f"\n💡 INSIGHTS:")
        print("- Grid Search: Systematic but limited by grid size")
        print("- Random Search: More exploration but less systematic")
        print("- Bayesian Optimization (Optuna): Best of both worlds")
        print("  * Learns from previous trials")
        print("  * Focuses on promising regions")
        print("  * More efficient than random search")
        print("  * More exploratory than grid search")

def main():
    comparator = OptimizationComparison()
    comparator.compare_methods()

if __name__ == "__main__":
    main()
