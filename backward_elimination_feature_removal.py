#!/usr/bin/env python3
"""
Backward elimination: Test removing each original feature to identify harmful ones
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
import json
import os
import logging
from datetime import datetime

# Setup comprehensive logging
def setup_logging():
    """Setup comprehensive logging to file and console"""

    # Create logs directory
    os.makedirs('backward_elimination_results', exist_ok=True)

    # Setup logger
    logger = logging.getLogger('BackwardElimination')
    logger.setLevel(logging.INFO)

    # Clear any existing handlers
    logger.handlers.clear()

    # File handler
    log_file = f'backward_elimination_results/backward_elimination_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility"""
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', str(name))
    cleaned = re.sub(r'_+', '_', cleaned).strip('_')
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    return cleaned or 'Feature_Unknown'

def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3):
    """Create time-based train/validation splits"""
    splits = []
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    current_date = min_date
    while current_date < max_date:
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        current_date = val_start
    
    return splits

def validate_feature_removal(X, y, dates, returns, removed_feature_name, baseline_performance):
    """Test removing one feature and measure performance impact"""
    
    # Load config
    with open('lofep_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Create time splits
    splits = create_time_splits(dates, 
                               config['validation']['train_months'], 
                               config['validation']['validation_months'])
    
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    # Use only first 10 folds for speed (can increase later)
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits[:10]):
        try:
            # Create masks
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Prepare fold data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            
            # Remove NaN targets
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()
            
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            # Train model
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate portfolio performance
            val_returns_data = returns[val_mask][val_valid_mask]
            val_dates_data = dates[val_mask][val_valid_mask]
            
            # Portfolio construction: top 15 equal-weighted stocks
            portfolio_returns = []
            
            for date in sorted(val_dates_data.unique()):
                date_mask = val_dates_data == date
                date_predictions = y_pred[date_mask]
                date_returns = val_returns_data[date_mask]
                
                if len(date_predictions) >= 15:
                    # Select top 15 stocks by prediction
                    top_indices = np.argsort(date_predictions)[-15:]
                    top_returns = date_returns.iloc[top_indices]
                    
                    # Equal-weighted portfolio return (convert percentage to decimal)
                    portfolio_return = top_returns.mean() / 100.0
                    portfolio_returns.append(portfolio_return)
            
            if len(portfolio_returns) > 0:
                portfolio_returns = np.array(portfolio_returns)
                
                # Calculate Sharpe ratio
                mean_return = portfolio_returns.mean()
                volatility = portfolio_returns.std()
                
                # Annualize (weekly rebalancing, 52 periods per year)
                annual_return = mean_return * 52
                annual_volatility = volatility * np.sqrt(52)
                sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
                
                fold_results.append(sharpe)
        
        except Exception as e:
            print(f"    Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Calculate performance metrics
    if len(fold_results) >= 3:  # Need at least 3 folds for reliable estimate
        mean_sharpe = np.mean(fold_results)
        std_sharpe = np.std(fold_results)
        
        # Calculate improvement (positive = removing feature helped)
        sharpe_improvement = mean_sharpe - baseline_performance
        
        return {
            'mean_sharpe_ratio': mean_sharpe,
            'std_sharpe_ratio': std_sharpe,
            'sharpe_improvement': sharpe_improvement,
            'num_folds': len(fold_results),
            'stability_score': 1.0 / (1.0 + std_sharpe) if std_sharpe > 0 else 1.0
        }
    else:
        return None

def run_backward_elimination():
    """Run backward elimination on all original features"""

    # Setup logging
    logger = setup_logging()

    logger.info("🔄 BACKWARD ELIMINATION - FEATURE REMOVAL TESTING")
    logger.info("="*70)
    print("🔄 BACKWARD ELIMINATION - FEATURE REMOVAL TESTING")
    print("="*70)
    
    # Load the enhanced dataset
    print("Loading enhanced dataset...")
    enhanced_data = pd.read_csv('enhanced_dataset_top50_features.csv')
    
    print(f"Enhanced dataset: {enhanced_data.shape}")
    
    # Identify original features (exclude metadata, target, and regenerated features)
    exclude_cols = ['Date', 'P123 ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn', 'Market_Cap_Quintile']
    
    # Load original dataset to identify baseline features
    original_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    original_feature_names = set(original_df.columns)
    
    # Get original features only
    original_features = [col for col in enhanced_data.columns 
                        if col in original_feature_names and 
                        col not in exclude_cols and 
                        enhanced_data[col].dtype in ['int64', 'float64']]
    
    print(f"Original features to test: {len(original_features)}")
    
    # Establish baseline performance (with all features)
    print("\nEstablishing baseline performance...")
    
    # Get all features (original + regenerated)
    all_features = [col for col in enhanced_data.columns 
                   if col not in exclude_cols and 
                   enhanced_data[col].dtype in ['int64', 'float64']]
    
    # Clean feature names
    feature_mapping = {}
    for feature in all_features:
        cleaned_name = clean_feature_name(feature)
        feature_mapping[feature] = cleaned_name
    
    # Prepare baseline data
    X_baseline = enhanced_data[all_features].copy()
    X_baseline.columns = [feature_mapping[col] for col in X_baseline.columns]
    X_baseline = X_baseline.fillna(0)
    X_baseline = X_baseline.replace([np.inf, -np.inf], 0)
    
    y = enhanced_data['Weighted_MixRel']
    dates = pd.to_datetime(enhanced_data['Date'])
    returns = enhanced_data['Future5DReturn']
    
    print("Running baseline validation...")
    baseline_result = validate_feature_removal(X_baseline, y, dates, returns, "BASELINE", 0.0)
    
    if not baseline_result:
        print("❌ Failed to establish baseline performance")
        return
    
    baseline_sharpe = baseline_result['mean_sharpe_ratio']
    print(f"✓ Baseline Sharpe: {baseline_sharpe:.4f} ± {baseline_result['std_sharpe_ratio']:.4f}")
    
    # Test removing each original feature
    print(f"\nTesting removal of {len(original_features)} original features...")
    print("(Using 10 folds per test for speed)")
    
    removal_results = {}
    
    # Create results directory
    os.makedirs('backward_elimination_results', exist_ok=True)
    
    for i, feature_to_remove in enumerate(original_features):
        try:
            msg = f"[{i+1:3d}/{len(original_features)}] Testing removal of: {feature_to_remove[:50]}"
            print(f"\n{msg}")
            logger.info(msg)
            
            # Create feature set without this feature
            features_without_removed = [f for f in all_features if f != feature_to_remove]
            
            # Prepare data without the removed feature
            X_test = enhanced_data[features_without_removed].copy()
            
            # Clean feature names
            test_feature_mapping = {}
            for feature in features_without_removed:
                cleaned_name = clean_feature_name(feature)
                test_feature_mapping[feature] = cleaned_name
            
            X_test.columns = [test_feature_mapping[col] for col in X_test.columns]
            X_test = X_test.fillna(0)
            X_test = X_test.replace([np.inf, -np.inf], 0)
            
            # Test performance without this feature
            result = validate_feature_removal(X_test, y, dates, returns, feature_to_remove, baseline_sharpe)
            
            if result:
                removal_results[feature_to_remove] = result
                improvement = result['sharpe_improvement']
                
                new_sharpe = result['mean_sharpe_ratio']

                if improvement > 0.01:
                    msg1 = f"    ✅ REMOVING HELPS: {improvement:+.4f} Sharpe improvement"
                    msg2 = f"       Baseline: {baseline_sharpe:.4f} → New: {new_sharpe:.4f}"
                    print(msg1)
                    print(msg2)
                    logger.info(f"{feature_to_remove}: {msg1}")
                    logger.info(f"{feature_to_remove}: {msg2}")
                elif improvement < -0.01:
                    msg1 = f"    ❌ REMOVING HURTS: {improvement:+.4f} Sharpe impact"
                    msg2 = f"       Baseline: {baseline_sharpe:.4f} → New: {new_sharpe:.4f}"
                    print(msg1)
                    print(msg2)
                    logger.info(f"{feature_to_remove}: {msg1}")
                    logger.info(f"{feature_to_remove}: {msg2}")
                else:
                    msg1 = f"    ⚪ NEUTRAL: {improvement:+.4f} Sharpe impact"
                    msg2 = f"       Baseline: {baseline_sharpe:.4f} → New: {new_sharpe:.4f}"
                    print(msg1)
                    print(msg2)
                    logger.info(f"{feature_to_remove}: {msg1}")
                    logger.info(f"{feature_to_remove}: {msg2}")
                
                # Save intermediate results every 25 features (more frequent)
                if (i + 1) % 25 == 0:
                    intermediate_file = f'backward_elimination_results/intermediate_results_{i+1}.json'
                    intermediate_data = {
                        'baseline_sharpe': baseline_sharpe,
                        'features_tested': i + 1,
                        'total_features': len(original_features),
                        'results': removal_results,
                        'timestamp': datetime.now().isoformat()
                    }
                    with open(intermediate_file, 'w') as f:
                        json.dump(intermediate_data, f, indent=2, default=str)
                    print(f"    💾 Saved intermediate results to {intermediate_file}")

                    # Also save a summary of best/worst so far
                    if removal_results:
                        improvements = [(feat, res['sharpe_improvement']) for feat, res in removal_results.items()]
                        improvements.sort(key=lambda x: x[1], reverse=True)

                        print(f"    📊 Progress Summary ({i+1}/{len(original_features)}):")
                        print(f"       Best removal so far: {improvements[0][0][:40]} ({improvements[0][1]:+.4f})")
                        print(f"       Worst removal so far: {improvements[-1][0][:40]} ({improvements[-1][1]:+.4f})")
            else:
                print(f"    ⚠️  Failed to validate removal")
        
        except Exception as e:
            print(f"    ❌ Error testing {feature_to_remove}: {str(e)}")
            continue
    
    # Analyze results
    print(f"\n📊 BACKWARD ELIMINATION RESULTS:")
    print("="*60)
    
    if removal_results:
        # Sort by improvement (positive = removing helps)
        improvements = [(feat, result['sharpe_improvement']) for feat, result in removal_results.items()]
        improvements.sort(key=lambda x: x[1], reverse=True)
        
        # Features that hurt performance (removing them helps)
        harmful_features = [(feat, imp) for feat, imp in improvements if imp > 0.01]
        neutral_features = [(feat, imp) for feat, imp in improvements if -0.01 <= imp <= 0.01]
        helpful_features = [(feat, imp) for feat, imp in improvements if imp < -0.01]
        
        print(f"Features tested: {len(removal_results)}")
        print(f"Harmful features (removing helps): {len(harmful_features)}")
        print(f"Neutral features: {len(neutral_features)}")
        print(f"Helpful features (removing hurts): {len(helpful_features)}")
        
        if harmful_features:
            print(f"\n🗑️  TOP 20 FEATURES TO REMOVE (Most Harmful):")
            for i, (feat, imp) in enumerate(harmful_features[:20]):
                print(f"  {i+1:2d}. {feat[:50]:<50} {imp:+.4f}")
        
        if helpful_features:
            print(f"\n💎 TOP 10 FEATURES TO KEEP (Most Helpful):")
            for i, (feat, imp) in enumerate(helpful_features[-10:]):
                print(f"  {i+1:2d}. {feat[:50]:<50} {imp:+.4f}")
        
        # Save final results
        final_results = {
            'baseline_sharpe': baseline_sharpe,
            'features_tested': len(removal_results),
            'harmful_features': harmful_features,
            'neutral_features': neutral_features,
            'helpful_features': helpful_features,
            'all_results': removal_results,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('backward_elimination_results/final_results.json', 'w') as f:
            json.dump(final_results, f, indent=2, default=str)
        
        print(f"\n💾 Final results saved to: backward_elimination_results/final_results.json")
        
        # Estimate potential improvement
        if harmful_features:
            total_potential_improvement = sum(imp for _, imp in harmful_features)
            print(f"\n🎯 Potential Sharpe improvement by removing all harmful features: {total_potential_improvement:+.4f}")
            print(f"   From {baseline_sharpe:.4f} to {baseline_sharpe + total_potential_improvement:.4f}")
    
    else:
        print("❌ No successful feature removal tests")

if __name__ == "__main__":
    run_backward_elimination()
