#!/usr/bin/env python3
"""
Simple baseline validation - memory efficient, no complex merging
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import re
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM"""
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', str(name))
    cleaned = re.sub(r'_+', '_', cleaned).strip('_')
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    return cleaned or 'Feature_Unknown'

def simple_validation():
    """Simple validation using just the target and returns files"""
    
    print("🎯 Simple Baseline Validation")
    print("="*50)
    
    # Load just the target and returns files
    print("Loading target and returns...")
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    print(f"Target: {target_df.shape}")
    print(f"Returns: {returns_df.shape}")
    
    # Merge target and returns only
    data = target_df.merge(returns_df, on=['Date', 'P123 ID'], how='inner')
    print(f"Merged: {data.shape}")
    
    # Check the data
    print(f"\nTarget stats:")
    print(data['Weighted_MixRel'].describe())
    print(f"\nReturns stats:")
    print(data['Future5DReturn'].describe())
    
    # Convert dates
    data['Date'] = pd.to_datetime(data['Date'])
    
    # Simple time series splits
    dates = sorted(data['Date'].unique())
    print(f"Date range: {dates[0]} to {dates[-1]}")
    print(f"Total dates: {len(dates)}")
    
    # Create simple splits - every 3 months
    splits = []
    for i in range(0, len(dates) - 90, 90):  # 90-day steps
        if i + 180 < len(dates):  # Need at least 180 days total
            train_dates = dates[i:i+90]
            val_dates = dates[i+90:i+180]
            splits.append((train_dates, val_dates))
    
    print(f"Created {len(splits)} simple splits")
    
    fold_results = []
    
    for fold_idx, (train_dates, val_dates) in enumerate(splits[:10]):  # Limit to 10 folds for speed
        try:
            # Get data for this fold
            train_data = data[data['Date'].isin(train_dates)].copy()
            val_data = data[data['Date'].isin(val_dates)].copy()
            
            if len(train_data) < 100 or len(val_data) < 50:
                continue
            
            print(f"Fold {fold_idx}: Train={len(train_data)}, Val={len(val_data)}")
            
            # Use target as both feature and target (simple baseline)
            X_train = train_data[['Weighted_MixRel']].copy()
            y_train = train_data['Weighted_MixRel'].copy()
            X_val = val_data[['Weighted_MixRel']].copy()
            y_val = val_data['Weighted_MixRel'].copy()
            
            # Clean data
            X_train = X_train.fillna(0)
            X_val = X_val.fillna(0)
            y_train = y_train.fillna(0)
            y_val = y_val.fillna(0)
            
            # Train simple model
            train_dataset = lgb.Dataset(X_train, label=y_train)
            val_dataset = lgb.Dataset(X_val, label=y_val, reference=train_dataset)
            
            params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1
            }
            
            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[val_dataset],
                num_boost_round=100,
                callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
            )
            
            # Make predictions
            predictions = model.predict(X_val)
            
            # Calculate portfolio performance
            val_returns = val_data['Future5DReturn'].values
            val_dates_unique = sorted(val_data['Date'].unique())
            
            portfolio_returns = []
            
            for date in val_dates_unique:
                date_mask = val_data['Date'] == date
                date_data = val_data[date_mask].copy()
                
                if len(date_data) >= 15:
                    # Get predictions for this date
                    date_predictions = predictions[date_mask.values]
                    date_returns = date_data['Future5DReturn'].values
                    
                    # Select top 15 stocks
                    top_indices = np.argsort(date_predictions)[-15:]
                    top_returns = date_returns[top_indices]
                    
                    # Portfolio return (convert percentage to decimal)
                    portfolio_return = np.mean(top_returns) / 100.0
                    portfolio_returns.append(portfolio_return)
            
            if len(portfolio_returns) > 0:
                portfolio_returns = np.array(portfolio_returns)
                
                # Calculate Sharpe ratio
                mean_return = np.mean(portfolio_returns)
                std_return = np.std(portfolio_returns)
                
                if std_return > 0:
                    # Annualize (weekly rebalancing)
                    annual_return = mean_return * 52
                    annual_volatility = std_return * np.sqrt(52)
                    sharpe = annual_return / annual_volatility
                else:
                    sharpe = 0
                
                fold_results.append(sharpe)
                
                print(f"  Sharpe: {sharpe:.3f}, Mean Return: {mean_return:.4f}, Volatility: {std_return:.4f}")
            
        except Exception as e:
            print(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Final results
    if fold_results:
        mean_sharpe = np.mean(fold_results)
        std_sharpe = np.std(fold_results)
        
        print(f"\n🎯 SIMPLE BASELINE RESULTS:")
        print(f"="*40)
        print(f"Mean Sharpe: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Folds: {len(fold_results)}")
        print(f"Min: {np.min(fold_results):.4f}")
        print(f"Max: {np.max(fold_results):.4f}")
        
        # Check if results are reasonable
        if 0.5 <= mean_sharpe <= 5.0:
            print("✅ Results look reasonable!")
        elif mean_sharpe > 10:
            print("⚠️  Sharpe too high - possible data leakage")
        else:
            print("⚠️  Sharpe very low - check data quality")
        
        return mean_sharpe
    else:
        print("❌ No successful folds")
        return 0

def test_data_leakage():
    """Quick test for obvious data leakage"""
    print("\n🔍 Testing for Data Leakage")
    print("="*30)
    
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    # Merge and check correlation
    data = target_df.merge(returns_df, on=['Date', 'P123 ID'], how='inner')
    
    correlation = np.corrcoef(data['Weighted_MixRel'], data['Future5DReturn'])[0, 1]
    print(f"Target-Returns correlation: {correlation:.4f}")
    
    if correlation > 0.9:
        print("🚨 HIGH CORRELATION - Possible data leakage!")
    elif correlation > 0.3:
        print("✅ Moderate correlation - Normal for good features")
    else:
        print("⚠️  Low correlation - Check if target is predictive")
    
    # Check return magnitudes
    mean_return = data['Future5DReturn'].mean()
    print(f"Mean return: {mean_return:.4f}")
    
    if abs(mean_return) > 10:
        print("⚠️  Returns appear to be in percentage form")
    else:
        print("✅ Returns appear to be in decimal form")

if __name__ == "__main__":
    test_data_leakage()
    sharpe = simple_validation()
    
    print(f"\n📊 Summary:")
    print(f"Simple baseline Sharpe: {sharpe:.4f}")
    
    if sharpe > 0:
        print("✅ Validation working - can now test LOFEP features")
    else:
        print("❌ Validation failed - need to fix data issues first")
