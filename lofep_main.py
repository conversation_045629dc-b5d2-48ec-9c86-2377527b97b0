#!/usr/bin/env python3
"""
LightGBM-Optimized Feature Engineering Pipeline (LOFEP)
Main execution module for advanced feature engineering and validation
"""

import pandas as pd
import numpy as np
import yaml
import logging
import os
import signal
import sys
from datetime import datetime
from pathlib import Path

from lofep_transformation_engine import TransformationEngine
from lofep_validation_engine import ValidationEngine
from lofep_selection_engine import SelectionEngine
from lofep_monitoring_engine import MonitoringEngine
from lofep_utils import setup_logging, create_output_directories

# Global variable to track current operation
current_operation = "initialization"

def signal_handler(signum, frame):
    """Handle termination signals and log current operation"""
    logging.getLogger('LOFEP').error(f"Process terminated with signal {signum} during: {current_operation}")
    logging.getLogger('LOFEP').error(f"Frame info: {frame.f_code.co_filename}:{frame.f_lineno}")
    sys.exit(1)

# Register signal handlers
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)


class LightGBMOptimizedFeatureEngineeringPipeline:
    """
    Main pipeline class for LightGBM-optimized feature engineering
    """
    
    def __init__(self, config_path="lofep_config.yaml"):
        """Initialize the pipeline with configuration"""
        self.config = self._load_config(config_path)
        self.logger = setup_logging(self.config['logging'])
        
        # Initialize engines
        self.transformation_engine = TransformationEngine(self.config)
        self.validation_engine = ValidationEngine(self.config)
        self.selection_engine = SelectionEngine(self.config)
        self.monitoring_engine = MonitoringEngine(self.config)
        
        # Data containers
        self.features_df = None
        self.target_df = None
        self.merged_data = None
        self.baseline_performance = None
        self.selected_features = []
        self.feature_performance_history = {}
        
        # Create output directories
        create_output_directories(self.config['output']['results_dir'])
        
        self.logger.info("LOFEP Pipeline initialized successfully")

    def _clean_feature_name(self, name: str) -> str:
        """Clean feature names for LightGBM compatibility - remove ALL special characters"""
        import re

        # Convert to string and handle any encoding issues
        cleaned = str(name)

        # Replace common problematic characters with meaningful substitutions
        replacements = {
            ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_',
            '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_',
            '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
            '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
            '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
            '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
        }

        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)

        # Remove any remaining special characters (keep only alphanumeric and underscore)
        cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)

        # Remove consecutive underscores
        cleaned = re.sub(r'_+', '_', cleaned)

        # Remove leading/trailing underscores
        cleaned = cleaned.strip('_')

        # Ensure it starts with letter or underscore (not digit)
        if cleaned and cleaned[0].isdigit():
            cleaned = 'F_' + cleaned

        # Ensure it's not empty
        if not cleaned:
            cleaned = 'Feature_Unknown'

        return cleaned

    def _log_memory_usage(self, operation: str):
        """Log current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.logger.info(f"Memory usage during {operation}: {memory_mb:.1f} MB")
        except ImportError:
            # psutil not available, skip memory logging
            pass

    def _load_config(self, config_path):
        """Load configuration from YAML file"""
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config
    
    def load_data(self):
        """Load and prepare data for feature engineering"""
        self.logger.info("Loading data...")
        
        # Load datasets
        features_path = self.config['data']['features_file']
        target_path = self.config['data']['target_file']
        returns_path = self.config['data']['returns_file']

        self.features_df = pd.read_csv(features_path)
        self.target_df = pd.read_csv(target_path)
        self.returns_df = pd.read_csv(returns_path)

        self.logger.info(f"Features loaded: {self.features_df.shape}")
        self.logger.info(f"Target loaded: {self.target_df.shape}")
        self.logger.info(f"Returns loaded: {self.returns_df.shape}")

        # Standardize column names
        for df in [self.target_df, self.features_df, self.returns_df]:
            if 'P123 ID' in df.columns:
                df.rename(columns={'P123 ID': 'P123_ID'}, inplace=True)

        # Merge datasets
        merge_cols = [self.config['data']['date_column'], self.config['data']['id_column']]
        target_cols = merge_cols + [self.config['data']['target_column']]
        returns_cols = merge_cols + [self.config['data']['returns_column']]

        # First merge features with target (for prediction)
        self.merged_data = self.features_df.merge(
            self.target_df[target_cols],
            on=merge_cols,
            how='inner'
        )

        # Then merge with returns (for performance calculation)
        self.merged_data = self.merged_data.merge(
            self.returns_df[returns_cols],
            on=merge_cols,
            how='inner'
        )
        
        # Convert date column to datetime
        self.merged_data[self.config['data']['date_column']] = pd.to_datetime(
            self.merged_data[self.config['data']['date_column']]
        )
        
        # Sort by date
        self.merged_data = self.merged_data.sort_values(self.config['data']['date_column'])
        
        self.logger.info(f"Merged data shape: {self.merged_data.shape}")
        self.logger.info(f"Date range: {self.merged_data[self.config['data']['date_column']].min()} to {self.merged_data[self.config['data']['date_column']].max()}")
        
        return self.merged_data
    
    def establish_baseline(self):
        """Establish baseline LightGBM performance"""
        self.logger.info("Establishing baseline performance...")
        
        # Get original features (exclude metadata columns)
        exclude_cols = [
            self.config['data']['date_column'],
            self.config['data']['id_column'],
            self.config['data']['ticker_column'],
            self.config['data']['target_column'],
            self.config['data']['returns_column']  # CRITICAL: exclude future returns from features!
        ]
        
        feature_cols = [col for col in self.merged_data.columns
                       if col not in exclude_cols and
                       self.merged_data[col].dtype in ['int64', 'float64']]

        # Clean feature names for LightGBM compatibility
        X = self.merged_data[feature_cols].copy()

        # Robust data cleaning
        print(f"Original features: {len(feature_cols)}")

        # Remove columns with too many missing values (>50%)
        missing_pct = X.isnull().sum() / len(X)
        good_cols = missing_pct[missing_pct <= 0.5].index.tolist()
        X = X[good_cols]
        print(f"After removing high-missing columns: {len(good_cols)}")

        # Fill remaining missing values with median
        X = X.fillna(X.median())

        # Remove infinite values
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(0)

        # Remove constant columns
        constant_cols = X.columns[X.nunique() <= 1].tolist()
        X = X.drop(columns=constant_cols)
        print(f"After removing constant columns: {len(X.columns)}")

        # Clean column names
        X.columns = [self._clean_feature_name(col) for col in X.columns]

        # Final validation
        assert not X.isnull().any().any(), "Still have NaN values!"
        assert not np.isinf(X.values).any(), "Still have infinite values!"

        print(f"Final clean features: {len(X.columns)}")
        y = self.merged_data[self.config['data']['target_column']]
        dates = self.merged_data[self.config['data']['date_column']]
        returns = self.merged_data[self.config['data']['returns_column']]

        # Run baseline validation
        self.baseline_performance = self.validation_engine.time_series_cross_validation(
            X, y, dates, feature_name="BASELINE", returns=returns
        )
        
        baseline_sharpe = self.baseline_performance.get('mean_sharpe_ratio', 0)
        baseline_std = self.baseline_performance.get('std_sharpe_ratio', 0)

        self.logger.info(f"Baseline Sharpe Ratio: {baseline_sharpe:.4f} ± {baseline_std:.4f}")
        self.logger.info(f"Baseline CAGR: {self.baseline_performance.get('mean_cagr', 0):.2%}")
        self.logger.info(f"Baseline Max Drawdown: {self.baseline_performance.get('mean_max_drawdown', 0):.2%}")
        
        return self.baseline_performance
    
    def generate_features(self):
        """Generate all feature transformations"""
        self.logger.info("Starting feature generation...")
        
        # Get base features (use the cleaned feature names from baseline)
        exclude_cols = [
            self.config['data']['date_column'],
            self.config['data']['id_column'],
            self.config['data']['ticker_column'],
            self.config['data']['target_column'],
            self.config['data']['returns_column']  # CRITICAL: exclude future returns from features!
        ]

        # Get original feature columns
        original_feature_cols = [col for col in self.merged_data.columns
                               if col not in exclude_cols and
                               self.merged_data[col].dtype in ['int64', 'float64']]

        # Apply same cleaning as in baseline
        X_temp = self.merged_data[original_feature_cols].copy()

        # Remove columns with too many missing values (>50%)
        missing_pct = X_temp.isnull().sum() / len(X_temp)
        good_cols = missing_pct[missing_pct <= 0.5].index.tolist()
        X_temp = X_temp[good_cols]

        # Remove constant columns
        constant_cols = X_temp.columns[X_temp.nunique() <= 1].tolist()
        good_cols = [col for col in good_cols if col not in constant_cols]

        # These are our base features for transformation
        base_features = good_cols
        
        # Generate features by category
        generated_features = {}
        
        # Phase 1: Mathematical Transformations
        if self.config['feature_generation']['mathematical']['enabled']:
            self.logger.info("Generating mathematical transformations...")
            math_features = self.transformation_engine.generate_mathematical_features(
                self.merged_data, base_features
            )
            generated_features.update(math_features)
            self.logger.info(f"Generated {len(math_features)} mathematical features")
        
        # Phase 2: Cross-sectional Features
        if self.config['feature_generation']['cross_sectional']['enabled']:
            self.logger.info("Generating cross-sectional features...")
            cross_features = self.transformation_engine.generate_cross_sectional_features(
                self.merged_data, base_features
            )
            generated_features.update(cross_features)
            self.logger.info(f"Generated {len(cross_features)} cross-sectional features")
        
        # Phase 3: Temporal Features
        if self.config['feature_generation']['temporal']['enabled']:
            self.logger.info("Generating temporal features...")
            temporal_features = self.transformation_engine.generate_temporal_features(
                self.merged_data, base_features
            )
            generated_features.update(temporal_features)
            self.logger.info(f"Generated {len(temporal_features)} temporal features")
        
        # Phase 4: Ratio Features
        if self.config['feature_generation']['ratio']['enabled']:
            self.logger.info("Generating ratio features...")
            ratio_features = self.transformation_engine.generate_ratio_features(
                self.merged_data, base_features
            )
            generated_features.update(ratio_features)
            self.logger.info(f"Generated {len(ratio_features)} ratio features")
        
        # Add generated features to merged_data
        for feature_name, feature_values in generated_features.items():
            # Clean feature name and values
            clean_name = self._clean_feature_name(feature_name)
            clean_values = feature_values.replace([np.inf, -np.inf], np.nan).fillna(0)
            self.merged_data[clean_name] = clean_values

        total_generated = len(generated_features)
        self.logger.info(f"Total features generated: {total_generated}")
        self.logger.info(f"Total features available: {len(self.merged_data.columns) - len(exclude_cols)}")

        # Final data validation (memory-efficient)
        feature_cols = [col for col in self.merged_data.columns
                       if col not in exclude_cols and
                       self.merged_data[col].dtype in ['int64', 'float64']]

        self.logger.info(f"Performing memory-efficient data validation on {len(feature_cols)} features...")

        # Check for NaN values in chunks to avoid memory issues
        chunk_size = 100
        nan_cols = []

        for i in range(0, len(feature_cols), chunk_size):
            chunk_cols = feature_cols[i:i+chunk_size]
            chunk_data = self.merged_data[chunk_cols]

            # Check for NaN in this chunk
            chunk_nan_cols = chunk_data.columns[chunk_data.isnull().any()].tolist()
            nan_cols.extend(chunk_nan_cols)

            # Clean up chunk to free memory
            del chunk_data

        if nan_cols:
            self.logger.warning(f"Found NaN values in {len(nan_cols)} columns, filling with 0")
            for col in nan_cols:
                self.merged_data[col] = self.merged_data[col].fillna(0)
        else:
            self.logger.info("✓ No NaN values found in feature data")

        return generated_features
    
    def select_features(self):
        """Select the best features using validation-driven approach"""
        self.logger.info("Starting feature selection...")

        try:
            # Get all available features
            exclude_cols = [
                self.config['data']['date_column'],
                self.config['data']['id_column'],
                self.config['data']['ticker_column'],
                self.config['data']['target_column'],
                self.config['data']['returns_column']  # CRITICAL: exclude future returns from features!
            ]

            self.logger.info(f"Excluding columns: {exclude_cols}")

            all_features = [col for col in self.merged_data.columns
                           if col not in exclude_cols and
                           self.merged_data[col].dtype in ['int64', 'float64']]

            self.logger.info(f"Total features available for selection: {len(all_features)}")
            self.logger.info(f"Merged data shape: {self.merged_data.shape}")

            # Check for memory issues
            memory_mb = self.merged_data.memory_usage(deep=True).sum() / 1024**2
            self.logger.info(f"Current dataset memory usage: {memory_mb:.1f} MB")

            # Memory-efficient feature selection - don't create full feature matrix
            self.logger.info("Starting memory-efficient feature selection...")

            y = self.merged_data[self.config['data']['target_column']]
            dates = self.merged_data[self.config['data']['date_column']]
            returns = self.merged_data[self.config['data']['returns_column']]

            self.logger.info(f"Target shape: {y.shape}")
            self.logger.info(f"Dates shape: {dates.shape}")

            # Use memory-efficient feature selection that doesn't require full matrix
            self.logger.info("Starting memory-efficient incremental feature selection...")
            self.selected_features, self.feature_performance_history = self.selection_engine.memory_efficient_feature_selection(
                self.merged_data, all_features, y, dates, returns, self.baseline_performance
            )

        except Exception as e:
            self.logger.error(f"Error in feature selection setup: {str(e)}")
            import traceback
            self.logger.error(f"Feature selection setup traceback: {traceback.format_exc()}")
            raise
        
        self.logger.info(f"Selected {len(self.selected_features)} features out of {len(all_features)} candidates")
        
        # Log top selected features
        if self.selected_features:
            self.logger.info("Top 10 selected features:")
            for i, feature in enumerate(self.selected_features[:10]):
                performance = self.feature_performance_history.get(feature, {})
                sharpe_improvement = performance.get('sharpe_improvement', 0)
                self.logger.info(f"  {i+1:2d}. {feature}: +{sharpe_improvement:.4f} Sharpe")
        
        return self.selected_features
    
    def validate_final_model(self):
        """Validate the final model with selected features"""
        self.logger.info("Validating final model...")
        
        if not self.selected_features:
            self.logger.warning("No features selected, using baseline model")
            return self.baseline_performance
        
        # Prepare final feature set
        X_final = self.merged_data[self.selected_features].fillna(0)
        y = self.merged_data[self.config['data']['target_column']]
        dates = self.merged_data[self.config['data']['date_column']]
        returns = self.merged_data[self.config['data']['returns_column']]

        # Run final validation
        final_performance = self.validation_engine.time_series_cross_validation(
            X_final, y, dates, feature_name="FINAL_MODEL", returns=returns
        )
        
        # Calculate improvements
        baseline_sharpe = self.baseline_performance.get('mean_sharpe_ratio', 0)
        final_sharpe = final_performance.get('mean_sharpe_ratio', 0)
        baseline_cagr = self.baseline_performance.get('mean_cagr', 0)
        final_cagr = final_performance.get('mean_cagr', 0)

        sharpe_improvement = final_sharpe - baseline_sharpe
        cagr_improvement = final_cagr - baseline_cagr

        self.logger.info("="*60)
        self.logger.info("FINAL RESULTS")
        self.logger.info("="*60)
        self.logger.info(f"Baseline Sharpe:     {baseline_sharpe:.4f}")
        self.logger.info(f"Final Sharpe:        {final_sharpe:.4f}")
        improvement_pct = f"{sharpe_improvement/baseline_sharpe:.1%}" if baseline_sharpe > 0 else "N/A"
        self.logger.info(f"Sharpe Improvement:  +{sharpe_improvement:.4f} ({improvement_pct})")
        self.logger.info(f"")
        self.logger.info(f"Baseline CAGR:       {baseline_cagr:.2%}")
        self.logger.info(f"Final CAGR:          {final_cagr:.2%}")
        self.logger.info(f"CAGR Improvement:    +{cagr_improvement:.2%}")
        self.logger.info(f"")
        self.logger.info(f"Features Selected:   {len(self.selected_features)}")
        self.logger.info("="*60)
        
        return final_performance
    
    def save_results(self):
        """Save all results and artifacts"""
        if not self.config['output']['save_results']:
            return
        
        self.logger.info("Saving results...")
        
        results_dir = Path(self.config['output']['results_dir'])
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save selected features
        if self.config['output']['save_features'] and self.selected_features:
            features_df = pd.DataFrame({
                'feature_name': self.selected_features,
                'rank': range(1, len(self.selected_features) + 1)
            })
            
            # Add performance metrics if available
            for feature in self.selected_features:
                if feature in self.feature_performance_history:
                    perf = self.feature_performance_history[feature]
                    features_df.loc[features_df['feature_name'] == feature, 'sharpe_improvement'] = perf.get('sharpe_improvement', 0)
                    features_df.loc[features_df['feature_name'] == feature, 'stability_score'] = perf.get('stability_score', 0)
            
            features_path = results_dir / f"selected_features_{timestamp}.csv"
            features_df.to_csv(features_path, index=False)
            self.logger.info(f"Selected features saved to: {features_path}")
        
        # Save performance history
        if self.feature_performance_history:
            perf_df = pd.DataFrame.from_dict(self.feature_performance_history, orient='index')
            perf_path = results_dir / f"feature_performance_history_{timestamp}.csv"
            perf_df.to_csv(perf_path)
            self.logger.info(f"Performance history saved to: {perf_path}")
        
        # Save enhanced dataset
        if self.config['output']['save_features']:
            enhanced_path = results_dir / f"enhanced_dataset_{timestamp}.csv"
            self.merged_data.to_csv(enhanced_path, index=False)
            self.logger.info(f"Enhanced dataset saved to: {enhanced_path}")
    
    def run_full_pipeline(self):
        """Run the complete feature engineering pipeline"""
        self.logger.info("="*60)
        self.logger.info("STARTING LOFEP FULL PIPELINE")
        self.logger.info("="*60)

        try:
            # Step 1: Load data
            global current_operation
            current_operation = "data_loading"
            self.logger.info("Step 1: Loading data...")
            self._log_memory_usage("before_data_loading")
            self.load_data()
            self._log_memory_usage("after_data_loading")
            self.logger.info("✓ Data loading completed successfully")

            # Step 2: Establish baseline
            current_operation = "baseline_establishment"
            self.logger.info("Step 2: Establishing baseline...")
            self.establish_baseline()
            self.logger.info("✓ Baseline establishment completed successfully")

            # Step 3: Generate features
            current_operation = "feature_generation"
            self.logger.info("Step 3: Generating features...")
            self.generate_features()
            self.logger.info("✓ Feature generation completed successfully")

            # Step 4: Select features
            current_operation = "feature_selection"
            self.logger.info("Step 4: Starting feature selection...")
            self._log_memory_usage("before_feature_selection")
            try:
                self.select_features()
                self._log_memory_usage("after_feature_selection")
                self.logger.info("✓ Feature selection completed successfully")
            except Exception as e:
                self.logger.error(f"Feature selection failed: {str(e)}")
                self._log_memory_usage("during_feature_selection_error")
                import traceback
                self.logger.error(f"Feature selection traceback: {traceback.format_exc()}")
                raise

            # Step 5: Final validation
            current_operation = "final_validation"
            self.logger.info("Step 5: Running final validation...")
            try:
                final_performance = self.validate_final_model()
                self.logger.info("✓ Final validation completed successfully")
            except Exception as e:
                self.logger.error(f"Final validation failed: {str(e)}")
                import traceback
                self.logger.error(f"Final validation traceback: {traceback.format_exc()}")
                raise

            # Step 6: Save results
            current_operation = "results_saving"
            self.logger.info("Step 6: Saving results...")
            try:
                self.save_results()
                self.logger.info("✓ Results saving completed successfully")
            except Exception as e:
                self.logger.error(f"Results saving failed: {str(e)}")
                import traceback
                self.logger.error(f"Results saving traceback: {traceback.format_exc()}")
                # Don't raise here, as we still want to return results

            self.logger.info("LOFEP pipeline completed successfully!")
            return final_performance

        except Exception as e:
            self.logger.error(f"Pipeline failed with error: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            raise


def main():
    """Main execution function"""
    print("🚀 LightGBM-Optimized Feature Engineering Pipeline (LOFEP)")
    print("="*70)
    
    # Initialize and run pipeline
    pipeline = LightGBMOptimizedFeatureEngineeringPipeline()
    results = pipeline.run_full_pipeline()
    
    print("\n🎉 Pipeline completed successfully!")
    print(f"📊 Final Sharpe Ratio: {results.get('mean_sharpe_ratio', 0):.4f}")
    print(f"📈 Final CAGR: {results.get('mean_cagr', 0):.2%}")


if __name__ == "__main__":
    main()
