#!/usr/bin/env python3
"""
Analyze why many features have zero SHAP importance
"""

import pandas as pd
import numpy as np

def analyze_zero_importance():
    print("="*60)
    print("🔍 ANALYZING ZERO IMPORTANCE FEATURES")
    print("="*60)
    
    # Load the feature importance results
    importance_df = pd.read_csv('comprehensive_feature_importance.csv')
    
    print(f"Total features: {len(importance_df)}")
    
    # Analyze distribution
    zero_features = importance_df[importance_df['avg_importance'] == 0]
    nonzero_features = importance_df[importance_df['avg_importance'] > 0]
    
    print(f"Features with zero importance: {len(zero_features)}")
    print(f"Features with non-zero importance: {len(nonzero_features)}")
    print(f"Percentage with zero importance: {len(zero_features)/len(importance_df)*100:.1f}%")
    
    # Check the boundary
    print(f"\nLast non-zero feature:")
    last_nonzero = nonzero_features.tail(1)
    print(f"  Rank {last_nonzero['rank'].iloc[0]}: {last_nonzero['feature'].iloc[0]} = {last_nonzero['avg_importance'].iloc[0]:.10f}")
    
    print(f"\nFirst zero feature:")
    first_zero = zero_features.head(1)
    print(f"  Rank {first_zero['rank'].iloc[0]}: {first_zero['feature'].iloc[0]} = {first_zero['avg_importance'].iloc[0]}")
    
    # Look at very small importance values
    print(f"\nFeatures with very small importance (< 1e-8):")
    small_features = importance_df[(importance_df['avg_importance'] > 0) & (importance_df['avg_importance'] < 1e-8)]
    print(f"Count: {len(small_features)}")
    if len(small_features) > 0:
        for _, row in small_features.tail(5).iterrows():
            print(f"  {row['feature']}: {row['avg_importance']:.2e}")
    
    # Check if this is related to LightGBM feature selection
    print(f"\n📊 POSSIBLE EXPLANATIONS:")
    print("1. LightGBM automatic feature selection - unused features get 0 importance")
    print("2. Early stopping - model doesn't use all features before stopping")
    print("3. Feature redundancy - correlated features may not be used")
    print("4. SHAP calculation precision - very small values rounded to 0")
    
    # Load original data to check feature characteristics
    print(f"\n🔍 CHECKING FEATURE CHARACTERISTICS...")
    
    try:
        # Load a sample of the original data
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv', nrows=1000)
        
        # Clean feature names to match
        import re
        name_mapping = {}
        for col in features_df.columns:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')
            name_mapping[col] = clean_name
        features_df = features_df.rename(columns=name_mapping)
        
        # Check some zero-importance features
        zero_feature_names = zero_features['feature'].head(10).tolist()
        available_zero_features = [f for f in zero_feature_names if f in features_df.columns]
        
        if len(available_zero_features) > 0:
            print(f"\nChecking {len(available_zero_features)} zero-importance features in data:")
            
            for feature in available_zero_features[:5]:
                if feature in features_df.columns:
                    values = features_df[feature]
                    print(f"\n{feature}:")
                    print(f"  Non-null values: {values.notna().sum()}/{len(values)}")
                    print(f"  Unique values: {values.nunique()}")
                    print(f"  Range: [{values.min():.3f}, {values.max():.3f}]")
                    print(f"  Std: {values.std():.6f}")
                    
                    # Check if feature is constant
                    if values.nunique() <= 1:
                        print(f"  ⚠️ CONSTANT FEATURE - this explains zero importance!")
                    elif values.std() < 1e-10:
                        print(f"  ⚠️ NEAR-CONSTANT FEATURE - very low variance!")
        
    except Exception as e:
        print(f"Could not load original data: {e}")
    
    # Check distribution of non-zero importances
    print(f"\n📈 NON-ZERO IMPORTANCE DISTRIBUTION:")
    nonzero_values = nonzero_features['avg_importance']
    print(f"Min: {nonzero_values.min():.2e}")
    print(f"Max: {nonzero_values.max():.2e}")
    print(f"Median: {nonzero_values.median():.2e}")
    print(f"Mean: {nonzero_values.mean():.2e}")
    
    # Check percentiles
    percentiles = [99, 95, 90, 75, 50, 25, 10, 5, 1]
    print(f"\nPercentiles of non-zero importance:")
    for p in percentiles:
        val = np.percentile(nonzero_values, p)
        print(f"  {p:2d}th percentile: {val:.2e}")

if __name__ == "__main__":
    analyze_zero_importance()
