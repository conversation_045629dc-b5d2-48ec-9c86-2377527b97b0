#!/usr/bin/env python3
"""
Final validation with regenerated top LOFEP features
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
import json
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility"""
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', str(name))
    cleaned = re.sub(r'_+', '_', cleaned).strip('_')
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    return cleaned or 'Feature_Unknown'

def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3):
    """Create time-based train/validation splits"""
    splits = []
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    current_date = min_date
    while current_date < max_date:
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        current_date = val_start
    
    return splits

def run_final_validation_with_regenerated_features():
    """Run final validation with the regenerated top features"""
    
    print("🚀 FINAL VALIDATION WITH REGENERATED TOP FEATURES")
    print("="*70)
    
    # Load the enhanced dataset with regenerated features
    print("Loading enhanced dataset with regenerated features...")
    enhanced_data = pd.read_csv('enhanced_dataset_top50_features.csv')
    
    print(f"Enhanced dataset: {enhanced_data.shape}")
    
    # Load LOFEP configuration
    with open('lofep_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Identify all features (exclude metadata and target columns)
    exclude_cols = [
        'Date', 'P123 ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn', 'Market_Cap_Quintile'
    ]
    
    all_features = [col for col in enhanced_data.columns 
                   if col not in exclude_cols and 
                   enhanced_data[col].dtype in ['int64', 'float64']]
    
    print(f"Total features available: {len(all_features)}")
    
    # Identify which are the newly regenerated features
    original_features = []
    regenerated_features = []
    
    # Load original dataset to identify baseline features
    original_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    original_feature_names = set(original_df.columns)
    
    for feature in all_features:
        if feature in original_feature_names:
            original_features.append(feature)
        else:
            regenerated_features.append(feature)
    
    print(f"Original features: {len(original_features)}")
    print(f"Regenerated LOFEP features: {len(regenerated_features)}")
    
    print("\nTop 10 regenerated features:")
    for i, feat in enumerate(regenerated_features[:10]):
        print(f"  {i+1:2d}. {feat}")
    
    # Clean feature names for LightGBM
    feature_mapping = {}
    for feature in all_features:
        cleaned_name = clean_feature_name(feature)
        feature_mapping[feature] = cleaned_name
    
    # Prepare data
    print("\nPreparing data for validation...")
    X = enhanced_data[all_features].copy()
    X.columns = [feature_mapping[col] for col in X.columns]
    X = X.fillna(0)
    X = X.replace([np.inf, -np.inf], 0)
    
    y = enhanced_data['Weighted_MixRel']
    dates = pd.to_datetime(enhanced_data['Date'])
    returns = enhanced_data['Future5DReturn']
    
    print(f"Final validation dataset: {X.shape}")
    print(f"Date range: {dates.min()} to {dates.max()}")
    
    # Run time series cross-validation
    print("\nRunning time series cross-validation...")
    
    splits = create_time_splits(dates, 
                               config['validation']['train_months'], 
                               config['validation']['validation_months'])
    
    print(f"Created {len(splits)} validation splits")
    
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
        try:
            # Create masks
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Prepare fold data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            
            # Remove NaN targets
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()
            
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            # Train LightGBM model
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate portfolio performance
            val_returns_data = returns[val_mask][val_valid_mask]
            val_dates_data = dates[val_mask][val_valid_mask]
            
            # Portfolio construction: top 15 equal-weighted stocks
            portfolio_returns = []
            
            for date in sorted(val_dates_data.unique()):
                date_mask = val_dates_data == date
                date_predictions = y_pred[date_mask]
                date_returns = val_returns_data[date_mask]
                
                if len(date_predictions) >= 15:
                    # Select top 15 stocks by prediction
                    top_indices = np.argsort(date_predictions)[-15:]
                    top_returns = date_returns.iloc[top_indices]
                    
                    # Equal-weighted portfolio return (convert percentage to decimal)
                    portfolio_return = top_returns.mean() / 100.0
                    portfolio_returns.append(portfolio_return)
            
            if len(portfolio_returns) > 0:
                portfolio_returns = np.array(portfolio_returns)
                
                # Calculate metrics
                mean_return = portfolio_returns.mean()
                volatility = portfolio_returns.std()
                
                # Annualize (weekly rebalancing, 52 periods per year)
                annual_return = mean_return * 52
                annual_volatility = volatility * np.sqrt(52)
                sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
                
                # CAGR
                cumulative_return = np.prod(1 + portfolio_returns) - 1
                n_years = len(portfolio_returns) / 52
                cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 and cumulative_return > -0.99 else 0
                
                fold_results.append({
                    'fold': fold_idx,
                    'sharpe': sharpe,
                    'cagr': cagr,
                    'annual_return': annual_return,
                    'annual_volatility': annual_volatility,
                    'periods': len(portfolio_returns)
                })
                
                print(f"Fold {fold_idx:2d} ({val_start.strftime('%Y-%m')}-{val_end.strftime('%Y-%m')}): "
                      f"Sharpe = {sharpe:6.3f}, CAGR = {cagr:6.1%}, Periods = {len(portfolio_returns)}")
        
        except Exception as e:
            print(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Calculate final results
    if fold_results:
        sharpe_values = [f['sharpe'] for f in fold_results]
        cagr_values = [f['cagr'] for f in fold_results]
        
        mean_sharpe = np.mean(sharpe_values)
        std_sharpe = np.std(sharpe_values)
        mean_cagr = np.mean(cagr_values)
        
        print(f"\n🎉 FINAL VALIDATION RESULTS WITH REGENERATED FEATURES:")
        print(f"="*70)
        print(f"Mean Sharpe Ratio: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Mean CAGR: {mean_cagr:.2%}")
        print(f"Number of folds: {len(fold_results)}")
        print(f"Min Sharpe: {np.min(sharpe_values):.4f}")
        print(f"Max Sharpe: {np.max(sharpe_values):.4f}")
        print(f"Total features used: {len(all_features)}")
        print(f"Original features: {len(original_features)}")
        print(f"Regenerated LOFEP features: {len(regenerated_features)}")
        
        # Compare to previous results
        baseline_sharpe = 1.7765  # Original LOFEP baseline
        previous_result = 2.2098  # Previous validation with 20 features
        improvement_vs_baseline = mean_sharpe - baseline_sharpe
        improvement_vs_previous = mean_sharpe - previous_result
        
        print(f"\nPerformance Comparison:")
        print(f"LOFEP Baseline: {baseline_sharpe:.4f}")
        print(f"Previous (20 features): {previous_result:.4f}")
        print(f"Current (37 regenerated): {mean_sharpe:.4f}")
        print(f"Improvement vs Baseline: {improvement_vs_baseline:+.4f} ({improvement_vs_baseline/baseline_sharpe:+.1%})")
        print(f"Improvement vs Previous: {improvement_vs_previous:+.4f} ({improvement_vs_previous/previous_result:+.1%})")
        
        # Save results
        results = {
            'final_sharpe_mean': mean_sharpe,
            'final_sharpe_std': std_sharpe,
            'final_cagr_mean': mean_cagr,
            'baseline_sharpe': baseline_sharpe,
            'previous_sharpe': previous_result,
            'improvement_vs_baseline': improvement_vs_baseline,
            'improvement_vs_previous': improvement_vs_previous,
            'num_folds': len(fold_results),
            'total_features': len(all_features),
            'original_features': len(original_features),
            'regenerated_features': len(regenerated_features),
            'regenerated_feature_list': regenerated_features,
            'fold_results': fold_results,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('lofep_results/final_validation_with_regenerated_features.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to: lofep_results/final_validation_with_regenerated_features.json")
        
        if improvement_vs_previous > 0.1:
            print("🎉 Regenerated features significantly improved performance!")
        elif improvement_vs_previous > 0:
            print("✅ Regenerated features provided additional improvement")
        else:
            print("⚠️  Regenerated features did not improve over previous result")
        
    else:
        print("❌ No successful validation folds")

if __name__ == "__main__":
    run_final_validation_with_regenerated_features()
