#!/usr/bin/env python3
"""
LOFEP Monitoring Engine
Continuous performance tracking and drift detection
"""

import pandas as pd
import numpy as np
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from lofep_utils import calculate_feature_stability, detect_feature_drift


class MonitoringEngine:
    """
    Engine for monitoring feature performance and detecting drift
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger('LOFEP.MonitoringEngine')
        
        # Monitoring configuration
        self.monitoring_config = config.get('monitoring', {})
        self.enabled = self.monitoring_config.get('enabled', True)
        
        # Storage for monitoring data
        self.performance_history = {}
        self.feature_importance_history = {}
        self.drift_alerts = []
        
        # File paths for persistent storage
        self.results_dir = Path(config['output']['results_dir'])
        self.monitoring_dir = self.results_dir / 'monitoring'
        self.monitoring_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("MonitoringEngine initialized")
    
    def track_feature_performance(self, feature_name: str, performance_metrics: Dict[str, float],
                                timestamp: Optional[datetime] = None) -> None:
        """Track performance metrics for a feature over time"""
        
        if not self.enabled:
            return
        
        if timestamp is None:
            timestamp = datetime.now()
        
        # Initialize feature history if not exists
        if feature_name not in self.performance_history:
            self.performance_history[feature_name] = []
        
        # Add performance record
        performance_record = {
            'timestamp': timestamp.isoformat(),
            'metrics': performance_metrics.copy()
        }
        
        self.performance_history[feature_name].append(performance_record)
        
        # Save to file
        self._save_performance_history()
        
        self.logger.debug(f"Tracked performance for {feature_name}")
    
    def track_feature_importance(self, feature_importance_dict: Dict[str, float],
                               model_name: str = "default", timestamp: Optional[datetime] = None) -> None:
        """Track feature importance from LightGBM model"""
        
        if not self.enabled:
            return
        
        if timestamp is None:
            timestamp = datetime.now()
        
        # Initialize model history if not exists
        if model_name not in self.feature_importance_history:
            self.feature_importance_history[model_name] = []
        
        # Add importance record
        importance_record = {
            'timestamp': timestamp.isoformat(),
            'feature_importance': feature_importance_dict.copy()
        }
        
        self.feature_importance_history[model_name].append(importance_record)
        
        # Check for drift
        self._check_importance_drift(feature_importance_dict, model_name)
        
        # Save to file
        self._save_importance_history()
        
        self.logger.debug(f"Tracked feature importance for {model_name}")
    
    def detect_performance_drift(self, feature_name: str, current_performance: Dict[str, float],
                               lookback_periods: int = 5) -> Dict[str, Any]:
        """Detect if feature performance has drifted significantly"""
        
        drift_result = {
            'feature_name': feature_name,
            'drift_detected': False,
            'drift_magnitude': 0.0,
            'drift_direction': 'stable',
            'confidence': 0.0,
            'recommendation': 'continue'
        }
        
        if feature_name not in self.performance_history:
            return drift_result
        
        history = self.performance_history[feature_name]
        
        if len(history) < lookback_periods + 1:
            return drift_result
        
        # Get recent performance history
        recent_history = history[-lookback_periods:]
        current_sharpe = current_performance.get('sharpe_ratio', 0)
        
        # Calculate historical average
        historical_sharpes = [record['metrics'].get('sharpe_ratio', 0) for record in recent_history]
        historical_avg = np.mean(historical_sharpes)
        historical_std = np.std(historical_sharpes)
        
        if historical_std == 0:
            return drift_result
        
        # Calculate drift magnitude (z-score)
        drift_magnitude = abs(current_sharpe - historical_avg) / historical_std
        
        # Determine if drift is significant
        drift_threshold = self.monitoring_config.get('alert_thresholds', {}).get('performance_drop', 0.1)
        
        if drift_magnitude > 2.0:  # 2 standard deviations
            drift_result['drift_detected'] = True
            drift_result['drift_magnitude'] = drift_magnitude
            drift_result['confidence'] = min(0.95, drift_magnitude / 3.0)
            
            if current_sharpe < historical_avg:
                drift_result['drift_direction'] = 'declining'
                drift_result['recommendation'] = 'investigate' if drift_magnitude > 3.0 else 'monitor'
            else:
                drift_result['drift_direction'] = 'improving'
                drift_result['recommendation'] = 'continue'
        
        return drift_result
    
    def _check_importance_drift(self, current_importance: Dict[str, float], model_name: str) -> None:
        """Check for drift in feature importance"""
        
        if model_name not in self.feature_importance_history:
            return
        
        history = self.feature_importance_history[model_name]
        
        if len(history) < 3:  # Need at least 3 historical records
            return
        
        # Get historical importance for comparison
        recent_history = history[-3:]  # Last 3 records
        
        drift_threshold = self.monitoring_config.get('alert_thresholds', {}).get('feature_drift', 0.3)
        
        for feature_name, current_imp in current_importance.items():
            # Get historical importance for this feature
            historical_importance = []
            for record in recent_history:
                if feature_name in record['feature_importance']:
                    historical_importance.append(record['feature_importance'][feature_name])
            
            if len(historical_importance) >= 2:
                # Check for drift
                if detect_feature_drift(current_imp, historical_importance, drift_threshold):
                    alert = {
                        'timestamp': datetime.now().isoformat(),
                        'type': 'feature_importance_drift',
                        'feature_name': feature_name,
                        'model_name': model_name,
                        'current_importance': current_imp,
                        'historical_avg': np.mean(historical_importance),
                        'drift_magnitude': abs(current_imp - np.mean(historical_importance)) / np.mean(historical_importance) if np.mean(historical_importance) > 0 else 0
                    }
                    
                    self.drift_alerts.append(alert)
                    self.logger.warning(f"Feature importance drift detected for {feature_name} in {model_name}")
    
    def generate_monitoring_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'features_tracked': len(self.performance_history),
                'models_tracked': len(self.feature_importance_history),
                'drift_alerts': len(self.drift_alerts),
                'monitoring_period_days': self._calculate_monitoring_period()
            },
            'feature_performance': {},
            'feature_importance_trends': {},
            'drift_alerts': self.drift_alerts.copy(),
            'recommendations': []
        }
        
        # Analyze feature performance trends
        for feature_name, history in self.performance_history.items():
            if len(history) >= 2:
                recent_performance = history[-1]['metrics']
                trend_analysis = self._analyze_performance_trend(history)
                
                report['feature_performance'][feature_name] = {
                    'latest_sharpe': recent_performance.get('sharpe_ratio', 0),
                    'trend': trend_analysis['trend'],
                    'stability': trend_analysis['stability'],
                    'recommendation': trend_analysis['recommendation']
                }
        
        # Analyze feature importance trends
        for model_name, history in self.feature_importance_history.items():
            if len(history) >= 2:
                latest_importance = history[-1]['feature_importance']
                trend_analysis = self._analyze_importance_trends(history)
                
                report['feature_importance_trends'][model_name] = {
                    'top_features': sorted(latest_importance.items(), key=lambda x: x[1], reverse=True)[:10],
                    'trending_up': trend_analysis['trending_up'],
                    'trending_down': trend_analysis['trending_down'],
                    'stable_features': trend_analysis['stable_features']
                }
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(report)
        
        # Save report
        self._save_monitoring_report(report)
        
        return report
    
    def _analyze_performance_trend(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance trend for a feature"""
        
        if len(history) < 2:
            return {'trend': 'insufficient_data', 'stability': 0, 'recommendation': 'continue'}
        
        # Extract Sharpe ratios
        sharpe_values = [record['metrics'].get('sharpe_ratio', 0) for record in history]
        
        # Calculate trend
        if len(sharpe_values) >= 3:
            recent_avg = np.mean(sharpe_values[-3:])
            earlier_avg = np.mean(sharpe_values[:-3]) if len(sharpe_values) > 3 else sharpe_values[0]
            
            if recent_avg > earlier_avg * 1.05:
                trend = 'improving'
            elif recent_avg < earlier_avg * 0.95:
                trend = 'declining'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        # Calculate stability
        stability = calculate_feature_stability(sharpe_values)
        
        # Generate recommendation
        if trend == 'declining' and stability < 0.5:
            recommendation = 'investigate'
        elif trend == 'improving':
            recommendation = 'continue'
        else:
            recommendation = 'monitor'
        
        return {
            'trend': trend,
            'stability': stability,
            'recommendation': recommendation
        }
    
    def _analyze_importance_trends(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze feature importance trends"""
        
        if len(history) < 2:
            return {'trending_up': [], 'trending_down': [], 'stable_features': []}
        
        # Get all features across history
        all_features = set()
        for record in history:
            all_features.update(record['feature_importance'].keys())
        
        trending_up = []
        trending_down = []
        stable_features = []
        
        for feature in all_features:
            # Get importance history for this feature
            importance_values = []
            for record in history:
                if feature in record['feature_importance']:
                    importance_values.append(record['feature_importance'][feature])
                else:
                    importance_values.append(0)
            
            if len(importance_values) >= 3:
                recent_avg = np.mean(importance_values[-2:])
                earlier_avg = np.mean(importance_values[:-2])
                
                if recent_avg > earlier_avg * 1.2:
                    trending_up.append(feature)
                elif recent_avg < earlier_avg * 0.8:
                    trending_down.append(feature)
                else:
                    stable_features.append(feature)
        
        return {
            'trending_up': trending_up,
            'trending_down': trending_down,
            'stable_features': stable_features
        }
    
    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on monitoring data"""
        
        recommendations = []
        
        # Check for declining features
        declining_features = [
            name for name, perf in report['feature_performance'].items()
            if perf['trend'] == 'declining'
        ]
        
        if declining_features:
            recommendations.append(f"Investigate {len(declining_features)} declining features: {', '.join(declining_features[:3])}")
        
        # Check for drift alerts
        if len(report['drift_alerts']) > 0:
            recommendations.append(f"Address {len(report['drift_alerts'])} drift alerts")
        
        # Check for unstable features
        unstable_features = [
            name for name, perf in report['feature_performance'].items()
            if perf['stability'] < 0.3
        ]
        
        if unstable_features:
            recommendations.append(f"Consider removing {len(unstable_features)} unstable features")
        
        # Check for trending features
        for model_name, trends in report['feature_importance_trends'].items():
            if trends['trending_down']:
                recommendations.append(f"Monitor declining importance features in {model_name}: {', '.join(trends['trending_down'][:3])}")
        
        if not recommendations:
            recommendations.append("No immediate action required - all features performing well")
        
        return recommendations
    
    def _calculate_monitoring_period(self) -> int:
        """Calculate the monitoring period in days"""
        
        all_timestamps = []
        
        for history in self.performance_history.values():
            for record in history:
                all_timestamps.append(datetime.fromisoformat(record['timestamp']))
        
        for history in self.feature_importance_history.values():
            for record in history:
                all_timestamps.append(datetime.fromisoformat(record['timestamp']))
        
        if not all_timestamps:
            return 0
        
        return (max(all_timestamps) - min(all_timestamps)).days
    
    def _save_performance_history(self) -> None:
        """Save performance history to file"""
        
        file_path = self.monitoring_dir / 'performance_history.json'
        
        with open(file_path, 'w') as f:
            json.dump(self.performance_history, f, indent=2)
    
    def _save_importance_history(self) -> None:
        """Save feature importance history to file"""
        
        file_path = self.monitoring_dir / 'importance_history.json'
        
        with open(file_path, 'w') as f:
            json.dump(self.feature_importance_history, f, indent=2)
    
    def _save_monitoring_report(self, report: Dict[str, Any]) -> None:
        """Save monitoring report to file"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = self.monitoring_dir / f'monitoring_report_{timestamp}.json'
        
        with open(file_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Monitoring report saved to: {file_path}")
    
    def load_historical_data(self) -> None:
        """Load historical monitoring data from files"""
        
        # Load performance history
        perf_file = self.monitoring_dir / 'performance_history.json'
        if perf_file.exists():
            with open(perf_file, 'r') as f:
                self.performance_history = json.load(f)
        
        # Load importance history
        imp_file = self.monitoring_dir / 'importance_history.json'
        if imp_file.exists():
            with open(imp_file, 'r') as f:
                self.feature_importance_history = json.load(f)
        
        self.logger.info("Historical monitoring data loaded")
