#!/usr/bin/env python3
"""
Correct final validation using EXACT same logic as working validation code
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility"""
    cleaned = str(name)
    
    replacements = {
        ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_', 
        '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_', 
        '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
        '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
        '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
        '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)
    cleaned = re.sub(r'_+', '_', cleaned)
    cleaned = cleaned.strip('_')
    
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    
    if not cleaned:
        cleaned = 'Feature_Unknown'
    
    return cleaned

def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3):
    """Create time-based train/validation splits - EXACT same as working code"""
    splits = []
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    current_date = min_date
    while current_date < max_date:
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        current_date = val_start
    
    return splits

def calculate_portfolio_metrics(predictions, returns, dates, top_k=15):
    """Calculate portfolio metrics - EXACT same as working code"""
    portfolio_returns = []
    
    for date in sorted(dates.unique()):
        date_mask = dates == date
        date_predictions = predictions[date_mask]
        date_returns = returns[date_mask]
        
        if len(date_predictions) >= top_k:
            top_indices = np.argsort(date_predictions)[-top_k:]
            top_returns = date_returns.iloc[top_indices]
            portfolio_return = top_returns.mean() / 100.0  # CRITICAL: Convert percentage to decimal
            portfolio_returns.append(portfolio_return)
    
    if len(portfolio_returns) == 0:
        return {'sharpe': 0, 'cagr': 0, 'max_drawdown': 0}
    
    portfolio_returns = np.array(portfolio_returns)
    
    # Calculate metrics - EXACT same as working code
    mean_return = portfolio_returns.mean()
    volatility = portfolio_returns.std()
    
    # Annualize (assuming weekly rebalancing)
    annual_return = mean_return * 52
    annual_volatility = volatility * np.sqrt(52)
    sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
    
    # CAGR
    cumulative_return = np.prod(1 + portfolio_returns) - 1
    n_years = len(portfolio_returns) / 52
    cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 else 0
    
    # Max drawdown
    cumulative_wealth = np.cumprod(1 + portfolio_returns)
    running_max = np.maximum.accumulate(cumulative_wealth)
    drawdowns = (cumulative_wealth - running_max) / running_max
    max_drawdown = np.min(drawdowns)
    
    return {
        'sharpe': sharpe,
        'cagr': cagr,
        'max_drawdown': max_drawdown,
        'volatility': annual_volatility,
        'total_return': cumulative_return
    }

def run_correct_final_validation():
    """Run final validation using EXACT same logic as working validation"""
    
    print("🎯 Running CORRECT Final Validation")
    print("="*60)
    
    # Load data using EXACT same files as working code
    print("Loading data files...")
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    print(f"Features: {features_df.shape}")
    print(f"Target: {target_df.shape}")
    print(f"Returns: {returns_df.shape}")
    
    # Merge data - EXACT same as working code
    merged_data = features_df.merge(target_df, on=['Date', 'P123 ID'], how='inner')
    merged_data = merged_data.merge(returns_df, on=['Date', 'P123 ID'], how='inner')
    
    print(f"Merged data: {merged_data.shape}")
    
    # Use EXACT same column names as working code
    target_col = 'Weighted_MixRel'
    returns_col = 'Future5DReturn'
    
    print(f"Target column: {target_col}")
    print(f"Returns column: {returns_col}")
    
    # Check data
    print(f"\nTarget stats:")
    print(merged_data[target_col].describe())
    print(f"\nReturns stats:")
    print(merged_data[returns_col].describe())
    
    # Prepare features - EXACT same as working code
    exclude_cols = ['Date', 'P123 ID', 'Ticker', target_col, returns_col]
    feature_cols = [col for col in merged_data.columns 
                   if col not in exclude_cols and 
                   merged_data[col].dtype in ['int64', 'float64']]
    
    print(f"Feature columns: {len(feature_cols)}")
    
    # Clean feature names
    feature_mapping = {}
    for col in feature_cols:
        cleaned = clean_feature_name(col)
        feature_mapping[col] = cleaned
    
    # Prepare data
    X = merged_data[feature_cols].copy()
    X.columns = [feature_mapping[col] for col in X.columns]
    X = X.fillna(0)
    X = X.replace([np.inf, -np.inf], 0)
    
    y = merged_data[target_col]
    dates = pd.to_datetime(merged_data['Date'])
    returns = merged_data[returns_col]
    
    print(f"Final dataset: {X.shape}")
    print(f"Date range: {dates.min()} to {dates.max()}")
    
    # Load config
    with open('lofep_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Run validation - EXACT same as working code
    print("\nRunning time series cross-validation...")
    
    splits = create_time_splits(dates, 
                               config['validation']['train_months'], 
                               config['validation']['validation_months'])
    
    print(f"Created {len(splits)} validation splits")
    
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
        try:
            # Create masks
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Prepare fold data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            
            # Remove NaN targets
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()
            
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            # Train model - EXACT same as working code
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate portfolio performance - EXACT same as working code
            val_returns_data = returns[val_mask][val_valid_mask]
            val_dates_data = dates[val_mask][val_valid_mask]
            
            # Use the EXACT same portfolio calculation as working code
            metrics = calculate_portfolio_metrics(y_pred, val_returns_data, val_dates_data, top_k=15)
            
            fold_results.append(metrics['sharpe'])
            
            print(f"Fold {fold_idx:2d} ({val_start.strftime('%Y-%m')}-{val_end.strftime('%Y-%m')}): "
                  f"Sharpe = {metrics['sharpe']:6.3f}, CAGR = {metrics['cagr']:6.1%}")
        
        except Exception as e:
            print(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Final results
    if fold_results:
        mean_sharpe = np.mean(fold_results)
        std_sharpe = np.std(fold_results)
        
        print(f"\n🎯 BASELINE VALIDATION RESULTS:")
        print(f"="*50)
        print(f"Mean Sharpe Ratio: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Number of folds: {len(fold_results)}")
        print(f"Min Sharpe: {np.min(fold_results):.4f}")
        print(f"Max Sharpe: {np.max(fold_results):.4f}")
        print(f"Features used: {len(feature_cols)}")
        
        # Compare to LOFEP baseline
        lofep_baseline = 1.7765
        print(f"\nComparison to LOFEP baseline:")
        print(f"LOFEP baseline: {lofep_baseline:.4f}")
        print(f"This validation: {mean_sharpe:.4f}")
        print(f"Difference: {mean_sharpe - lofep_baseline:+.4f}")
        
        if abs(mean_sharpe - lofep_baseline) < 0.1:
            print("✅ Results match LOFEP baseline - validation is working correctly!")
        else:
            print("⚠️  Results differ from LOFEP baseline - may indicate data differences")
        
        # Save results
        results = {
            'baseline_sharpe_mean': mean_sharpe,
            'baseline_sharpe_std': std_sharpe,
            'num_folds': len(fold_results),
            'fold_results': fold_results,
            'features_used': len(feature_cols),
            'lofep_baseline': lofep_baseline,
            'difference_from_lofep': mean_sharpe - lofep_baseline,
            'timestamp': datetime.now().isoformat()
        }
        
        import json
        with open('lofep_results/correct_baseline_validation.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to: lofep_results/correct_baseline_validation.json")
        
    else:
        print("❌ No successful validation folds")

if __name__ == "__main__":
    run_correct_final_validation()
