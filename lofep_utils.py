#!/usr/bin/env python3
"""
LOFEP Utilities
Common utility functions for the LightGBM-Optimized Feature Engineering Pipeline
"""

import pandas as pd
import numpy as np
import logging
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings

warnings.filterwarnings('ignore')


def setup_logging(logging_config: Dict) -> logging.Logger:
    """Setup logging configuration"""
    
    # Create logger
    logger = logging.getLogger('LOFEP')
    logger.setLevel(getattr(logging, logging_config['level']))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(logging_config['format'])
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, logging_config['level']))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    if 'file' in logging_config:
        file_handler = logging.FileHandler(logging_config['file'])
        file_handler.setLevel(getattr(logging, logging_config['level']))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def create_output_directories(results_dir: str) -> None:
    """Create output directories if they don't exist"""
    
    base_dir = Path(results_dir)
    
    # Create main results directory
    base_dir.mkdir(exist_ok=True)
    
    # Create subdirectories
    subdirs = ['features', 'models', 'validation', 'reports', 'monitoring']
    for subdir in subdirs:
        (base_dir / subdir).mkdir(exist_ok=True)


def calculate_performance_metrics(returns: pd.Series, benchmark_returns: pd.Series = None) -> Dict[str, float]:
    """Calculate comprehensive performance metrics"""

    # Remove NaN values
    returns = returns.dropna()

    if len(returns) == 0:
        return {
            'total_return': 0.0,
            'cagr': 0.0,
            'volatility': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'hit_rate': 0.0,
            'skewness': 0.0,
            'kurtosis': 0.0
        }

    # Convert percentage returns to decimal (e.g., 5.0 -> 0.05)
    # Check if returns look like percentages (mean > 1 or values > 100)
    if returns.abs().mean() > 1 or (returns.abs() > 100).any():
        returns = returns / 100.0

    # Basic metrics
    total_return = (1 + returns).prod() - 1
    
    # Annualized metrics (assuming 5-day returns, ~52 periods per year)
    periods_per_year = 52  # Approximately 52 weeks per year
    n_periods = len(returns)
    years = n_periods / periods_per_year

    cagr = (1 + total_return) ** (1 / years) - 1 if years > 0 and total_return > -1 else 0
    volatility = returns.std() * np.sqrt(periods_per_year)
    
    # Sharpe ratio (assuming 0% risk-free rate)
    sharpe_ratio = (returns.mean() * periods_per_year) / volatility if volatility > 0 else 0
    
    # Maximum drawdown
    cumulative_returns = (1 + returns).cumprod()
    running_max = cumulative_returns.expanding().max()
    drawdowns = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdowns.min()
    
    # Hit rate
    hit_rate = (returns > 0).mean()
    
    # Higher moments
    skewness = returns.skew()
    kurtosis = returns.kurtosis()
    
    metrics = {
        'total_return': total_return,
        'cagr': cagr,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'hit_rate': hit_rate,
        'skewness': skewness,
        'kurtosis': kurtosis
    }
    
    # Information ratio vs benchmark
    if benchmark_returns is not None:
        benchmark_returns = benchmark_returns.dropna()
        if len(benchmark_returns) == len(returns):
            excess_returns = returns - benchmark_returns
            tracking_error = excess_returns.std() * np.sqrt(periods_per_year)
            information_ratio = (excess_returns.mean() * periods_per_year) / tracking_error if tracking_error > 0 else 0
            metrics['information_ratio'] = information_ratio
    
    return metrics


def safe_divide(numerator: pd.Series, denominator, fill_value: float = 0.0) -> pd.Series:
    """Safely divide a series by another series or scalar, handling division by zero"""

    # Handle scalar denominator
    if np.isscalar(denominator):
        if denominator == 0 or abs(denominator) < 1e-10:
            return pd.Series(fill_value, index=numerator.index)
        result = numerator / denominator
    else:
        # Handle series denominator
        safe_denominator = denominator.replace(0, np.nan)
        safe_denominator = safe_denominator.where(np.abs(safe_denominator) > 1e-10, np.nan)
        result = numerator / safe_denominator

    # Fill NaN values
    result = result.fillna(fill_value)

    # Handle infinite values
    result = result.replace([np.inf, -np.inf], fill_value)

    return result


def winsorize_series(series: pd.Series, lower_percentile: float = 0.01, upper_percentile: float = 0.99) -> pd.Series:
    """Winsorize a series to handle outliers"""
    
    if series.isna().all():
        return series
    
    lower_bound = series.quantile(lower_percentile)
    upper_bound = series.quantile(upper_percentile)
    
    return series.clip(lower_bound, upper_bound)


def standardize_series(series: pd.Series, method: str = 'zscore') -> pd.Series:
    """Standardize a series using various methods"""
    
    if series.isna().all() or series.std() == 0:
        return pd.Series(0, index=series.index)
    
    if method == 'zscore':
        return (series - series.mean()) / series.std()
    elif method == 'minmax':
        return (series - series.min()) / (series.max() - series.min())
    elif method == 'robust':
        median = series.median()
        mad = (series - median).abs().median()
        return (series - median) / (mad * 1.4826) if mad > 0 else pd.Series(0, index=series.index)
    else:
        raise ValueError(f"Unknown standardization method: {method}")


def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3) -> List[Tuple[pd.Timestamp, pd.Timestamp, pd.Timestamp, pd.Timestamp]]:
    """Create time-based train/validation splits"""
    
    splits = []
    
    # Sort dates and get unique dates
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    # Create monthly periods
    current_date = min_date
    
    while current_date < max_date:
        # Calculate split dates
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        # Check if we have enough data for validation
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        # Move to next validation period
        current_date = val_start
    
    return splits


def calculate_feature_stability(feature_importance_history: List[float], method: str = 'coefficient_of_variation') -> float:
    """Calculate stability of feature importance over time"""
    
    if len(feature_importance_history) < 2:
        return 0.0
    
    importance_series = pd.Series(feature_importance_history)
    
    if method == 'coefficient_of_variation':
        if importance_series.mean() == 0:
            return 0.0
        return 1 - (importance_series.std() / importance_series.mean())
    
    elif method == 'consistency_ratio':
        # Ratio of periods where feature was in top 50% of importance
        median_importance = importance_series.median()
        above_median = (importance_series >= median_importance).sum()
        return above_median / len(importance_series)
    
    else:
        raise ValueError(f"Unknown stability method: {method}")


def detect_feature_drift(current_importance: float, historical_importance: List[float], threshold: float = 0.3) -> bool:
    """Detect if feature importance has drifted significantly"""
    
    if len(historical_importance) < 3:
        return False
    
    historical_mean = np.mean(historical_importance)
    
    if historical_mean == 0:
        return current_importance > threshold
    
    relative_change = abs(current_importance - historical_mean) / historical_mean
    return relative_change > threshold


def validate_data_quality(df: pd.DataFrame, required_columns: List[str]) -> Dict[str, Any]:
    """Validate data quality and return quality metrics"""
    
    quality_report = {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'missing_required_columns': [],
        'missing_data_percentage': {},
        'duplicate_rows': 0,
        'data_types': {},
        'outlier_columns': [],
        'quality_score': 0.0
    }
    
    # Check required columns
    for col in required_columns:
        if col not in df.columns:
            quality_report['missing_required_columns'].append(col)
    
    # Check missing data
    for col in df.columns:
        missing_pct = df[col].isna().sum() / len(df) * 100
        quality_report['missing_data_percentage'][col] = missing_pct
    
    # Check duplicates
    quality_report['duplicate_rows'] = df.duplicated().sum()
    
    # Check data types
    for col in df.columns:
        quality_report['data_types'][col] = str(df[col].dtype)
    
    # Check for outliers (using IQR method for numerical columns)
    numerical_cols = df.select_dtypes(include=[np.number]).columns
    for col in numerical_cols:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
        outlier_percentage = outliers / len(df) * 100
        
        if outlier_percentage > 5:  # More than 5% outliers
            quality_report['outlier_columns'].append({
                'column': col,
                'outlier_percentage': outlier_percentage
            })
    
    # Calculate overall quality score
    quality_score = 100.0
    
    # Penalize missing required columns
    quality_score -= len(quality_report['missing_required_columns']) * 20
    
    # Penalize high missing data
    avg_missing = np.mean(list(quality_report['missing_data_percentage'].values()))
    quality_score -= avg_missing
    
    # Penalize duplicates
    duplicate_pct = quality_report['duplicate_rows'] / len(df) * 100
    quality_score -= duplicate_pct * 2
    
    # Penalize excessive outliers
    quality_score -= len(quality_report['outlier_columns']) * 2
    
    quality_report['quality_score'] = max(0.0, quality_score)
    
    return quality_report


def memory_usage_check(df: pd.DataFrame, limit_gb: float = 8.0) -> Dict[str, Any]:
    """Check memory usage and provide optimization suggestions"""
    
    memory_info = {
        'total_memory_mb': df.memory_usage(deep=True).sum() / 1024**2,
        'memory_per_column': {},
        'optimization_suggestions': [],
        'within_limit': True
    }
    
    # Calculate memory per column
    for col in df.columns:
        memory_mb = df[col].memory_usage(deep=True) / 1024**2
        memory_info['memory_per_column'][col] = memory_mb
    
    # Check if within limit
    memory_info['within_limit'] = memory_info['total_memory_mb'] < (limit_gb * 1024)
    
    # Provide optimization suggestions
    if not memory_info['within_limit']:
        # Suggest categorical conversion for object columns
        object_cols = df.select_dtypes(include=['object']).columns
        for col in object_cols:
            unique_ratio = df[col].nunique() / len(df)
            if unique_ratio < 0.5:  # Less than 50% unique values
                memory_info['optimization_suggestions'].append(
                    f"Convert '{col}' to categorical (unique ratio: {unique_ratio:.2%})"
                )
        
        # Suggest downcast for numerical columns
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        for col in numerical_cols:
            if df[col].dtype == 'float64':
                memory_info['optimization_suggestions'].append(
                    f"Downcast '{col}' from float64 to float32"
                )
            elif df[col].dtype == 'int64':
                memory_info['optimization_suggestions'].append(
                    f"Downcast '{col}' from int64 to int32"
                )
    
    return memory_info
