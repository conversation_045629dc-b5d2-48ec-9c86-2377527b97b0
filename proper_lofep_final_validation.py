#!/usr/bin/env python3
"""
Proper LOFEP Final Validation - exactly as designed in the LOFEP system
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
import json
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility - EXACT same as LOFEP"""
    cleaned = str(name)
    
    replacements = {
        ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_', 
        '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_', 
        '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
        '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
        '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
        '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)
    cleaned = re.sub(r'_+', '_', cleaned)
    cleaned = cleaned.strip('_')
    
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    
    if not cleaned:
        cleaned = 'Feature_Unknown'
    
    return cleaned

def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3):
    """Create time-based train/validation splits - EXACT same as LOFEP"""
    splits = []
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    current_date = min_date
    while current_date < max_date:
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        current_date = val_start
    
    return splits

def run_proper_lofep_final_validation():
    """Run the proper LOFEP final validation with all selected features"""
    
    print("🚀 PROPER LOFEP FINAL VALIDATION")
    print("="*60)
    
    # Load LOFEP configuration
    print("Loading LOFEP configuration...")
    with open('lofep_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Load all data files as specified in LOFEP config
    print("Loading data files...")
    features_df = pd.read_csv(config['data']['features_file'])
    target_df = pd.read_csv(config['data']['target_file'])
    returns_df = pd.read_csv(config['data']['returns_file'])
    
    print(f"Features: {features_df.shape}")
    print(f"Target: {target_df.shape}")
    print(f"Returns: {returns_df.shape}")
    
    # Merge data exactly as LOFEP does
    print("Merging datasets...")
    merged_data = features_df.merge(target_df, on=['Date', 'P123 ID'], how='inner')
    merged_data = merged_data.merge(returns_df, on=['Date', 'P123 ID'], how='inner')
    
    print(f"Merged data: {merged_data.shape}")
    
    # Load LOFEP selected features
    print("Loading LOFEP selected features...")
    selected_df = pd.read_csv('lofep_results/selected_features_20250711_165917.csv')
    selected_features = selected_df['feature_name'].tolist()
    
    print(f"LOFEP selected features: {len(selected_features)}")
    
    # Load LOFEP performance history to get beneficial features
    print("Loading LOFEP performance history...")
    perf_df = pd.read_csv('lofep_results/feature_performance_history_20250711_165917.csv', index_col=0)
    
    # Get features with significant positive improvement
    beneficial_features = []
    for idx, row in perf_df.iterrows():
        if row['sharpe_improvement'] > 0.02:  # LOFEP threshold
            beneficial_features.append(idx)
    
    print(f"Beneficial features found: {len(beneficial_features)}")
    print("Top 10 beneficial features:")
    improvements = [(idx, row['sharpe_improvement']) for idx, row in perf_df.iterrows()]
    improvements.sort(key=lambda x: x[1], reverse=True)
    for i, (feat, imp) in enumerate(improvements[:10]):
        print(f"  {i+1:2d}. {feat[:50]:<50} {imp:+.4f}")
    
    # Prepare feature set exactly as LOFEP does
    exclude_cols = [
        config['data']['date_column'],
        config['data']['id_column'], 
        config['data']['ticker_column'],
        config['data']['target_column'],
        config['data']['returns_column']
    ]
    
    # Get baseline features (original features from the dataset)
    all_features = [col for col in merged_data.columns 
                   if col not in exclude_cols and 
                   merged_data[col].dtype in ['int64', 'float64']]
    
    print(f"Total available features: {len(all_features)}")
    
    # Create final feature set: baseline + beneficial LOFEP features
    # Note: LOFEP generated features may not be in the merged dataset since they were created during LOFEP
    available_beneficial = [feat for feat in beneficial_features if feat in merged_data.columns]
    final_features = list(set(all_features + available_beneficial))
    
    print(f"Available beneficial features: {len(available_beneficial)}")
    print(f"Final feature set: {len(final_features)} features")
    
    # Clean feature names for LightGBM
    feature_mapping = {}
    for feature in final_features:
        cleaned_name = clean_feature_name(feature)
        feature_mapping[feature] = cleaned_name
    
    # Prepare data
    print("Preparing data for validation...")
    X = merged_data[final_features].copy()
    X.columns = [feature_mapping[col] for col in X.columns]
    X = X.fillna(0)
    X = X.replace([np.inf, -np.inf], 0)
    
    y = merged_data[config['data']['target_column']]
    dates = pd.to_datetime(merged_data[config['data']['date_column']])
    returns = merged_data[config['data']['returns_column']]
    
    print(f"Final validation dataset: {X.shape}")
    print(f"Date range: {dates.min()} to {dates.max()}")
    
    # Run time series cross-validation exactly as LOFEP does
    print("\nRunning LOFEP time series cross-validation...")
    
    splits = create_time_splits(dates, 
                               config['validation']['train_months'], 
                               config['validation']['validation_months'])
    
    print(f"Created {len(splits)} validation splits")
    
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
        try:
            # Create masks
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Prepare fold data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            
            # Remove NaN targets
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()
            
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            # Train LightGBM model with LOFEP parameters
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate portfolio performance exactly as LOFEP does
            val_returns_data = returns[val_mask][val_valid_mask]
            val_dates_data = dates[val_mask][val_valid_mask]
            
            # Portfolio construction: top 15 equal-weighted stocks, weekly rebalancing
            portfolio_returns = []
            
            for date in sorted(val_dates_data.unique()):
                date_mask = val_dates_data == date
                date_predictions = y_pred[date_mask]
                date_returns = val_returns_data[date_mask]
                
                if len(date_predictions) >= 15:
                    # Select top 15 stocks by prediction
                    top_indices = np.argsort(date_predictions)[-15:]
                    top_returns = date_returns.iloc[top_indices]
                    
                    # Equal-weighted portfolio return (convert percentage to decimal)
                    portfolio_return = top_returns.mean() / 100.0
                    portfolio_returns.append(portfolio_return)
            
            if len(portfolio_returns) > 0:
                portfolio_returns = np.array(portfolio_returns)
                
                # Calculate metrics exactly as LOFEP does
                mean_return = portfolio_returns.mean()
                volatility = portfolio_returns.std()
                
                # Annualize (weekly rebalancing, 52 periods per year)
                annual_return = mean_return * 52
                annual_volatility = volatility * np.sqrt(52)
                sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
                
                # CAGR
                cumulative_return = np.prod(1 + portfolio_returns) - 1
                n_years = len(portfolio_returns) / 52
                cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 and cumulative_return > -0.99 else 0
                
                fold_results.append({
                    'fold': fold_idx,
                    'sharpe': sharpe,
                    'cagr': cagr,
                    'annual_return': annual_return,
                    'annual_volatility': annual_volatility,
                    'periods': len(portfolio_returns)
                })
                
                print(f"Fold {fold_idx:2d} ({val_start.strftime('%Y-%m')}-{val_end.strftime('%Y-%m')}): "
                      f"Sharpe = {sharpe:6.3f}, CAGR = {cagr:6.1%}, Periods = {len(portfolio_returns)}")
        
        except Exception as e:
            print(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Calculate final results
    if fold_results:
        sharpe_values = [f['sharpe'] for f in fold_results]
        cagr_values = [f['cagr'] for f in fold_results]
        
        mean_sharpe = np.mean(sharpe_values)
        std_sharpe = np.std(sharpe_values)
        mean_cagr = np.mean(cagr_values)
        
        print(f"\n🎉 LOFEP FINAL VALIDATION RESULTS:")
        print(f"="*60)
        print(f"Mean Sharpe Ratio: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Mean CAGR: {mean_cagr:.2%}")
        print(f"Number of folds: {len(fold_results)}")
        print(f"Min Sharpe: {np.min(sharpe_values):.4f}")
        print(f"Max Sharpe: {np.max(sharpe_values):.4f}")
        print(f"Features used: {len(final_features)}")
        print(f"Beneficial LOFEP features: {len(available_beneficial)}")
        
        # Compare to baseline
        baseline_sharpe = 1.7765  # From LOFEP logs
        improvement = mean_sharpe - baseline_sharpe
        
        print(f"\nPerformance vs Baseline:")
        print(f"LOFEP Baseline: {baseline_sharpe:.4f}")
        print(f"Final Result: {mean_sharpe:.4f}")
        print(f"Improvement: {improvement:+.4f} ({improvement/baseline_sharpe:+.1%})")
        
        # Save results
        results = {
            'final_sharpe_mean': mean_sharpe,
            'final_sharpe_std': std_sharpe,
            'final_cagr_mean': mean_cagr,
            'baseline_sharpe': baseline_sharpe,
            'improvement': improvement,
            'improvement_percent': improvement/baseline_sharpe,
            'num_folds': len(fold_results),
            'features_used': len(final_features),
            'beneficial_features': len(available_beneficial),
            'fold_results': fold_results,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('lofep_results/proper_final_validation_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to: lofep_results/proper_final_validation_results.json")
        
        if improvement > 0.1:
            print("🎉 LOFEP successfully improved performance!")
        elif improvement > 0:
            print("✅ LOFEP provided modest improvement")
        else:
            print("⚠️  LOFEP did not improve performance")
        
    else:
        print("❌ No successful validation folds")

if __name__ == "__main__":
    run_proper_lofep_final_validation()
