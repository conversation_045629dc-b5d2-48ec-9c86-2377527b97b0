#!/usr/bin/env python3
"""
LOFEP Transformation Engine
Generates features that LightGBM cannot discover naturally
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any
from lofep_utils import safe_divide, winsorize_series, standardize_series


class TransformationEngine:
    """
    Engine for generating mathematical, cross-sectional, temporal, and ratio features
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger('LOFEP.TransformationEngine')
        
        # Cache for expensive computations
        self.computation_cache = {}
        
        self.logger.info("TransformationEngine initialized")
    
    def generate_mathematical_features(self, df: pd.DataFrame, base_features: List[str]) -> Dict[str, pd.Series]:
        """Generate mathematical transformations that LightGBM cannot discover"""
        
        self.logger.info("Generating mathematical transformations...")
        
        math_features = {}
        transformations = self.config['feature_generation']['mathematical']['transformations']
        
        # Limit to top features by variance to keep it manageable
        feature_variance = df[base_features].var().sort_values(ascending=False)
        top_features = feature_variance.head(50).index.tolist()  # Top 50 features (reduced for memory)

        self.logger.info(f"Transforming top {len(top_features)} features by variance")

        for feature in top_features:
            feature_data = df[feature]

            # Skip if all NaN or constant
            if feature_data.isna().all() or feature_data.nunique() <= 1:
                continue
            
            try:
                # Rank transformation (most important for LightGBM)
                if 'rank' in transformations:
                    rank_feature = feature_data.rank(method='average', na_option='keep')
                    math_features[f"{feature}_rank"] = rank_feature

                # Log transformation (handle negative values)
                if 'log' in transformations:
                    # Use log(abs(x) + 1) to handle negative values and zeros
                    log_feature = np.log(np.abs(feature_data) + 1)
                    # Preserve sign
                    log_feature = log_feature * np.sign(feature_data)
                    math_features[f"{feature}_log"] = log_feature

                # Square root transformation (variance stabilization)
                if 'sqrt' in transformations:
                    sqrt_feature = np.sqrt(np.abs(feature_data)) * np.sign(feature_data)
                    math_features[f"{feature}_sqrt"] = sqrt_feature

                # Z-score standardization
                if 'zscore' in transformations:
                    if feature_data.std() > 0:
                        zscore_feature = standardize_series(feature_data, method='zscore')
                        math_features[f"{feature}_zscore"] = zscore_feature

                # Percentile transformation
                if 'percentile' in transformations:
                    percentile_feature = feature_data.rank(pct=True) * 100
                    math_features[f"{feature}_percentile"] = percentile_feature

            except Exception as e:
                self.logger.warning(f"Error transforming {feature}: {e}")
                continue
        
        # Clean generated features
        cleaned_features = {}
        for feature_name, feature_values in math_features.items():
            # Remove infinite and NaN values
            cleaned_values = feature_values.replace([np.inf, -np.inf], np.nan)
            cleaned_values = cleaned_values.fillna(0)
            cleaned_features[feature_name] = cleaned_values

        self.logger.info(f"Generated {len(cleaned_features)} mathematical features")
        return cleaned_features
    
    def generate_cross_sectional_features(self, df: pd.DataFrame, base_features: List[str]) -> Dict[str, pd.Series]:
        """Generate cross-sectional features (rankings within groups)"""
        
        self.logger.info("Generating cross-sectional features...")
        
        cross_features = {}
        grouping_columns = self.config['feature_generation']['cross_sectional']['grouping_columns']
        transformations = self.config['feature_generation']['cross_sectional']['transformations']
        
        # Create market cap quintiles if not exists
        if 'Market_Cap_Quintile' in grouping_columns and 'Market_Cap_Quintile' not in df.columns:
            if 'MarketCap' in df.columns:
                try:
                    df['Market_Cap_Quintile'] = pd.qcut(df['MarketCap'], 5, labels=False, duplicates='drop')
                    self.logger.info("Created Market_Cap_Quintile grouping column")
                except Exception as e:
                    self.logger.warning(f"Could not create Market_Cap_Quintile: {e}")
                    grouping_columns = [col for col in grouping_columns if col != 'Market_Cap_Quintile']
            else:
                # Remove from grouping columns if MarketCap doesn't exist
                grouping_columns = [col for col in grouping_columns if col != 'Market_Cap_Quintile']
        
        for group_col in grouping_columns:
            if group_col not in df.columns:
                self.logger.warning(f"Grouping column '{group_col}' not found in data")
                continue
            
            for feature in base_features:
                feature_data = df[feature]
                
                # Skip if all NaN or constant
                if feature_data.isna().all() or feature_data.nunique() <= 1:
                    continue
                
                # Group-wise rank
                if 'rank' in transformations:
                    group_rank = df.groupby(group_col)[feature].rank(method='average', na_option='keep')
                    cross_features[f"{feature}_{group_col}_rank"] = group_rank
                
                # Group-wise percentile
                if 'percentile' in transformations:
                    group_percentile = df.groupby(group_col)[feature].rank(pct=True) * 100
                    cross_features[f"{feature}_{group_col}_percentile"] = group_percentile
                
                # Group-wise z-score
                if 'zscore' in transformations:
                    group_mean = df.groupby(group_col)[feature].transform('mean')
                    group_std = df.groupby(group_col)[feature].transform('std')
                    group_zscore = safe_divide(feature_data - group_mean, group_std, fill_value=0)
                    cross_features[f"{feature}_{group_col}_zscore"] = group_zscore
                
                # Relative ratio (feature / group_median)
                if 'relative_ratio' in transformations:
                    group_median = df.groupby(group_col)[feature].transform('median')
                    relative_ratio = safe_divide(feature_data, group_median, fill_value=1)
                    cross_features[f"{feature}_{group_col}_ratio"] = relative_ratio
        
        self.logger.info(f"Generated {len(cross_features)} cross-sectional features")
        return cross_features
    
    def generate_temporal_features(self, df: pd.DataFrame, base_features: List[str]) -> Dict[str, pd.Series]:
        """Generate temporal features (rolling statistics, momentum)"""
        
        self.logger.info("Generating temporal features...")
        
        temporal_features = {}
        windows = self.config['feature_generation']['temporal']['windows']
        transformations = self.config['feature_generation']['temporal']['transformations']
        
        # Sort by date and ID for proper time series operations
        date_col = self.config['data']['date_column']
        id_col = self.config['data']['id_column']
        
        if date_col not in df.columns or id_col not in df.columns:
            self.logger.warning("Date or ID column not found, skipping temporal features")
            return temporal_features
        
        # Sort dataframe
        df_sorted = df.sort_values([id_col, date_col])
        
        for feature in base_features:
            feature_data = df_sorted[feature]
            
            # Skip if all NaN or constant
            if feature_data.isna().all() or feature_data.nunique() <= 1:
                continue
            
            for window in windows:
                # Rolling mean
                if 'rolling_mean' in transformations:
                    rolling_mean = df_sorted.groupby(id_col)[feature].rolling(window=window, min_periods=1).mean().reset_index(0, drop=True)
                    temporal_features[f"{feature}_rolling_mean_{window}M"] = rolling_mean
                
                # Rolling standard deviation
                if 'rolling_std' in transformations:
                    rolling_std = df_sorted.groupby(id_col)[feature].rolling(window=window, min_periods=1).std().reset_index(0, drop=True)
                    temporal_features[f"{feature}_rolling_std_{window}M"] = rolling_std
                
                # Rolling median
                if 'rolling_median' in transformations:
                    rolling_median = df_sorted.groupby(id_col)[feature].rolling(window=window, min_periods=1).median().reset_index(0, drop=True)
                    temporal_features[f"{feature}_rolling_median_{window}M"] = rolling_median
                
                # Momentum (current / lagged - 1)
                if 'momentum' in transformations:
                    lagged_feature = df_sorted.groupby(id_col)[feature].shift(window)
                    momentum = safe_divide(feature_data, lagged_feature, fill_value=1) - 1
                    temporal_features[f"{feature}_momentum_{window}M"] = momentum
                
                # Mean reversion (distance from rolling mean)
                if 'mean_reversion' in transformations:
                    rolling_mean = df_sorted.groupby(id_col)[feature].rolling(window=window, min_periods=1).mean().reset_index(0, drop=True)
                    mean_reversion = feature_data - rolling_mean
                    temporal_features[f"{feature}_mean_reversion_{window}M"] = mean_reversion
                
                # Volatility (rolling coefficient of variation)
                if 'volatility' in transformations:
                    rolling_mean = df_sorted.groupby(id_col)[feature].rolling(window=window, min_periods=1).mean().reset_index(0, drop=True)
                    rolling_std = df_sorted.groupby(id_col)[feature].rolling(window=window, min_periods=1).std().reset_index(0, drop=True)
                    volatility = safe_divide(rolling_std, rolling_mean, fill_value=0)
                    temporal_features[f"{feature}_volatility_{window}M"] = volatility
        
        # Reindex to match original dataframe order
        original_index = df.index
        for feature_name, feature_values in temporal_features.items():
            # Map back to original index
            temporal_features[feature_name] = feature_values.reindex(original_index)
        
        self.logger.info(f"Generated {len(temporal_features)} temporal features")
        return temporal_features
    
    def generate_ratio_features(self, df: pd.DataFrame, base_features: List[str]) -> Dict[str, pd.Series]:
        """Generate ratio and normalized features"""
        
        self.logger.info("Generating ratio features...")
        
        ratio_features = {}
        normalization_methods = self.config['feature_generation']['ratio']['normalization_methods']
        
        # Create grouping columns for normalization
        grouping_info = {
            'sector_median': 'Sector',
            'industry_median': 'Industry',
            'market_median': None,  # Overall market
            'historical_median': None  # Historical for same stock
        }
        
        for feature in base_features:
            feature_data = df[feature]
            
            # Skip if all NaN or constant
            if feature_data.isna().all() or feature_data.nunique() <= 1:
                continue
            
            for norm_method in normalization_methods:
                if norm_method not in grouping_info:
                    continue
                
                group_col = grouping_info[norm_method]
                
                if norm_method == 'sector_median' and 'Sector' in df.columns:
                    sector_median = df.groupby('Sector')[feature].transform('median')
                    ratio = safe_divide(feature_data, sector_median, fill_value=1)
                    ratio_features[f"{feature}_vs_sector_median"] = ratio
                
                elif norm_method == 'industry_median' and 'Industry' in df.columns:
                    industry_median = df.groupby('Industry')[feature].transform('median')
                    ratio = safe_divide(feature_data, industry_median, fill_value=1)
                    ratio_features[f"{feature}_vs_industry_median"] = ratio
                
                elif norm_method == 'market_median':
                    market_median = feature_data.median()
                    ratio = safe_divide(feature_data, market_median, fill_value=1)
                    ratio_features[f"{feature}_vs_market_median"] = ratio
                
                elif norm_method == 'historical_median':
                    # Historical median for same stock (requires ID column)
                    id_col = self.config['data']['id_column']
                    if id_col in df.columns:
                        historical_median = df.groupby(id_col)[feature].transform('median')
                        ratio = safe_divide(feature_data, historical_median, fill_value=1)
                        ratio_features[f"{feature}_vs_historical_median"] = ratio
        
        self.logger.info(f"Generated {len(ratio_features)} ratio features")
        return ratio_features
    
    def generate_interaction_features(self, df: pd.DataFrame, base_features: List[str], 
                                    selected_features: List[str]) -> Dict[str, pd.Series]:
        """Generate selective interaction features (only if enabled and beneficial)"""
        
        if not self.config['feature_generation']['interactions']['enabled']:
            return {}
        
        self.logger.info("Generating selective interaction features...")
        
        interaction_features = {}
        max_interactions = self.config['feature_generation']['interactions']['max_interactions']
        min_importance = self.config['feature_generation']['interactions']['min_individual_importance']
        
        # Only create interactions between high-importance features
        high_importance_features = selected_features[:20]  # Top 20 features
        
        interaction_count = 0
        for i, feature1 in enumerate(high_importance_features):
            if interaction_count >= max_interactions:
                break
            
            for j, feature2 in enumerate(high_importance_features[i+1:], i+1):
                if interaction_count >= max_interactions:
                    break
                
                if feature1 in df.columns and feature2 in df.columns:
                    # Simple multiplication interaction
                    interaction = df[feature1] * df[feature2]
                    interaction_features[f"{feature1}_x_{feature2}"] = interaction
                    interaction_count += 1
                    
                    # Ratio interaction
                    if interaction_count < max_interactions:
                        ratio_interaction = safe_divide(df[feature1], df[feature2], fill_value=0)
                        interaction_features[f"{feature1}_div_{feature2}"] = ratio_interaction
                        interaction_count += 1
        
        self.logger.info(f"Generated {len(interaction_features)} interaction features")
        return interaction_features
