#!/usr/bin/env python3
"""
LOFEP Validation Engine
Time-aware validation using actual LightGBM performance metrics
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import logging
from typing import Dict, List, Tuple, Any
from sklearn.metrics import mean_squared_error
from lofep_utils import calculate_performance_metrics, create_time_splits


class ValidationEngine:
    """
    Engine for rigorous time-aware validation using actual LightGBM performance
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger('LOFEP.ValidationEngine')
        
        # LightGBM parameters
        self.lgb_params = self.config['lightgbm']['params'].copy()
        self.training_params = self.config['lightgbm']['training'].copy()
        
        self.logger.info("ValidationEngine initialized")
    
    def time_series_cross_validation(self, X: pd.DataFrame, y: pd.Series, dates: pd.Series,
                                   feature_name: str = "UNKNOWN", returns: pd.Series = None) -> Dict[str, float]:
        """
        Perform time series cross-validation with walk-forward approach
        Returns actual LightGBM performance metrics
        """
        
        self.logger.info(f"Running time series CV for: {feature_name}")
        
        # Create time splits
        train_months = self.config['validation']['train_months']
        val_months = self.config['validation']['validation_months']
        
        splits = create_time_splits(dates, train_months, val_months)
        
        if len(splits) < self.config['validation']['min_periods']:
            self.logger.warning(f"Insufficient validation periods: {len(splits)} < {self.config['validation']['min_periods']}")
            return self._empty_performance_dict()
        
        # Store results for each fold
        fold_results = []
        
        for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
            try:
                # Create masks for this fold
                train_mask = (dates >= train_start) & (dates < train_end)
                val_mask = (dates >= val_start) & (dates < val_end)
                
                # Check if we have enough data
                if train_mask.sum() < 100 or val_mask.sum() < 20:
                    self.logger.warning(f"Insufficient data in fold {fold_idx}: train={train_mask.sum()}, val={val_mask.sum()}")
                    continue
                
                # Prepare data for this fold
                X_train = X[train_mask].copy()
                y_train = y[train_mask].copy()
                X_val = X[val_mask].copy()
                y_val = y[val_mask].copy()

                # Robust data cleaning for this fold
                # Fill missing values
                X_train = X_train.fillna(X_train.median())
                X_val = X_val.fillna(X_train.median())  # Use training medians for validation

                # Handle infinite values
                X_train = X_train.replace([np.inf, -np.inf], np.nan)
                X_val = X_val.replace([np.inf, -np.inf], np.nan)
                X_train = X_train.fillna(0)
                X_val = X_val.fillna(0)

                # Remove NaN from target
                train_valid_mask = ~y_train.isna()
                val_valid_mask = ~y_val.isna()

                X_train = X_train[train_valid_mask]
                y_train = y_train[train_valid_mask]
                X_val = X_val[val_valid_mask]
                y_val = y_val[val_valid_mask]

                # Final validation
                if X_train.isnull().any().any() or X_val.isnull().any().any():
                    self.logger.warning(f"Still have NaN values in fold {fold_idx}")
                    continue

                if np.isinf(X_train.values).any() or np.isinf(X_val.values).any():
                    self.logger.warning(f"Still have infinite values in fold {fold_idx}")
                    continue
                
                # Train LightGBM model
                train_data = lgb.Dataset(X_train, label=y_train)
                val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                
                model = lgb.train(
                    self.lgb_params,
                    train_data,
                    valid_sets=[val_data],
                    num_boost_round=self.training_params['num_boost_round'],
                    callbacks=[
                        lgb.early_stopping(self.training_params['early_stopping_rounds']),
                        lgb.log_evaluation(0)  # Suppress output
                    ]
                )
                
                # Make predictions
                y_pred = model.predict(X_val)

                # Get corresponding returns and dates for performance calculation
                val_returns = returns[val_mask] if returns is not None else y_val
                val_dates = dates[val_mask]

                # Calculate performance metrics
                fold_performance = self._calculate_fold_performance(y_val, y_pred, val_start, val_end, val_returns, val_dates)
                fold_performance['fold'] = fold_idx
                fold_performance['train_start'] = train_start
                fold_performance['train_end'] = train_end
                fold_performance['val_start'] = val_start
                fold_performance['val_end'] = val_end
                
                fold_results.append(fold_performance)
                
                self.logger.debug(f"Fold {fold_idx}: Sharpe={fold_performance['sharpe_ratio']:.4f}, CAGR={fold_performance['cagr']:.2%}")
                
            except Exception as e:
                self.logger.warning(f"Error in fold {fold_idx}: {str(e)}")
                continue
        
        if not fold_results:
            self.logger.error("No successful validation folds")
            return self._empty_performance_dict()
        
        # Aggregate results across folds
        aggregated_results = self._aggregate_fold_results(fold_results)
        aggregated_results['num_folds'] = len(fold_results)
        aggregated_results['feature_name'] = feature_name
        
        self.logger.info(f"CV completed for {feature_name}: Sharpe={aggregated_results.get('mean_sharpe_ratio', 0):.4f} ± {aggregated_results.get('std_sharpe_ratio', 0):.4f}")
        
        return aggregated_results
    
    def _calculate_fold_performance(self, y_true: pd.Series, y_pred: np.ndarray,
                                  start_date: pd.Timestamp, end_date: pd.Timestamp,
                                  actual_returns: pd.Series = None, val_dates: pd.Series = None) -> Dict[str, float]:
        """Calculate performance metrics for a single fold using proper portfolio construction"""

        predictions = pd.Series(y_pred, index=y_true.index)

        # Use actual returns if provided, otherwise fall back to target
        if actual_returns is not None:
            returns_data = actual_returns
        else:
            # Fallback: use predictions as returns (not ideal)
            returns_data = predictions

        # Remove NaN values
        valid_mask = ~(returns_data.isna() | predictions.isna())
        returns_data = returns_data[valid_mask]
        predictions = predictions[valid_mask]

        if val_dates is not None:
            val_dates = val_dates[valid_mask]

        if len(returns_data) == 0:
            return self._empty_single_fold_performance()

        # Proper portfolio construction: group by date and select top stocks each period
        portfolio_returns = []

        if val_dates is not None:
            # Group by date and create portfolio for each date
            unique_dates = sorted(val_dates.unique())
            self.logger.debug(f"Processing {len(unique_dates)} unique dates")

            for date in unique_dates:
                date_mask = val_dates == date
                date_predictions = predictions[date_mask]
                date_returns = returns_data[date_mask]

                self.logger.debug(f"Date {date}: {len(date_predictions)} stocks available")

                if len(date_predictions) >= 20:  # Need at least 20 stocks
                    # Get top 20 stocks by prediction
                    top_indices = date_predictions.nlargest(20).index
                    top_returns = date_returns[top_indices]

                    # Equal-weighted portfolio return (convert percentage to decimal)
                    portfolio_return = top_returns.mean() / 100.0
                    portfolio_returns.append(portfolio_return)

                    self.logger.debug(f"Date {date}: portfolio return = {portfolio_return:.6f}")
                else:
                    self.logger.debug(f"Date {date}: insufficient stocks ({len(date_predictions)} < 20)")
        else:
            # Fallback: single period portfolio
            top_k = min(20, len(predictions))
            top_indices = predictions.nlargest(top_k).index
            portfolio_return = returns_data[top_indices].mean() / 100.0
            portfolio_returns.append(portfolio_return)

        if len(portfolio_returns) == 0:
            return self._empty_single_fold_performance()

        # Calculate performance metrics using the EXACT working method
        portfolio_returns = np.array(portfolio_returns)

        if len(portfolio_returns) == 0:
            return self._empty_single_fold_performance()

        # Debug logging
        self.logger.debug(f"Portfolio returns count: {len(portfolio_returns)}")
        self.logger.debug(f"Portfolio returns sample: {portfolio_returns[:5] if len(portfolio_returns) > 0 else 'None'}")
        self.logger.debug(f"Portfolio returns mean: {portfolio_returns.mean():.6f}")
        self.logger.debug(f"Portfolio returns std: {portfolio_returns.std():.6f}")

        # Sanity check: if mean return > 10% per period, we likely have data leakage
        if portfolio_returns.mean() > 0.10:
            self.logger.warning(f"Suspiciously high returns detected: {portfolio_returns.mean():.2%} per period")
            self.logger.warning("This suggests possible data leakage or look-ahead bias")

        # Calculate metrics (EXACT same method as working code)
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()

        # Annualize (5-day returns, approximately 52 periods per year)
        # But we need to be more careful about the time period
        periods_per_year = 52  # Approximately 52 weeks per year for 5-day returns
        annual_return = mean_return * periods_per_year
        annual_volatility = volatility * np.sqrt(periods_per_year)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0

        self.logger.debug(f"Fold calculation: mean_return={mean_return:.6f}, annual_return={annual_return:.6f}, annual_vol={annual_volatility:.6f}, sharpe={sharpe:.4f}")

        # CAGR - be more conservative with the calculation
        cumulative_return = np.prod(1 + portfolio_returns) - 1
        n_years = len(portfolio_returns) / periods_per_year
        cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 and cumulative_return > -0.99 else 0

        # Max drawdown
        cumulative_wealth = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_wealth)
        drawdowns = (cumulative_wealth - running_max) / running_max
        max_drawdown = np.min(drawdowns)

        # Hit rate
        hit_rate = (portfolio_returns > 0).mean()

        performance = {
            'sharpe_ratio': sharpe,
            'cagr': cagr,
            'volatility': annual_volatility,
            'max_drawdown': max_drawdown,
            'hit_rate': hit_rate,
            'total_return': cumulative_return
        }

        # Add additional metrics (comparing predictions to actual target, not returns)
        target_values = y_true[valid_mask]
        performance['rmse'] = np.sqrt(mean_squared_error(target_values, predictions))
        performance['correlation'] = np.corrcoef(target_values, predictions)[0, 1] if len(target_values) > 1 else 0

        # Calculate information coefficient (Spearman correlation between predictions and returns)
        try:
            from scipy.stats import spearmanr
            ic, _ = spearmanr(predictions, returns_data)
            performance['information_coefficient'] = ic if not np.isnan(ic) else 0
        except:
            performance['information_coefficient'] = 0

        return performance

    def _empty_single_fold_performance(self) -> Dict[str, float]:
        """Return empty performance for a single fold"""
        return {
            'sharpe_ratio': 0.0,
            'cagr': 0.0,
            'volatility': 0.0,
            'max_drawdown': 0.0,
            'hit_rate': 0.5,
            'rmse': 1.0,
            'correlation': 0.0,
            'information_coefficient': 0.0
        }
    
    def _aggregate_fold_results(self, fold_results: List[Dict[str, float]]) -> Dict[str, float]:
        """Aggregate results across all folds"""
        
        if not fold_results:
            return self._empty_performance_dict()
        
        # Convert to DataFrame for easier aggregation
        results_df = pd.DataFrame(fold_results)
        
        # Calculate mean and std for key metrics
        aggregated = {}
        
        key_metrics = ['sharpe_ratio', 'cagr', 'volatility', 'max_drawdown', 'hit_rate', 
                      'rmse', 'correlation', 'information_coefficient']
        
        for metric in key_metrics:
            if metric in results_df.columns:
                aggregated[f'mean_{metric}'] = results_df[metric].mean()
                aggregated[f'std_{metric}'] = results_df[metric].std()
                aggregated[f'min_{metric}'] = results_df[metric].min()
                aggregated[f'max_{metric}'] = results_df[metric].max()
        
        # Calculate stability metrics
        aggregated['stability_score'] = self._calculate_stability_score(results_df)
        
        return aggregated
    
    def _calculate_stability_score(self, results_df: pd.DataFrame) -> float:
        """Calculate stability score based on consistency of performance"""
        
        if len(results_df) < 2:
            return 0.0
        
        # Use Sharpe ratio consistency as primary stability metric
        sharpe_values = results_df['sharpe_ratio']
        
        # Calculate coefficient of variation (lower is more stable)
        if sharpe_values.mean() != 0:
            cv = sharpe_values.std() / abs(sharpe_values.mean())
            stability = max(0, 1 - cv)  # Convert to 0-1 scale where 1 is most stable
        else:
            stability = 0.0
        
        # Bonus for consistently positive Sharpe ratios
        positive_ratio = (sharpe_values > 0).mean()
        stability = stability * (0.5 + 0.5 * positive_ratio)
        
        return stability
    
    def _empty_performance_dict(self) -> Dict[str, float]:
        """Return empty performance dictionary with default values"""
        
        return {
            'mean_sharpe_ratio': 0.0,
            'std_sharpe_ratio': 0.0,
            'mean_cagr': 0.0,
            'std_cagr': 0.0,
            'mean_volatility': 0.0,
            'mean_max_drawdown': 0.0,
            'mean_hit_rate': 0.5,
            'mean_rmse': 1.0,
            'mean_correlation': 0.0,
            'mean_information_coefficient': 0.0,
            'stability_score': 0.0,
            'num_folds': 0
        }
    
    def validate_feature_significance(self, baseline_performance: Dict[str, float], 
                                    feature_performance: Dict[str, float]) -> Dict[str, Any]:
        """Test if feature provides statistically significant improvement"""
        
        baseline_sharpe = baseline_performance.get('mean_sharpe_ratio', 0)
        feature_sharpe = feature_performance.get('mean_sharpe_ratio', 0)
        
        sharpe_improvement = feature_sharpe - baseline_sharpe
        
        # Simple significance test based on improvement threshold
        significance_threshold = self.config['validation']['significance_threshold']
        is_significant = sharpe_improvement >= significance_threshold
        
        # Stability requirement
        stability_threshold = self.config['validation']['stability_threshold']
        stability_score = feature_performance.get('stability_score', 0)
        is_stable = stability_score >= stability_threshold
        
        # Overall acceptance
        is_accepted = is_significant and is_stable
        
        validation_result = {
            'sharpe_improvement': sharpe_improvement,
            'is_significant': is_significant,
            'significance_threshold': significance_threshold,
            'stability_score': stability_score,
            'is_stable': is_stable,
            'stability_threshold': stability_threshold,
            'is_accepted': is_accepted,
            'baseline_sharpe': baseline_sharpe,
            'feature_sharpe': feature_sharpe
        }
        
        return validation_result
    
    def batch_validate_features(self, X_base: pd.DataFrame, feature_candidates: Dict[str, pd.Series], 
                              y: pd.Series, dates: pd.Series, 
                              baseline_performance: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """Validate multiple features in batch"""
        
        self.logger.info(f"Batch validating {len(feature_candidates)} features...")
        
        validation_results = {}
        
        for feature_name, feature_values in feature_candidates.items():
            try:
                # Create enhanced dataset with this feature
                X_enhanced = X_base.copy()
                X_enhanced[feature_name] = feature_values.fillna(0)
                
                # Validate feature
                feature_performance = self.time_series_cross_validation(
                    X_enhanced, y, dates, feature_name
                )
                
                # Test significance
                significance_result = self.validate_feature_significance(
                    baseline_performance, feature_performance
                )
                
                # Combine results
                validation_results[feature_name] = {
                    'performance': feature_performance,
                    'significance': significance_result
                }
                
                if significance_result['is_accepted']:
                    self.logger.info(f"✓ {feature_name}: +{significance_result['sharpe_improvement']:.4f} Sharpe (accepted)")
                else:
                    self.logger.debug(f"✗ {feature_name}: +{significance_result['sharpe_improvement']:.4f} Sharpe (rejected)")
                
            except Exception as e:
                self.logger.warning(f"Error validating {feature_name}: {str(e)}")
                continue
        
        # Summary statistics
        accepted_features = [name for name, result in validation_results.items() 
                           if result['significance']['is_accepted']]
        
        self.logger.info(f"Batch validation completed: {len(accepted_features)}/{len(feature_candidates)} features accepted")
        
        return validation_results
