#!/usr/bin/env python3
"""
PROPER Hyperparameter Optimization - NO DATA LEAKAGE
- Uses EXACT same methodology as proven validation system
- Proper 6-month gaps in ALL splits
- Same time period (2005-2025)
- Future returns properly aligned
- Conservative approach to prevent overfitting
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import optuna
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class ProperHyperparameterOptimizer:
    def __init__(self, n_trials=30):
        self.n_trials = n_trials
        self.best_params = None
        self.optimization_results = []
        
    def load_and_prepare_data(self):
        """Load data using EXACT same method as proven validation system"""
        print("📊 Loading data (SAME as proven validation system)...")
        
        # Load datasets
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        target_df = pd.read_csv('PureEURTarget.csv')
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        
        # Clean feature names (EXACT same method)
        import re
        name_mapping = {}
        for col in features_df.columns:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')
            name_mapping[col] = clean_name
        features_df = features_df.rename(columns=name_mapping)
        
        # Merge datasets (EXACT same method)
        data = features_df.merge(
            target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        data = data.merge(
            returns_df[['Date', 'P123 ID', 'Future5DReturn']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        # Filter to EXACT same time period
        data['Date'] = pd.to_datetime(data['Date'])
        start_date = pd.to_datetime('2005-12-01')
        end_date = pd.to_datetime('2025-05-01')
        
        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        data = data.sort_values('Date').reset_index(drop=True)
        
        # Feature columns (EXACT same method)
        feature_cols = [col for col in data.columns if col not in 
                       ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']]
        
        print(f"✓ Data: {len(data)} samples, {len(feature_cols)} features")
        print(f"✓ Date range: {data['Date'].min().date()} to {data['Date'].max().date()}")
        print(f"✓ SAME as proven validation system")
        
        self.data = data
        self.feature_cols = feature_cols
        
        return data
    
    def create_proper_time_series_splits(self, n_splits=5, gap_months=6):
        """Create time series splits with PROPER 6-month gaps (EXACT same method)"""
        print(f"📅 Creating {n_splits} time series splits with {gap_months}-month gaps...")
        
        # Get unique dates
        unique_dates = sorted(self.data['Date'].unique())
        print(f"Date range: {unique_dates[0].date()} to {unique_dates[-1].date()}")
        print(f"Total unique dates: {len(unique_dates)}")
        
        # Calculate split points (EXACT same method as proven system)
        total_dates = len(unique_dates)
        dates_per_split = total_dates // (n_splits + 1)
        gap_days = gap_months * 30  # Approximate gap in days
        
        splits = []
        
        for i in range(n_splits):
            # Training end
            train_end_idx = (i + 1) * dates_per_split
            train_end_date = unique_dates[min(train_end_idx, len(unique_dates) - 1)]
            
            # Gap
            val_start_date = train_end_date + timedelta(days=gap_days)
            
            # Find validation start index
            val_start_idx = next((idx for idx, date in enumerate(unique_dates) 
                                if date >= val_start_date), len(unique_dates))
            
            # Validation end
            val_end_idx = min(val_start_idx + dates_per_split, len(unique_dates) - 1)
            val_end_date = unique_dates[val_end_idx]
            
            if val_start_idx < len(unique_dates):
                splits.append({
                    'train_start': unique_dates[0],
                    'train_end': train_end_date,
                    'val_start': unique_dates[val_start_idx],
                    'val_end': val_end_date,
                    'fold': i + 1
                })
        
        print(f"✓ Created {len(splits)} valid splits with PROPER 6-month gaps")
        for i, split in enumerate(splits):
            gap_days = (split['val_start'] - split['train_end']).days
            print(f"  Fold {split['fold']}: Train {split['train_start'].date()} to {split['train_end'].date()}, "
                  f"Val {split['val_start'].date()} to {split['val_end'].date()} (Gap: {gap_days} days)")
        
        self.splits = splits
        return splits
    
    def convert_to_relevance_scores(self, targets, dates, n_levels=5):
        """Convert targets to relevance scores (EXACT same method)"""
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['relevance'] = 0
        
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']
            
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                valid_targets = date_targets[valid_mask]
                relevance_scores = pd.qcut(valid_targets, q=n_levels, labels=False, duplicates='drop')
                df.loc[date_mask & valid_mask, 'relevance'] = relevance_scores
        
        return df['relevance'].values
    
    def prepare_ranking_data(self, dates):
        """Prepare groups for ranking (EXACT same method)"""
        unique_dates = sorted(dates.unique())
        groups = []
        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)
        return groups
    
    def calculate_portfolio_metrics(self, predictions, returns, dates, top_k=20):
        """Calculate portfolio metrics (EXACT same method as proven system)"""
        portfolio_returns = []
        
        # Group by date and select top k stocks
        for date in sorted(dates.unique()):
            date_mask = dates == date
            date_predictions = predictions[date_mask]
            date_returns = returns[date_mask]
            
            if len(date_predictions) >= top_k:
                # Get top k stocks by prediction
                top_indices = np.argsort(date_predictions)[-top_k:]
                top_returns = date_returns.iloc[top_indices]
                
                # Equal-weighted portfolio return
                portfolio_return = top_returns.mean() / 100.0  # Convert percentage to decimal
                portfolio_returns.append(portfolio_return)
        
        if len(portfolio_returns) == 0:
            return {'sharpe': 0, 'cagr': 0, 'max_drawdown': 0, 'total_return': 0}
        
        portfolio_returns = np.array(portfolio_returns)
        
        # Calculate metrics (EXACT same method)
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()
        
        # Annualize (assuming weekly rebalancing)
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
        
        # CAGR
        cumulative_return = np.prod(1 + portfolio_returns) - 1
        n_years = len(portfolio_returns) / 52
        cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 else 0
        
        # Max drawdown
        cumulative_wealth = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_wealth)
        drawdowns = (cumulative_wealth - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        return {
            'sharpe': sharpe,
            'cagr': cagr,
            'max_drawdown': max_drawdown,
            'total_return': cumulative_return,
            'n_periods': len(portfolio_returns)
        }
    
    def objective_function(self, trial, split):
        """Optuna objective function with PROPER validation"""
        
        # Conservative hyperparameter search space (around current good parameters)
        params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'verbose': -1,
            'random_state': 42,
            
            # Conservative ranges around current good parameters
            'n_estimators': trial.suggest_int('n_estimators', 8000, 20000, step=1000),
            'max_depth': trial.suggest_int('max_depth', 8, 14),
            'learning_rate': trial.suggest_float('learning_rate', 0.0001, 0.001, log=True),
            'num_leaves': trial.suggest_int('num_leaves', 200, 500),
            'subsample': trial.suggest_float('subsample', 0.4, 0.7),
            'min_child_samples': trial.suggest_int('min_child_samples', 400, 800),
            
            # Regularization (conservative)
            'lambda_l1': trial.suggest_float('lambda_l1', 0.005, 0.05),
            'lambda_l2': trial.suggest_float('lambda_l2', 0.01, 0.05),
            'min_split_gain': trial.suggest_float('min_split_gain', 0.003, 0.02),
            
            # Feature selection (conservative)
            'feature_fraction': trial.suggest_float('feature_fraction', 0.15, 0.35),
            'bagging_freq': trial.suggest_int('bagging_freq', 2, 5),
            
            # Other parameters (conservative)
            'max_bin': trial.suggest_int('max_bin', 500, 900),
            'min_data_in_bin': trial.suggest_int('min_data_in_bin', 20, 60),
        }
        
        try:
            # Prepare train/validation data with PROPER gaps
            train_mask = (self.data['Date'] >= split['train_start']) & (self.data['Date'] <= split['train_end'])
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])
            
            train_data = self.data[train_mask].copy()
            val_data = self.data[val_mask].copy()
            
            if len(train_data) == 0 or len(val_data) == 0:
                return -999
            
            # Prepare features and targets (EXACT same method)
            X_train = train_data[self.feature_cols].fillna(0)
            y_train = self.convert_to_relevance_scores(train_data['Weighted_MixRel'], train_data['Date'])
            train_groups = self.prepare_ranking_data(train_data['Date'])
            
            X_val = val_data[self.feature_cols].fillna(0)
            y_val = self.convert_to_relevance_scores(val_data['Weighted_MixRel'], val_data['Date'])
            val_groups = self.prepare_ranking_data(val_data['Date'])
            
            # Train model (NO early stopping to match proven system)
            train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
            val_dataset = lgb.Dataset(X_val, label=y_val, group=val_groups, reference=train_dataset)

            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Make predictions
            val_predictions = model.predict(X_val)
            
            # Calculate portfolio metrics (EXACT same method)
            metrics = self.calculate_portfolio_metrics(
                val_predictions, 
                val_data['Future5DReturn'], 
                val_data['Date']
            )
            
            # Return Sharpe ratio (primary objective)
            return metrics['sharpe']
            
        except Exception as e:
            print(f"Trial failed: {e}")
            return -999
    
    def optimize_hyperparameters(self):
        """Run PROPER hyperparameter optimization"""
        print("\n" + "="*60)
        print("🚀 PROPER HYPERPARAMETER OPTIMIZATION (NO DATA LEAKAGE)")
        print("="*60)
        
        # Load data and create splits
        self.load_and_prepare_data()
        self.create_proper_time_series_splits(n_splits=3)  # Use 3 folds
        
        all_results = []
        
        # Test each fold separately (like nested CV but simpler)
        for split in self.splits:
            print(f"\n📊 OPTIMIZING ON FOLD {split['fold']}")
            print(f"Train: {split['train_start'].date()} to {split['train_end'].date()}")
            print(f"Val: {split['val_start'].date()} to {split['val_end'].date()}")
            
            # Create Optuna study
            study = optuna.create_study(
                direction='maximize',
                sampler=optuna.samplers.TPESampler(seed=42)
            )
            
            # Optimize hyperparameters for this fold
            print(f"🔍 Running {self.n_trials} optimization trials...")
            
            study.optimize(
                lambda trial: self.objective_function(trial, split),
                n_trials=self.n_trials,
                show_progress_bar=True
            )
            
            # Store results
            fold_result = {
                'fold': split['fold'],
                'best_score': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials)
            }
            
            all_results.append(fold_result)
            
            print(f"✓ Fold {split['fold']} Best Sharpe: {study.best_value:.4f}")
        
        self.optimization_results = all_results
        return all_results

def main():
    # Initialize optimizer with FULL settings as requested
    optimizer = ProperHyperparameterOptimizer(n_trials=30)  # FULL 30 trials as requested
    
    # Run optimization
    results = optimizer.optimize_hyperparameters()
    
    # Analyze results
    print("\n" + "="*60)
    print("📊 PROPER OPTIMIZATION RESULTS")
    print("="*60)
    
    best_scores = [r['best_score'] for r in results]
    
    print(f"Fold results:")
    for result in results:
        print(f"  Fold {result['fold']}: Sharpe {result['best_score']:.4f}")
    
    print(f"\nAverage optimized Sharpe: {np.mean(best_scores):.4f} ± {np.std(best_scores):.4f}")
    print(f"Current baseline Sharpe: 2.187")
    
    if np.mean(best_scores) > 2.187:
        improvement = ((np.mean(best_scores) - 2.187) / 2.187) * 100
        print(f"🎯 Potential improvement: {improvement:.1f}%")
    else:
        print(f"📊 Current parameters are already well-optimized")
    
    # Show best parameters
    best_fold_idx = np.argmax(best_scores)
    best_params = results[best_fold_idx]['best_params']
    
    print(f"\n🏆 BEST PARAMETERS (Fold {best_fold_idx + 1}):")
    for param, value in best_params.items():
        print(f"  {param}: {value}")

if __name__ == "__main__":
    main()
