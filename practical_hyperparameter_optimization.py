#!/usr/bin/env python3
"""
PRACTICAL Hyperparameter Optimization - Fast but Proper
- No data leakage but reasonable speed
- Smaller parameter ranges for faster trials
- Early stopping for speed while maintaining quality
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import optuna
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class PracticalHyperparameterOptimizer:
    def __init__(self, n_trials=15):
        self.n_trials = n_trials
        self.best_params = None
        self.optimization_results = []
        
    def load_and_prepare_data(self):
        """Load data using same method but with recent subset for speed"""
        print("📊 Loading data (recent subset for practical optimization)...")
        
        # Load datasets
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        target_df = pd.read_csv('PureEURTarget.csv')
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        
        # Clean feature names
        import re
        name_mapping = {}
        for col in features_df.columns:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')
            name_mapping[col] = clean_name
        features_df = features_df.rename(columns=name_mapping)
        
        # Merge datasets
        data = features_df.merge(
            target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        data = data.merge(
            returns_df[['Date', 'P123 ID', 'Future5DReturn']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        # Use FULL 20 years as requested
        data['Date'] = pd.to_datetime(data['Date'])
        start_date = pd.to_datetime('2005-12-01')  # FULL 20 years
        end_date = pd.to_datetime('2025-05-01')
        
        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        data = data.sort_values('Date').reset_index(drop=True)
        
        # Feature columns
        feature_cols = [col for col in data.columns if col not in 
                       ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']]
        
        print(f"✓ Data: {len(data)} samples, {len(feature_cols)} features")
        print(f"✓ Date range: {data['Date'].min().date()} to {data['Date'].max().date()}")
        print(f"✓ FULL 20 years as requested")
        
        self.data = data
        self.feature_cols = feature_cols
        
        return data
    
    def create_practical_splits(self, n_splits=3, gap_months=6):
        """Create time series splits with proper gaps"""
        print(f"📅 Creating {n_splits} time series splits with {gap_months}-month gaps...")
        
        unique_dates = sorted(self.data['Date'].unique())
        total_dates = len(unique_dates)
        dates_per_split = total_dates // (n_splits + 1)
        gap_days = gap_months * 30
        
        splits = []
        
        for i in range(n_splits):
            train_end_idx = (i + 1) * dates_per_split
            train_end_date = unique_dates[min(train_end_idx, len(unique_dates) - 1)]
            
            val_start_date = train_end_date + timedelta(days=gap_days)
            val_start_idx = next((idx for idx, date in enumerate(unique_dates) 
                                if date >= val_start_date), len(unique_dates))
            
            val_end_idx = min(val_start_idx + dates_per_split, len(unique_dates) - 1)
            val_end_date = unique_dates[val_end_idx]
            
            if val_start_idx < len(unique_dates):
                splits.append({
                    'train_start': unique_dates[0],
                    'train_end': train_end_date,
                    'val_start': unique_dates[val_start_idx],
                    'val_end': val_end_date,
                    'fold': i + 1
                })
        
        print(f"✓ Created {len(splits)} splits with proper 6-month gaps")
        for split in splits:
            gap_days = (split['val_start'] - split['train_end']).days
            print(f"  Fold {split['fold']}: Gap = {gap_days} days")
        
        self.splits = splits
        return splits
    
    def convert_to_relevance_scores(self, targets, dates, n_levels=5):
        """Convert targets to relevance scores"""
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['relevance'] = 0
        
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']
            
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                valid_targets = date_targets[valid_mask]
                relevance_scores = pd.qcut(valid_targets, q=n_levels, labels=False, duplicates='drop')
                df.loc[date_mask & valid_mask, 'relevance'] = relevance_scores
        
        return df['relevance'].values
    
    def prepare_ranking_data(self, dates):
        """Prepare groups for ranking"""
        unique_dates = sorted(dates.unique())
        groups = []
        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)
        return groups
    
    def calculate_portfolio_metrics(self, predictions, returns, dates, top_k=20):
        """Calculate portfolio metrics"""
        portfolio_returns = []
        
        for date in sorted(dates.unique()):
            date_mask = dates == date
            date_predictions = predictions[date_mask]
            date_returns = returns[date_mask]
            
            if len(date_predictions) >= top_k:
                top_indices = np.argsort(date_predictions)[-top_k:]
                top_returns = date_returns.iloc[top_indices]
                portfolio_return = top_returns.mean() / 100.0
                portfolio_returns.append(portfolio_return)
        
        if len(portfolio_returns) == 0:
            return {'sharpe': 0}
        
        portfolio_returns = np.array(portfolio_returns)
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()
        
        # Annualize
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
        
        return {'sharpe': sharpe}
    
    def objective_function(self, trial, split):
        """Optuna objective function with practical parameters"""
        
        # PRACTICAL parameter ranges (smaller, faster)
        params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'verbose': -1,
            'random_state': 42,
            
            # Practical ranges around current good parameters
            'n_estimators': trial.suggest_int('n_estimators', 1000, 5000, step=500),
            'max_depth': trial.suggest_int('max_depth', 9, 13),
            'learning_rate': trial.suggest_float('learning_rate', 0.0002, 0.0008),
            'num_leaves': trial.suggest_int('num_leaves', 250, 400),
            'subsample': trial.suggest_float('subsample', 0.45, 0.65),
            'min_child_samples': trial.suggest_int('min_child_samples', 500, 750),
            
            # Regularization
            'lambda_l1': trial.suggest_float('lambda_l1', 0.01, 0.03),
            'lambda_l2': trial.suggest_float('lambda_l2', 0.015, 0.035),
            'min_split_gain': trial.suggest_float('min_split_gain', 0.005, 0.015),
            
            # Feature selection
            'feature_fraction': trial.suggest_float('feature_fraction', 0.2, 0.3),
            'bagging_freq': trial.suggest_int('bagging_freq', 2, 4),
            
            # Other parameters
            'max_bin': trial.suggest_int('max_bin', 600, 800),
            'min_data_in_bin': trial.suggest_int('min_data_in_bin', 30, 50),
        }
        
        try:
            # Prepare data
            train_mask = (self.data['Date'] >= split['train_start']) & (self.data['Date'] <= split['train_end'])
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])
            
            train_data = self.data[train_mask].copy()
            val_data = self.data[val_mask].copy()
            
            if len(train_data) == 0 or len(val_data) == 0:
                return -999
            
            # Prepare features and targets
            X_train = train_data[self.feature_cols].fillna(0)
            y_train = self.convert_to_relevance_scores(train_data['Weighted_MixRel'], train_data['Date'])
            train_groups = self.prepare_ranking_data(train_data['Date'])
            
            X_val = val_data[self.feature_cols].fillna(0)
            y_val = self.convert_to_relevance_scores(val_data['Weighted_MixRel'], val_data['Date'])
            val_groups = self.prepare_ranking_data(val_data['Date'])
            
            # Train model with NO early stopping as requested
            train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
            val_dataset = lgb.Dataset(X_val, label=y_val, group=val_groups, reference=train_dataset)

            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Make predictions
            val_predictions = model.predict(X_val)
            
            # Calculate portfolio metrics
            metrics = self.calculate_portfolio_metrics(
                val_predictions, 
                val_data['Future5DReturn'], 
                val_data['Date']
            )
            
            return metrics['sharpe']
            
        except Exception as e:
            print(f"Trial failed: {e}")
            return -999
    
    def optimize_hyperparameters(self):
        """Run practical hyperparameter optimization"""
        print("\n" + "="*60)
        print("🚀 FULL HYPERPARAMETER OPTIMIZATION (20 years, no early stopping, 30 trials)")
        print("="*60)
        
        # Load data and create splits
        self.load_and_prepare_data()
        self.create_practical_splits(n_splits=3)  # 3 folds for proper validation
        
        all_results = []
        
        # Test each fold
        for split in self.splits:
            print(f"\n📊 OPTIMIZING ON FOLD {split['fold']}")
            print(f"Train: {split['train_start'].date()} to {split['train_end'].date()}")
            print(f"Val: {split['val_start'].date()} to {split['val_end'].date()}")
            
            # Create Optuna study
            study = optuna.create_study(
                direction='maximize',
                sampler=optuna.samplers.TPESampler(seed=42)
            )
            
            # Optimize hyperparameters
            print(f"🔍 Running {self.n_trials} optimization trials...")
            
            study.optimize(
                lambda trial: self.objective_function(trial, split),
                n_trials=self.n_trials,
                show_progress_bar=True
            )
            
            # Store results
            fold_result = {
                'fold': split['fold'],
                'best_score': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials)
            }
            
            all_results.append(fold_result)
            
            print(f"✓ Fold {split['fold']} Best Sharpe: {study.best_value:.4f}")
        
        self.optimization_results = all_results
        return all_results

def main():
    # Initialize optimizer with FULL settings as requested
    optimizer = PracticalHyperparameterOptimizer(n_trials=30)
    
    # Run optimization
    results = optimizer.optimize_hyperparameters()
    
    # Analyze results
    print("\n" + "="*60)
    print("📊 PRACTICAL OPTIMIZATION RESULTS")
    print("="*60)
    
    best_scores = [r['best_score'] for r in results if r['best_score'] > 0]
    
    if len(best_scores) > 0:
        print(f"Fold results:")
        for result in results:
            if result['best_score'] > 0:
                print(f"  Fold {result['fold']}: Sharpe {result['best_score']:.4f}")
        
        print(f"\nAverage optimized Sharpe: {np.mean(best_scores):.4f} ± {np.std(best_scores):.4f}")
        print(f"Current baseline Sharpe: 2.187")
        
        if np.mean(best_scores) > 2.187:
            improvement = ((np.mean(best_scores) - 2.187) / 2.187) * 100
            print(f"🎯 Potential improvement: {improvement:.1f}%")
        else:
            print(f"📊 Current parameters are well-optimized")
        
        # Show best parameters
        best_fold_idx = np.argmax(best_scores)
        best_params = results[best_fold_idx]['best_params']
        
        print(f"\n🏆 BEST PARAMETERS (Fold {best_fold_idx + 1}):")
        for param, value in best_params.items():
            print(f"  {param}: {value}")
    else:
        print("❌ No successful optimization trials")

if __name__ == "__main__":
    main()
