"""
Factors data extracted from factors (4).csv
Contains formulaList and nameList with matching indices for API processing.
"""

# List of formulas - each formula corresponds to the name at the same index in nameList
formulaList = [
    "Ret%Chg(10)",
    "Ret%Chg(21)",
    "Ret%Chg(252)",
    "Ret%Chg(63)",
    "Ret%Chg(126)",
    'ZScore("IsNA((HighVal(252)/LowVal(252)-1) / PctDev(252,1),0)", #Sector, 7.5, NA, 2.5)',
    "Beta1Y",
    'CCI(20,0)',
    'close(0,GetSeries("IWM:USA"))',
    'FRank("Beta1Y", #All, #ASC) + FRank("PctDev(252,1)", #All, #ASC)',
    'Correl(1, 126, GetSeries("$VIX"))',
    'FHistRel("PctDev(21,1)", 52, 1)',
    "Eval(Ret%Chg(252)>0, HighValBar(252,0), LowValBar(252,0))",
    'ZScore("LoopStdDev(`Min(0, Ret%Chg(1, CTR))`, 63)", #Sector, 7.5, NA, 2.5)',
    "(SMA(50,0)/SMA(200,0)) - (SMA(50,21)/SMA(200,21))",
    "IsNA(SMA(50,0)/SMA(200,0), 1)",
    "Ret%Chg(21) - Ret%Chg(21, #Industry)",
    "Ret%Chg(63) - Ret%Chg(63, #Industry)",
    'FRank("Ret%Chg(252)") - FRank("Ret%Chg(21)")',
    "MACDD(12,26,9)",
    "IsNA(MACDD(12,26,9)/Close(0),0)",
    'ZScore("Ret%Chg(21) - Ret%Chg(63)", #Sector, 7.5, NA, 2.5)',
    'ZScore("Ret%Chg(63) - Ret%Chg(252) ", #Sector, 7.5, NA, 2.5)',
    'LoopSum("Close(CTR) > SMA(50,CTR)", 63)/63',
    'LoopSum("close(CTR)/close(CTR+1) > close(CTR, GetSeries(`IWM:USA`))/close(CTR+1, GetSeries(`IWM:USA`))", 63) / 63',
    'Eval(LinReg("Close(CTR)", 63), Slope, NA) - Eval(LinReg("Close(CTR)", 63, 63), Slope, NA)',
    'FHistRank("Eval(LinReg(`Close(CTR)`, 21), Slope, 0)", 52, 1) ',
    'Eval(LinReg("FHist(`Ret%Chg(21)`,CTR*4)", 13), R2, 0)',
    'ZScore("(HighVal(252) - LowVal(252)) / Close(0)", #Sector, 7.5, NA, 2.5)',
    'LoopAvg("Hi(CTR)-Low(CTR)",5)/LoopAvg("Hi(CTR)-Low(CTR)",63)',
    'PctDev(21,1)/Aggregate("PctDev(21,1)",#Industry)',
    'PctDev(252,1)/Aggregate("PctDev(252,1)",#Industry)',
    "(Close(0)/Close(5)) / (Close(0, #Industry)/Close(5, #Industry))",
    'LoopSum("Eval(Ret%Chg(1,CTR) > 0 AND Ret%Chg(1,CTR+1) < 0, 1, Eval(Ret%Chg(1,CTR) < 0 AND Ret%Chg(1,CTR+1) > 0, 1, 0))", 63)/63',
    "RSI(14,0)",
    'Ret%Chg(252) / (LoopStdDev("Min(0, Ret%Chg(1, CTR))", 252) + 0.01)',
    "ADX(14,0)",
    "ADX(14,0) - ADX(14,21)",
    'FHistZScore("PctDev(21,1)", 52, 1)',
    "(Close(0) - SMA(50,0)) / IsNA(PctDev(50,1),1)"
]

# List of names - each name corresponds to the formula at the same index in formulaList
nameList = [
    "Abs_Return_10D",
    "Abs_Return_1M",
    "Abs_Return_1Y",
    "Abs_Return_3M",
    "Abs_Return_6M",
    "Annual_Range_per_Unit_of_Vol",
    "Beta_vs_Bench_1Y",
    "CCI_20D",
    "IWM_Close",
    "Composite_Low_Risk_Rank",
    "Correl_vs_VIX_6M",
    "Current_Vol_vs_Annual_Range",
    "Days_Since_Peak_or_Trough",
    "Downside_Deviation_3M",
    "Golden_Cross_Momentum_1M",
    "Golden_Cross_Ratio",
    "Idio_Return_1M_vs_Industry",
    "Idio_Return_3M_vs_Industry",
    "Long_vs_Short_Momentum_Rank_Diff",
    "MACD_Histogram",
    "MACD_Histogram_Normalized",
    "Mom_Accel_1M_vs_3M",
    "Mom_Accel_3M_vs_1Y",
    "Pct_Days_Above_50dMA_3M",
    "Pct_Days_Outperforming_Bench_3M",
    "Price_Trend_Slope_Accel_3M",
    "Price_Trend_Slope_Rank_vs_1Y_Hist",
    "R2_of_Monthly_Returns_1Y",
    "Range_52Wk_as_Pct_of_Price",
    "Range_Contraction_Ratio_1W_vs_3M",
    "Realized_Vol_1M",
    "Realized_Vol_1Y",
    "Relative_Strength_vs_Industry_1W",
    "Return_Sign_Flip_Frequency_3M",
    "RSI_14D",
    "Sortino_Ratio_Proxy_1Y",
    "Trend_Strength_ADX14",
    "Trend_Strength_Momentum_ADX14",
    "Vol_Surprise_vs_1Y_History",
    "ZScore_of_Price_vs_SMA50"
]

# Verify that both lists have the same length
assert len(formulaList) == len(nameList), f"Lists have different lengths: formulaList={len(formulaList)}, nameList={len(nameList)}"

# Print confirmation
print(f"Successfully loaded {len(formulaList)} factors")
print("Formula and name lists are ready for API processing with matching indices")
