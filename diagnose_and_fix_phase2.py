#!/usr/bin/env python3
"""
Diagnose Phase 2 issues and create a fixed version
WITHOUT losing the 24+ hours of Phase 1 data
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def diagnose_phase2_issue():
    """Diagnose why Phase 2 is getting 'No further splits' warnings"""
    print("="*60)
    print("🔍 DIAGNOSING PHASE 2 ISSUES")
    print("="*60)
    
    print("The warnings 'No further splits with positive gain, best gain: -inf' indicate:")
    print("1. 🚨 Over-regularization: lambda_l1/l2 too high for 14,500 estimators")
    print("2. 🚨 Min split gain too high: Prevents any beneficial splits")
    print("3. 🚨 Feature fraction too low: Not enough features to work with")
    print("4. 🚨 Min child samples too high: Prevents small but useful splits")
    
    print(f"\n💡 SOLUTION:")
    print("Adjust regularization parameters for Phase 2 (full 14,500 estimators):")
    print("- Reduce lambda_l1 and lambda_l2 by 50-70%")
    print("- Reduce min_split_gain by 50%") 
    print("- Increase feature_fraction slightly")
    print("- Reduce min_child_samples slightly")
    
    return True

def create_fixed_phase2_parameters(original_params):
    """Create Phase 2 parameters that avoid over-regularization"""
    
    fixed_params = original_params.copy()
    
    # Reduce regularization for full training
    if 'lambda_l1' in fixed_params:
        fixed_params['lambda_l1'] = max(0.001, fixed_params['lambda_l1'] * 0.3)  # Reduce by 70%
    
    if 'lambda_l2' in fixed_params:
        fixed_params['lambda_l2'] = max(0.001, fixed_params['lambda_l2'] * 0.3)  # Reduce by 70%
    
    if 'min_split_gain' in fixed_params:
        fixed_params['min_split_gain'] = max(0.001, fixed_params['min_split_gain'] * 0.5)  # Reduce by 50%
    
    if 'feature_fraction' in fixed_params:
        fixed_params['feature_fraction'] = min(0.8, fixed_params['feature_fraction'] * 1.2)  # Increase by 20%
    
    if 'min_child_samples' in fixed_params:
        fixed_params['min_child_samples'] = max(100, int(fixed_params['min_child_samples'] * 0.8))  # Reduce by 20%
    
    # Set to full estimators
    fixed_params['n_estimators'] = 14500
    
    return fixed_params

def simulate_top_phase1_results():
    """Simulate what the top Phase 1 results would look like"""
    
    # Based on the terminal output, here are some top performing parameter sets
    top_trials = [
        {
            'score': 3.051415080707989,
            'params': {
                'n_estimators': 500,
                'max_depth': 15,
                'learning_rate': 0.0004991234041800156,
                'num_leaves': 422,
                'subsample': 0.4898830830169697,
                'min_child_samples': 438,
                'lambda_l1': 0.007300334311996499,
                'lambda_l2': 0.03621232459824884,
                'min_split_gain': 0.006946077212869926,
                'feature_fraction': 0.6091782242710134,
                'bagging_freq': 4,
                'max_bin': 557,
                'min_data_in_bin': 82,
                'path_smooth': 4.7056143235745726e-09,
                'bin_construct_sample_cnt': 300000
            }
        },
        {
            'score': 3.013259768741074,
            'params': {
                'n_estimators': 3000,
                'max_depth': 16,
                'learning_rate': 0.0029106359131330704,
                'num_leaves': 399,
                'subsample': 0.37800932022121825,
                'min_child_samples': 324,
                'lambda_l1': 0.0013066739238053278,
                'lambda_l2': 0.05399484409787434,
                'min_split_gain': 0.010502105436744277,
                'feature_fraction': 0.5956508044572318,
                'bagging_freq': 1,
                'max_bin': 976,
                'min_data_in_bin': 84,
                'path_smooth': 7.068974950624601e-10,
                'bin_construct_sample_cnt': 300000
            }
        },
        {
            'score': 3.012500485397324,
            'params': {
                'n_estimators': 7000,
                'max_depth': 16,
                'learning_rate': 0.001839632003227772,
                'num_leaves': 504,
                'subsample': 0.5223153828197415,
                'min_child_samples': 278,
                'lambda_l1': 0.0012648547125075843,
                'lambda_l2': 0.012132639708363495,
                'min_split_gain': 0.008494323052024926,
                'feature_fraction': 0.7090436108729318,
                'bagging_freq': 5,
                'max_bin': 815,
                'min_data_in_bin': 94,
                'path_smooth': 3.3682198169608013e-09,
                'bin_construct_sample_cnt': 300000
            }
        }
    ]
    
    return top_trials

def test_fixed_parameters():
    """Test if the fixed parameters resolve the issue"""
    print("\n" + "="*60)
    print("🧪 TESTING FIXED PARAMETERS")
    print("="*60)
    
    top_trials = simulate_top_phase1_results()
    
    for i, trial in enumerate(top_trials):
        print(f"\n📊 Testing Trial {i+1} (Phase 1 score: {trial['score']:.4f})")
        
        # Original parameters (causing warnings)
        original_params = trial['params'].copy()
        original_params.update({
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'verbose': -1,
            'random_state': 42,
            'n_estimators': 14500
        })
        
        # Fixed parameters
        fixed_params = create_fixed_phase2_parameters(original_params)
        
        print(f"Original regularization:")
        print(f"  lambda_l1: {original_params.get('lambda_l1', 'N/A')}")
        print(f"  lambda_l2: {original_params.get('lambda_l2', 'N/A')}")
        print(f"  min_split_gain: {original_params.get('min_split_gain', 'N/A')}")
        print(f"  feature_fraction: {original_params.get('feature_fraction', 'N/A')}")
        
        print(f"Fixed regularization:")
        print(f"  lambda_l1: {fixed_params.get('lambda_l1', 'N/A')} (reduced)")
        print(f"  lambda_l2: {fixed_params.get('lambda_l2', 'N/A')} (reduced)")
        print(f"  min_split_gain: {fixed_params.get('min_split_gain', 'N/A')} (reduced)")
        print(f"  feature_fraction: {fixed_params.get('feature_fraction', 'N/A')} (increased)")
        
        print(f"✓ Parameters adjusted for full 14,500 estimator training")

def main():
    print("="*60)
    print("🚨 PHASE 2 DIAGNOSTIC REPORT")
    print("="*60)
    
    # Diagnose the issue
    diagnose_phase2_issue()
    
    # Test parameter fixes
    test_fixed_parameters()
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print("1. ✅ Phase 1 completed successfully with excellent results (3.05+ Sharpe)")
    print("2. 🔧 Phase 2 needs parameter adjustment for 14,500 estimators")
    print("3. 💡 Create a separate script to run Phase 2 with fixed parameters")
    print("4. 📊 Use the top 10-20 trials from Phase 1 with adjusted regularization")
    print("5. ⏰ This will save the 24+ hours of Phase 1 optimization work")
    
    print(f"\n✅ NEXT STEPS:")
    print("1. Let current process finish (it will complete despite warnings)")
    print("2. Extract top trials from Phase 1 results")
    print("3. Run Phase 2 with properly adjusted parameters")
    print("4. Get final optimized hyperparameters without data loss")

if __name__ == "__main__":
    main()
