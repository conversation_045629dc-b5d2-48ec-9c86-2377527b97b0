#!/usr/bin/env python3
"""
Exact copy of LOFEP validation methodology, but for backward elimination
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any
from sklearn.metrics import mean_squared_error

def setup_logging():
    """Setup logging exactly like LOFEP"""
    os.makedirs('backward_elimination_results', exist_ok=True)
    
    logger = logging.getLogger('BackwardElimination')
    logger.setLevel(logging.INFO)
    logger.handlers.clear()
    
    log_file = f'backward_elimination_results/backward_elimination_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def clean_feature_name(name: str) -> str:
    """Clean feature names exactly like LOFEP"""
    cleaned = str(name)
    
    replacements = {
        ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_', 
        '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_', 
        '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
        '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
        '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
        '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)
    cleaned = re.sub(r'_+', '_', cleaned)
    cleaned = cleaned.strip('_')
    
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    
    if not cleaned:
        cleaned = 'Feature_Unknown'
    
    return cleaned

def create_time_splits(dates: pd.Series, train_months: int, val_months: int) -> List[Tuple]:
    """Create time splits exactly like LOFEP"""
    splits = []
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    current_date = min_date
    while current_date < max_date:
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=val_months)
        
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        current_date = val_start
    
    return splits

def time_series_cross_validation(X: pd.DataFrame, y: pd.Series, dates: pd.Series, 
                                returns: pd.Series, config: Dict, feature_name: str = "UNKNOWN") -> Dict[str, float]:
    """Exact copy of LOFEP validation methodology"""
    
    logger = logging.getLogger('BackwardElimination')
    logger.info(f"Running time series CV for: {feature_name}")
    
    # Create time splits exactly like LOFEP
    train_months = config['validation']['train_months']
    val_months = config['validation']['validation_months']
    
    splits = create_time_splits(dates, train_months, val_months)
    
    if len(splits) < config['validation']['min_periods']:
        logger.warning(f"Insufficient validation periods: {len(splits)} < {config['validation']['min_periods']}")
        return {'mean_sharpe_ratio': 0.0, 'std_sharpe_ratio': 0.0}
    
    # Store results for each fold
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
        try:
            # Create masks for this fold
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            # Check if we have enough data
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                logger.warning(f"Insufficient data in fold {fold_idx}: train={train_mask.sum()}, val={val_mask.sum()}")
                continue
            
            # Prepare data for this fold - EXACT same as LOFEP
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()

            # Robust data cleaning - EXACT same as LOFEP
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())

            X_train = X_train.replace([np.inf, -np.inf], np.nan)
            X_val = X_val.replace([np.inf, -np.inf], np.nan)
            X_train = X_train.fillna(0)
            X_val = X_val.fillna(0)

            # Remove NaN from target
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()

            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]

            # Final validation
            if X_train.isnull().any().any() or X_val.isnull().any().any():
                logger.warning(f"Still have NaN values in fold {fold_idx}")
                continue

            if np.isinf(X_train.values).any() or np.isinf(X_val.values).any():
                logger.warning(f"Still have infinite values in fold {fold_idx}")
                continue
            
            # Train LightGBM model - EXACT same as LOFEP
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)

            # Get corresponding returns and dates for performance calculation
            val_returns = returns[val_mask][val_valid_mask]
            val_dates = dates[val_mask][val_valid_mask]

            # Calculate performance metrics - EXACT same as LOFEP
            fold_performance = calculate_fold_performance(y_val, y_pred, val_returns, val_dates, logger)
            fold_performance['fold'] = fold_idx
            
            fold_results.append(fold_performance)
            
            logger.debug(f"Fold {fold_idx}: Sharpe={fold_performance['sharpe_ratio']:.4f}")
            
        except Exception as e:
            logger.warning(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    if not fold_results:
        logger.error("No successful validation folds")
        return {'mean_sharpe_ratio': 0.0, 'std_sharpe_ratio': 0.0}
    
    # Aggregate results across folds - EXACT same as LOFEP
    results_df = pd.DataFrame(fold_results)
    
    aggregated_results = {
        'mean_sharpe_ratio': results_df['sharpe_ratio'].mean(),
        'std_sharpe_ratio': results_df['sharpe_ratio'].std(),
        'mean_cagr': results_df['cagr'].mean(),
        'num_folds': len(fold_results),
        'feature_name': feature_name
    }
    
    logger.info(f"CV completed for {feature_name}: Sharpe={aggregated_results['mean_sharpe_ratio']:.4f} ± {aggregated_results['std_sharpe_ratio']:.4f}")
    
    return aggregated_results

def calculate_fold_performance(y_true: pd.Series, y_pred: np.ndarray, 
                             actual_returns: pd.Series, val_dates: pd.Series, logger) -> Dict[str, float]:
    """Calculate performance metrics exactly like LOFEP"""
    
    predictions = pd.Series(y_pred, index=y_true.index)
    returns_data = actual_returns
    
    # Remove NaN values
    valid_mask = ~(returns_data.isna() | predictions.isna())
    returns_data = returns_data[valid_mask]
    predictions = predictions[valid_mask]
    val_dates = val_dates[valid_mask]

    if len(returns_data) == 0:
        return {'sharpe_ratio': 0.0, 'cagr': 0.0}

    # Proper portfolio construction - EXACT same as LOFEP
    portfolio_returns = []
    unique_dates = sorted(val_dates.unique())

    for date in unique_dates:
        date_mask = val_dates == date
        date_predictions = predictions[date_mask]
        date_returns = returns_data[date_mask]

        if len(date_predictions) >= 20:  # Need at least 20 stocks
            # Get top 20 stocks by prediction
            top_indices = date_predictions.nlargest(20).index
            top_returns = date_returns[top_indices]

            # Equal-weighted portfolio return (convert percentage to decimal)
            portfolio_return = top_returns.mean() / 100.0
            portfolio_returns.append(portfolio_return)

    if len(portfolio_returns) == 0:
        return {'sharpe_ratio': 0.0, 'cagr': 0.0}

    # Calculate performance metrics - EXACT same as LOFEP
    portfolio_returns = np.array(portfolio_returns)
    
    mean_return = portfolio_returns.mean()
    volatility = portfolio_returns.std()

    # Annualize (5-day returns, approximately 52 periods per year)
    periods_per_year = 52
    annual_return = mean_return * periods_per_year
    annual_volatility = volatility * np.sqrt(periods_per_year)
    sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0

    # CAGR
    cumulative_return = np.prod(1 + portfolio_returns) - 1
    n_years = len(portfolio_returns) / periods_per_year
    cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 and cumulative_return > -0.99 else 0

    return {
        'sharpe_ratio': sharpe,
        'cagr': cagr
    }

def run_exact_lofep_backward_elimination():
    """Run backward elimination using exact LOFEP methodology"""
    
    logger = setup_logging()
    logger.info("🔄 EXACT LOFEP BACKWARD ELIMINATION")
    logger.info("="*60)
    
    # Load LOFEP config
    with open('lofep_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Load data exactly like LOFEP
    logger.info("Loading data...")
    features_df = pd.read_csv(config['data']['features_file'])
    target_df = pd.read_csv(config['data']['target_file'])
    returns_df = pd.read_csv(config['data']['returns_file'])
    
    # Merge data exactly like LOFEP
    merged_data = features_df.merge(target_df, on=['Date', 'P123 ID'], how='inner')
    merged_data = merged_data.merge(returns_df, on=['Date', 'P123 ID'], how='inner')
    
    logger.info(f"Merged data shape: {merged_data.shape}")
    
    # Get original features exactly like LOFEP baseline
    exclude_cols = [
        config['data']['date_column'],
        config['data']['id_column'], 
        config['data']['ticker_column'],
        config['data']['target_column'],
        config['data']['returns_column']
    ]
    
    original_features = [col for col in merged_data.columns 
                        if col not in exclude_cols and 
                        merged_data[col].dtype in ['int64', 'float64']]
    
    logger.info(f"Original features: {len(original_features)}")
    
    # Clean feature names exactly like LOFEP
    feature_mapping = {}
    for feature in original_features:
        cleaned_name = clean_feature_name(feature)
        feature_mapping[feature] = cleaned_name
    
    # Establish baseline exactly like LOFEP
    logger.info("Establishing baseline performance...")
    
    X_baseline = merged_data[original_features].copy()
    X_baseline.columns = [feature_mapping[col] for col in X_baseline.columns]
    X_baseline = X_baseline.fillna(0)
    
    y = merged_data[config['data']['target_column']]
    dates = pd.to_datetime(merged_data[config['data']['date_column']])
    returns = merged_data[config['data']['returns_column']]
    
    # Run baseline validation exactly like LOFEP
    baseline_performance = time_series_cross_validation(
        X_baseline, y, dates, returns, config, feature_name="BASELINE"
    )
    
    baseline_sharpe = baseline_performance.get('mean_sharpe_ratio', 0)
    baseline_std = baseline_performance.get('std_sharpe_ratio', 0)
    
    logger.info(f"Baseline Sharpe Ratio: {baseline_sharpe:.4f} ± {baseline_std:.4f}")
    
    # Now test removing each feature one by one
    logger.info(f"Testing removal of {len(original_features)} features...")
    
    removal_results = {}
    
    for i, feature_to_remove in enumerate(original_features[:10]):  # Test first 10 for now
        try:
            logger.info(f"[{i+1:3d}/{len(original_features)}] Testing removal of: {feature_to_remove}")
            
            # Create feature set without this feature
            features_without_removed = [f for f in original_features if f != feature_to_remove]
            
            X_test = merged_data[features_without_removed].copy()
            
            # Clean feature names
            test_feature_mapping = {}
            for feature in features_without_removed:
                cleaned_name = clean_feature_name(feature)
                test_feature_mapping[feature] = cleaned_name
            
            X_test.columns = [test_feature_mapping[col] for col in X_test.columns]
            X_test = X_test.fillna(0)
            
            # Test performance without this feature
            result = time_series_cross_validation(
                X_test, y, dates, returns, config, feature_name=f"REMOVED_{feature_to_remove}"
            )
            
            if result:
                removal_results[feature_to_remove] = result
                new_sharpe = result['mean_sharpe_ratio']
                improvement = new_sharpe - baseline_sharpe
                
                logger.info(f"    Baseline: {baseline_sharpe:.4f} → New: {new_sharpe:.4f} (Δ{improvement:+.4f})")
                
                if improvement > 0.01:
                    logger.info(f"    ✅ REMOVING HELPS: {improvement:+.4f} Sharpe improvement")
                elif improvement < -0.01:
                    logger.info(f"    ❌ REMOVING HURTS: {improvement:+.4f} Sharpe impact")
                else:
                    logger.info(f"    ⚪ NEUTRAL: {improvement:+.4f} Sharpe impact")
        
        except Exception as e:
            logger.error(f"Error testing {feature_to_remove}: {str(e)}")
            continue
    
    # Save results
    final_results = {
        'baseline_sharpe': baseline_sharpe,
        'baseline_std': baseline_std,
        'removal_results': removal_results,
        'timestamp': datetime.now().isoformat()
    }
    
    with open('backward_elimination_results/exact_lofep_results.json', 'w') as f:
        json.dump(final_results, f, indent=2, default=str)
    
    logger.info("Results saved to backward_elimination_results/exact_lofep_results.json")

if __name__ == "__main__":
    run_exact_lofep_backward_elimination()
