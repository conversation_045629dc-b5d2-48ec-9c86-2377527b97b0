#!/usr/bin/env python3
"""
Investigate potential data leakage in hyperparameter optimization
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def investigate_leakage():
    print("="*60)
    print("🔍 INVESTIGATING POTENTIAL DATA LEAKAGE")
    print("="*60)
    
    # Load the data used in optimization
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    # Clean feature names
    import re
    name_mapping = {}
    for col in features_df.columns:
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
        clean_name = re.sub(r'_+', '_', clean_name)
        clean_name = clean_name.strip('_')
        name_mapping[col] = clean_name
    features_df = features_df.rename(columns=name_mapping)
    
    # Merge datasets
    data = features_df.merge(
        target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
        left_on=['Date', 'P123_ID'], 
        right_on=['Date', 'P123 ID'],
        how='inner'
    )
    
    data = data.merge(
        returns_df[['Date', 'P123 ID', 'Future5DReturn']], 
        left_on=['Date', 'P123_ID'], 
        right_on=['Date', 'P123 ID'],
        how='inner'
    )
    
    # Filter to optimization period
    data['Date'] = pd.to_datetime(data['Date'])
    start_date = pd.to_datetime('2010-01-01')
    end_date = pd.to_datetime('2023-12-31')
    
    data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
    data = data.sort_values('Date').reset_index(drop=True)
    
    print(f"Optimization data: {len(data)} samples")
    print(f"Date range: {data['Date'].min().date()} to {data['Date'].max().date()}")
    
    # Check the splits used in optimization
    print(f"\n🔍 CHECKING OPTIMIZATION SPLITS:")
    
    # Recreate the splits from the optimization code
    unique_dates = sorted(data['Date'].unique())
    total_dates = len(unique_dates)
    n_outer_folds = 3
    
    print(f"Total unique dates: {total_dates}")
    
    # Outer splits
    outer_splits = []
    dates_per_outer_fold = total_dates // (n_outer_folds + 1)
    
    for i in range(n_outer_folds):
        train_end_idx = (i + 1) * dates_per_outer_fold
        test_start_idx = train_end_idx + 26  # 6-month gap (26 weeks)
        test_end_idx = min(test_start_idx + dates_per_outer_fold, total_dates)
        
        if test_start_idx < total_dates:
            outer_splits.append({
                'fold': i + 1,
                'train_end': unique_dates[train_end_idx],
                'test_start': unique_dates[test_start_idx],
                'test_end': unique_dates[test_end_idx - 1] if test_end_idx < total_dates else unique_dates[-1]
            })
    
    print(f"\nOuter splits:")
    for split in outer_splits:
        print(f"  Fold {split['fold']}: Train until {split['train_end'].date()}, Test {split['test_start'].date()} to {split['test_end'].date()}")
        
        # Check gap
        gap_days = (split['test_start'] - split['train_end']).days
        print(f"    Gap: {gap_days} days ({gap_days/7:.1f} weeks)")
    
    # Check inner splits for first fold
    print(f"\n🔍 CHECKING INNER SPLITS (Fold 1):")
    
    first_split = outer_splits[0]
    train_mask = data['Date'] <= first_split['train_end']
    outer_train_data = data[train_mask]
    
    # Inner split (80% for inner train)
    inner_split_date = outer_train_data['Date'].quantile(0.8)
    inner_train_data = outer_train_data[outer_train_data['Date'] <= inner_split_date]
    inner_val_data = outer_train_data[outer_train_data['Date'] > inner_split_date]
    
    print(f"Inner train: {inner_train_data['Date'].min().date()} to {inner_train_data['Date'].max().date()}")
    print(f"Inner val: {inner_val_data['Date'].min().date()} to {inner_val_data['Date'].max().date()}")
    
    # Check for potential leakage
    print(f"\n⚠️  POTENTIAL LEAKAGE ISSUES:")
    
    # Issue 1: No gap in inner split
    inner_gap = (inner_val_data['Date'].min() - inner_train_data['Date'].max()).days
    print(f"1. Inner split gap: {inner_gap} days")
    if inner_gap <= 7:
        print(f"   🚨 LEAKAGE RISK: No gap between inner train/val!")
    
    # Issue 2: Check if we're using future returns correctly
    print(f"\n2. Future returns analysis:")
    sample_date = inner_train_data['Date'].iloc[-100]  # Sample from end of inner train
    sample_data = inner_train_data[inner_train_data['Date'] == sample_date]
    
    if len(sample_data) > 0:
        print(f"   Sample date: {sample_date.date()}")
        print(f"   Future5DReturn values: {sample_data['Future5DReturn'].describe()}")
        
        # Check if Future5DReturn is actually 5 days in the future
        next_week_date = sample_date + timedelta(days=7)
        print(f"   Next week would be: {next_week_date.date()}")
        
        # Check if we have data for that date
        next_week_data = data[data['Date'] == next_week_date]
        if len(next_week_data) > 0:
            print(f"   ✓ Next week data exists")
        else:
            print(f"   ⚠️ Next week data missing")
    
    # Issue 3: Check portfolio calculation method
    print(f"\n3. Portfolio calculation analysis:")
    print("   Current method: Group by date, select top 20, equal weight")
    print("   Potential issue: Using same-date returns for same-date predictions")
    print("   🚨 MAJOR LEAKAGE: We should use NEXT period returns, not current period!")
    
    # Issue 4: Check target vs returns relationship
    print(f"\n4. Target vs Returns relationship:")
    sample_data = data.sample(1000, random_state=42)
    correlation = sample_data['Weighted_MixRel'].corr(sample_data['Future5DReturn'])
    print(f"   Correlation between target and future returns: {correlation:.4f}")
    
    if abs(correlation) > 0.3:
        print(f"   🚨 HIGH CORRELATION: Target may be using future information!")
    
    # Issue 5: Check date alignment
    print(f"\n5. Date alignment check:")
    print("   Features date: When features are calculated")
    print("   Target date: When target score is assigned") 
    print("   Returns date: When the 'future' return is measured")
    print("   🚨 CRITICAL: All three should be for SAME date, but returns should be FUTURE returns")
    
    print(f"\n🎯 LIKELY CAUSES OF INFLATED PERFORMANCE:")
    print("1. 🚨 NO GAP in inner CV splits - using adjacent weeks")
    print("2. 🚨 SAME-DATE returns - not truly 'future' returns")
    print("3. 🚨 SHORTER TIME PERIOD - 2010-2023 vs 2005-2025")
    print("4. 🚨 DIFFERENT VALIDATION METHODOLOGY")
    
    print(f"\n💡 FIXES NEEDED:")
    print("1. Add 6-month gap to inner CV splits")
    print("2. Use returns from NEXT period, not current period")
    print("3. Verify target/returns are truly forward-looking")
    print("4. Use same time period as main validation")
    print("5. Use same 10-fold methodology")

if __name__ == "__main__":
    investigate_leakage()
