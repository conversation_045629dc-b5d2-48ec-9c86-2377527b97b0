#!/usr/bin/env python3
"""
Debug the feature removal process to find the bug
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility"""
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', str(name))
    cleaned = re.sub(r'_+', '_', cleaned).strip('_')
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    return cleaned or 'Feature_Unknown'

def simple_validation_test(X, y, dates, returns, test_name):
    """Simple validation test with fixed methodology"""
    
    print(f"\n🧪 Testing: {test_name}")
    print(f"Dataset shape: {X.shape}")
    print(f"Features: {list(X.columns)[:5]}... (showing first 5)")
    
    # Use only first 3 time periods for speed and consistency
    unique_dates = sorted(dates.unique())
    
    # Fixed splits for consistency
    split1_train = unique_dates[:90]  # First 90 dates
    split1_val = unique_dates[90:120]  # Next 30 dates
    
    split2_train = unique_dates[120:210]  # Next 90 dates  
    split2_val = unique_dates[210:240]   # Next 30 dates
    
    split3_train = unique_dates[240:330]  # Next 90 dates
    split3_val = unique_dates[330:360]    # Next 30 dates
    
    splits = [
        (split1_train, split1_val),
        (split2_train, split2_val), 
        (split3_train, split3_val)
    ]
    
    fold_results = []
    
    for fold_idx, (train_dates, val_dates) in enumerate(splits):
        try:
            # Create masks
            train_mask = dates.isin(train_dates)
            val_mask = dates.isin(val_dates)
            
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Get data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(0)
            X_val = X_val.fillna(0)
            
            # Remove NaN targets
            train_valid = ~y_train.isna()
            val_valid = ~y_val.isna()
            
            X_train = X_train[train_valid]
            y_train = y_train[train_valid]
            X_val = X_val[val_valid]
            y_val = y_val[val_valid]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            print(f"  Fold {fold_idx}: Train={len(X_train)}, Val={len(X_val)}")
            
            # Train model with fixed parameters
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42  # Fixed seed!
            }
            
            model = lgb.train(
                params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=50,  # Fixed rounds
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate portfolio performance
            val_returns_data = returns[val_mask][val_valid]
            val_dates_data = dates[val_mask][val_valid]
            
            portfolio_returns = []
            
            for date in sorted(val_dates_data.unique()):
                date_mask = val_dates_data == date
                date_predictions = y_pred[date_mask]
                date_returns = val_returns_data[date_mask]
                
                if len(date_predictions) >= 15:
                    top_indices = np.argsort(date_predictions)[-15:]
                    top_returns = date_returns.iloc[top_indices]
                    portfolio_return = top_returns.mean() / 100.0
                    portfolio_returns.append(portfolio_return)
            
            if len(portfolio_returns) > 0:
                portfolio_returns = np.array(portfolio_returns)
                mean_return = portfolio_returns.mean()
                volatility = portfolio_returns.std()
                
                annual_return = mean_return * 52
                annual_volatility = volatility * np.sqrt(52)
                sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
                
                fold_results.append(sharpe)
                print(f"    Sharpe: {sharpe:.4f}")
        
        except Exception as e:
            print(f"    Error in fold {fold_idx}: {str(e)}")
            continue
    
    if fold_results:
        mean_sharpe = np.mean(fold_results)
        print(f"  📊 Mean Sharpe: {mean_sharpe:.4f} (from {len(fold_results)} folds)")
        return mean_sharpe
    else:
        print(f"  ❌ No successful folds")
        return None

def debug_feature_removal():
    """Debug the feature removal process"""
    
    print("🔍 DEBUGGING FEATURE REMOVAL PROCESS")
    print("="*60)
    
    # Load data
    print("Loading data...")
    enhanced_data = pd.read_csv('enhanced_dataset_top50_features.csv')
    
    # Prepare data
    exclude_cols = ['Date', 'P123 ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn', 'Market_Cap_Quintile']
    
    all_features = [col for col in enhanced_data.columns 
                   if col not in exclude_cols and 
                   enhanced_data[col].dtype in ['int64', 'float64']]
    
    print(f"Total features: {len(all_features)}")
    
    # Clean feature names
    feature_mapping = {}
    for feature in all_features:
        cleaned_name = clean_feature_name(feature)
        feature_mapping[feature] = cleaned_name
    
    # Prepare baseline data
    X_baseline = enhanced_data[all_features].copy()
    X_baseline.columns = [feature_mapping[col] for col in X_baseline.columns]
    X_baseline = X_baseline.fillna(0)
    X_baseline = X_baseline.replace([np.inf, -np.inf], 0)
    
    y = enhanced_data['Weighted_MixRel']
    dates = pd.to_datetime(enhanced_data['Date'])
    returns = enhanced_data['Future5DReturn']
    
    # Test 1: Baseline with all features
    baseline_sharpe = simple_validation_test(X_baseline, y, dates, returns, "BASELINE (All Features)")
    
    # Test 2: Remove first feature (Abs_Return_10D)
    feature_to_remove = all_features[0]  # Should be 'Abs_Return_10D'
    print(f"\n🎯 Testing removal of: {feature_to_remove}")
    
    # Create dataset without this feature
    features_without_removed = [f for f in all_features if f != feature_to_remove]
    print(f"Features before removal: {len(all_features)}")
    print(f"Features after removal: {len(features_without_removed)}")
    print(f"Feature actually removed: {feature_to_remove in features_without_removed}")
    
    X_removed = enhanced_data[features_without_removed].copy()
    
    # Clean feature names for removed dataset
    removed_feature_mapping = {}
    for feature in features_without_removed:
        cleaned_name = clean_feature_name(feature)
        removed_feature_mapping[feature] = cleaned_name
    
    X_removed.columns = [removed_feature_mapping[col] for col in X_removed.columns]
    X_removed = X_removed.fillna(0)
    X_removed = X_removed.replace([np.inf, -np.inf], 0)
    
    removed_sharpe = simple_validation_test(X_removed, y, dates, returns, f"REMOVED {feature_to_remove}")
    
    # Test 3: Verify baseline is reproducible
    baseline_sharpe2 = simple_validation_test(X_baseline, y, dates, returns, "BASELINE (Repeat)")
    
    # Test 4: Remove a different feature
    feature_to_remove2 = all_features[10]  # Different feature
    features_without_removed2 = [f for f in all_features if f != feature_to_remove2]
    
    X_removed2 = enhanced_data[features_without_removed2].copy()
    removed_feature_mapping2 = {}
    for feature in features_without_removed2:
        cleaned_name = clean_feature_name(feature)
        removed_feature_mapping2[feature] = cleaned_name
    
    X_removed2.columns = [removed_feature_mapping2[col] for col in X_removed2.columns]
    X_removed2 = X_removed2.fillna(0)
    X_removed2 = X_removed2.replace([np.inf, -np.inf], 0)
    
    removed_sharpe2 = simple_validation_test(X_removed2, y, dates, returns, f"REMOVED {feature_to_remove2}")
    
    # Analysis
    print(f"\n📊 DEBUGGING RESULTS:")
    print("="*40)
    print(f"Baseline (first run):  {baseline_sharpe:.4f}")
    print(f"Baseline (repeat):     {baseline_sharpe2:.4f}")
    print(f"Removed {feature_to_remove[:20]}:  {removed_sharpe:.4f}")
    print(f"Removed {feature_to_remove2[:20]}:  {removed_sharpe2:.4f}")
    
    if baseline_sharpe and removed_sharpe:
        improvement1 = removed_sharpe - baseline_sharpe
        print(f"\nImprovement 1: {improvement1:+.4f}")
    
    if baseline_sharpe and removed_sharpe2:
        improvement2 = removed_sharpe2 - baseline_sharpe
        print(f"Improvement 2: {improvement2:+.4f}")
    
    if baseline_sharpe and baseline_sharpe2:
        baseline_diff = abs(baseline_sharpe2 - baseline_sharpe)
        print(f"Baseline reproducibility: {baseline_diff:.4f} difference")
        
        if baseline_diff > 0.1:
            print("⚠️  Baseline not reproducible - high variance!")
        else:
            print("✅ Baseline is reproducible")
    
    # Check if the issue is variance or systematic
    if baseline_sharpe and removed_sharpe and removed_sharpe2:
        if removed_sharpe > baseline_sharpe and removed_sharpe2 > baseline_sharpe:
            print("\n🚨 BOTH removals help - suggests systematic overfitting")
        elif abs(improvement1) < 0.1 and abs(improvement2) < 0.1:
            print("\n✅ Small differences - normal variance")
        else:
            print("\n🤔 Mixed results - need more investigation")

if __name__ == "__main__":
    debug_feature_removal()
