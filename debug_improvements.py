#!/usr/bin/env python3
"""
Debug script to verify if our improvements are actually working
"""

import pandas as pd
import numpy as np
import lightgbm as lgb

def test_relevance_labels():
    """Test if quintile relevance labels are different from continuous targets"""
    print("🔍 TESTING RELEVANCE LABELS...")
    
    # Load a small sample of data
    df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv', nrows=10000)
    target_df = pd.read_csv('PureEURTarget.csv', nrows=10000)
    
    # Merge
    data = df.merge(target_df, on=['Date', 'P123_ID'], how='inner')
    data['Date'] = pd.to_datetime(data['Date'])
    
    # Create relevance labels like in our system
    def create_relevance_labels(group_data, n_levels=5):
        target_values = group_data['Weighted_MixRel'].dropna()
        if len(target_values) <= 1:
            return pd.Series([n_levels//2] * len(group_data), index=group_data.index)
        
        try:
            labels = pd.qcut(target_values, q=n_levels, labels=range(n_levels), duplicates='drop')
            result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
            result.loc[target_values.index] = labels.astype(int)
            return result
        except:
            return pd.Series([n_levels//2] * len(group_data), index=group_data.index)
    
    # Apply relevance labeling
    data['Relevance'] = data.groupby('Date').apply(
        lambda x: create_relevance_labels(x)
    ).droplevel(0)
    
    # Compare distributions
    print(f"Original Weighted_MixRel stats:")
    print(f"  Mean: {data['Weighted_MixRel'].mean():.4f}")
    print(f"  Std: {data['Weighted_MixRel'].std():.4f}")
    print(f"  Min: {data['Weighted_MixRel'].min():.4f}")
    print(f"  Max: {data['Weighted_MixRel'].max():.4f}")
    
    print(f"\nRelevance labels distribution:")
    relevance_dist = data['Relevance'].value_counts().sort_index()
    for label, count in relevance_dist.items():
        pct = count / len(data) * 100
        print(f"  Label {label}: {count} ({pct:.1f}%)")
    
    # Test correlation
    correlation = data['Weighted_MixRel'].corr(data['Relevance'])
    print(f"\nCorrelation between original and relevance labels: {correlation:.4f}")
    
    return correlation > 0.8  # Should be highly correlated

def test_categorical_features():
    """Test if categorical features are being processed correctly"""
    print("\n🔍 TESTING CATEGORICAL FEATURES...")
    
    # Load a small sample
    df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv', nrows=1000)
    
    categorical_features = [
        'Industry_Code', 'SubIndustry_Code', 'SubSector_Code',
        'Death_Cross_Event_Binary', 'Golden_Cross_Event_Binary',
        'Nordic_Exchanges'
    ]
    
    found_features = []
    for cat_feature in categorical_features:
        if cat_feature in df.columns:
            found_features.append(cat_feature)
            unique_vals = df[cat_feature].nunique()
            sample_vals = df[cat_feature].dropna().unique()[:5]
            print(f"  {cat_feature}: {unique_vals} unique values, samples: {sample_vals}")
        else:
            print(f"  ❌ {cat_feature}: NOT FOUND")
    
    print(f"\nFound {len(found_features)} out of {len(categorical_features)} categorical features")
    return len(found_features) >= 3  # Should find at least 3

def test_training_difference():
    """Test if training with relevance labels vs continuous targets gives different results"""
    print("\n🔍 TESTING TRAINING DIFFERENCE...")
    
    # Create synthetic ranking data
    np.random.seed(42)
    n_samples = 1000
    n_features = 10
    n_groups = 10
    
    X = np.random.randn(n_samples, n_features)
    y_continuous = np.random.randn(n_samples)
    
    # Create relevance labels from continuous
    group_size = n_samples // n_groups
    y_relevance = np.zeros(n_samples)
    
    for i in range(n_groups):
        start_idx = i * group_size
        end_idx = (i + 1) * group_size
        group_y = y_continuous[start_idx:end_idx]
        
        # Convert to quintiles
        try:
            relevance = pd.qcut(group_y, q=5, labels=range(5), duplicates='drop')
            y_relevance[start_idx:end_idx] = relevance.astype(int)
        except:
            y_relevance[start_idx:end_idx] = 2  # Middle value
    
    groups = [group_size] * n_groups
    
    # Train with continuous targets
    train_data_cont = lgb.Dataset(X, label=y_continuous, group=groups)
    model_cont = lgb.train(
        {'objective': 'rank_xendcg', 'metric': 'ndcg', 'verbose': -1},
        train_data_cont,
        num_boost_round=100
    )
    
    # Train with relevance labels
    train_data_rel = lgb.Dataset(X, label=y_relevance, group=groups)
    model_rel = lgb.train(
        {'objective': 'rank_xendcg', 'metric': 'ndcg', 'verbose': -1},
        train_data_rel,
        num_boost_round=100
    )
    
    # Compare predictions
    pred_cont = model_cont.predict(X)
    pred_rel = model_rel.predict(X)
    
    correlation = np.corrcoef(pred_cont, pred_rel)[0, 1]
    print(f"Correlation between continuous and relevance predictions: {correlation:.4f}")
    
    return correlation < 0.95  # Should be different if relevance labels matter

if __name__ == "__main__":
    print("=" * 60)
    print("🔍 DEBUGGING LIGHTGBM IMPROVEMENTS")
    print("=" * 60)
    
    # Test 1: Relevance labels
    try:
        relevance_ok = test_relevance_labels()
        print(f"✅ Relevance labels test: {'PASS' if relevance_ok else 'FAIL'}")
    except Exception as e:
        print(f"❌ Relevance labels test: ERROR - {e}")
        relevance_ok = False
    
    # Test 2: Categorical features
    try:
        categorical_ok = test_categorical_features()
        print(f"✅ Categorical features test: {'PASS' if categorical_ok else 'FAIL'}")
    except Exception as e:
        print(f"❌ Categorical features test: ERROR - {e}")
        categorical_ok = False
    
    # Test 3: Training difference
    try:
        training_ok = test_training_difference()
        print(f"✅ Training difference test: {'PASS' if training_ok else 'FAIL'}")
    except Exception as e:
        print(f"❌ Training difference test: ERROR - {e}")
        training_ok = False
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY")
    print("=" * 60)
    
    if relevance_ok and categorical_ok and training_ok:
        print("✅ All improvements are working correctly")
        print("💡 The small performance difference might be expected")
    else:
        print("❌ Some improvements may not be working as expected")
        print("🔧 Consider investigating further or reverting changes")
