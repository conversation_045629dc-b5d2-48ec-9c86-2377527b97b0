#!/usr/bin/env python3
"""
Generate comprehensive SHAP feature importance analysis using the new hyperparameters
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import shap
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data():
    """Load and prepare data exactly like the validation system"""
    print("📊 Loading data...")
    
    # Load datasets
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    print(f"✓ Features: {features_df.shape}")
    print(f"✓ Targets: {target_df.shape}")
    print(f"✓ Returns: {returns_df.shape}")
    
    # Clean feature names
    def clean_feature_names(df):
        name_mapping = {}
        df_clean = df.copy()
        
        for col in df.columns:
            clean_name = col.replace(' ', '_').replace('(', '').replace(')', '').replace('%', 'Pct')
            clean_name = clean_name.replace('/', '_').replace('-', '_').replace('+', 'Plus')
            clean_name = clean_name.replace('&', 'And').replace('__', '_')
            if clean_name != col:
                name_mapping[clean_name] = col
                df_clean = df_clean.rename(columns={col: clean_name})
        
        return df_clean, name_mapping
    
    features_df, name_mapping = clean_feature_names(features_df)
    target_df, _ = clean_feature_names(target_df)
    returns_df, _ = clean_feature_names(returns_df)
    
    # Merge datasets
    print("🔗 Merging datasets...")
    data = features_df.merge(target_df, on=['Date', 'P123_ID'], how='inner')
    data = data.merge(returns_df, on=['Date', 'P123_ID'], how='inner')
    
    # Filter date range
    data['Date'] = pd.to_datetime(data['Date'])
    start_date = pd.to_datetime('2005-12-01')
    end_date = pd.to_datetime('2025-05-01')
    data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
    
    print(f"✓ Merged and filtered: {data.shape}")
    
    # Create relevance labels
    print("🏷️ Creating relevance labels...")
    data = data.sort_values(['Date', 'P123_ID']).reset_index(drop=True)
    data['QueryID'] = data['Date'].rank(method='dense').astype(int)
    
    def create_relevance_labels(group_data, n_levels=5):
        target_values = group_data['Weighted_MixRel'].dropna()
        if len(target_values) <= 1:
            return pd.Series([n_levels//2] * len(group_data), index=group_data.index)
        
        try:
            labels = pd.qcut(target_values, q=n_levels, labels=range(n_levels), duplicates='drop')
            result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
            result.loc[target_values.index] = labels.astype(int)
            return result
        except:
            return pd.Series([n_levels//2] * len(group_data), index=group_data.index)
    
    data['Relevance'] = data.groupby('Date').apply(
        lambda x: create_relevance_labels(x)
    ).droplevel(0)
    
    # Handle categorical features
    categorical_features = [
        'Industry_Code', 'SubIndustry_Code', 'SubSector_Code',
        'Death_Cross_Event_Binary', 'Golden_Cross_Event_Binary',
        'Nordic_Exchanges', 'Banks_SubSector', 'Cyclicals_Sector',
        'Energy_Sector', 'Financial_Sector', 'Healthcare_Sector',
        'Industrial_Sector', 'Materials_Sector', 'Tech_Sector'
    ]
    
    # Exclude metadata and object columns
    exclude_cols = ['Date', 'P123_ID', 'Ticker', 'Ticker_x', 'Ticker_y', 'Weighted_MixRel', 'Future5DReturn', 'QueryID', 'Relevance']
    feature_cols = [col for col in data.columns if col not in exclude_cols and data[col].dtype in ['int64', 'float64', 'bool']]
    
    categorical_indices = []
    for i, col in enumerate(feature_cols):
        if any(cat in col for cat in categorical_features):
            categorical_indices.append(i)
    
    print(f"✓ Features: {len(feature_cols)}")
    print(f"✓ Categorical: {len(categorical_indices)}")
    
    return data, feature_cols, categorical_indices, name_mapping

def train_model_for_shap(data, feature_cols, categorical_indices):
    """Train a model using comprehensive hyperparameters for SHAP analysis"""
    print("🧠 Training model for SHAP analysis...")
    
    # Use recent data for training (last 3 years)
    recent_date = data['Date'].max() - pd.DateOffset(years=3)
    train_data = data[data['Date'] >= recent_date].copy()
    
    print(f"Training on recent data: {len(train_data)} samples")
    
    # Prepare features and targets
    X = train_data[feature_cols].fillna(0)
    y = train_data['Relevance'].values
    query_ids = train_data['QueryID'].values
    
    # Create groups
    unique_queries = sorted(np.unique(query_ids))
    groups = []
    for query_id in unique_queries:
        query_mask = query_ids == query_id
        group_size = query_mask.sum()
        if group_size > 0:
            groups.append(group_size)
    
    # Comprehensive hyperparameters
    params = {
        'objective': 'rank_xendcg',
        'metric': 'ndcg',
        'boosting_type': 'gbdt',
        'n_estimators': 1500,
        'max_depth': 8,
        'learning_rate': 0.03,
        'num_leaves': 100,
        'subsample': 0.8,
        'min_child_samples': 50,
        'feature_fraction': 0.8,
        'lambda_l1': 0.001,
        'lambda_l2': 0.001,
        'min_split_gain': 0.001,
        'bagging_freq': 3,
        'verbose': -1,
        'random_state': 42,
        'n_jobs': -1,
        'ndcg_eval_at': [10, 20, 50],
        'lambdarank_truncation_level': 100,
        'lambdarank_norm': True
    }
    
    # Train model
    train_dataset = lgb.Dataset(
        X, label=y, group=groups,
        categorical_feature=categorical_indices if categorical_indices else 'auto'
    )
    
    model = lgb.train(params, train_dataset, callbacks=[lgb.log_evaluation(0)])
    
    return model, X, feature_cols

def run_shap_analysis(model, X, feature_cols, name_mapping):
    """Run comprehensive SHAP analysis"""
    print("🔍 Running SHAP analysis...")
    
    # Sample data for SHAP (use subset for speed)
    sample_size = min(1000, len(X))
    X_sample = X.sample(n=sample_size, random_state=42)
    
    print(f"Using {sample_size} samples for SHAP analysis...")
    
    # Create SHAP explainer
    explainer = shap.TreeExplainer(model)
    shap_values = explainer.shap_values(X_sample)
    
    # Calculate feature importance
    feature_importance = np.abs(shap_values).mean(axis=0)
    
    # Create results dataframe
    results_df = pd.DataFrame({
        'Feature_Clean': feature_cols,
        'Feature_Original': [name_mapping.get(col, col) for col in feature_cols],
        'SHAP_Importance': feature_importance
    }).sort_values('SHAP_Importance', ascending=False)
    
    results_df['Rank'] = range(1, len(results_df) + 1)
    
    return results_df, shap_values, X_sample

def main():
    print("=" * 80)
    print("🔍 COMPREHENSIVE SHAP FEATURE IMPORTANCE ANALYSIS")
    print("=" * 80)
    
    # Load and prepare data
    data, feature_cols, categorical_indices, name_mapping = load_and_prepare_data()
    
    # Train model
    model, X, feature_cols = train_model_for_shap(data, feature_cols, categorical_indices)
    
    # Run SHAP analysis
    results_df, shap_values, X_sample = run_shap_analysis(model, X, feature_cols, name_mapping)
    
    # Display results
    print("\n" + "=" * 80)
    print("🏆 TOP 30 MOST IMPORTANT FEATURES")
    print("=" * 80)
    
    print(f"{'Rank':<5} {'Feature Name':<50} {'SHAP Importance':<15}")
    print("-" * 80)
    
    for _, row in results_df.head(30).iterrows():
        print(f"{row['Rank']:<5} {row['Feature_Original']:<50} {row['SHAP_Importance']:<15.6f}")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'shap_feature_importance_comprehensive_{timestamp}.csv'
    results_df.to_csv(filename, index=False)
    
    print(f"\n✅ Results saved to: {filename}")
    print(f"📊 Total features analyzed: {len(results_df)}")
    print(f"🎯 Top feature: {results_df.iloc[0]['Feature_Original']}")
    print(f"📈 Importance range: {results_df['SHAP_Importance'].min():.6f} to {results_df['SHAP_Importance'].max():.6f}")
    
    return results_df

if __name__ == "__main__":
    results = main()
