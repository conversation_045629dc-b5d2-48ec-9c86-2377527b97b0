#!/usr/bin/env python3
"""
Lightweight final validation that doesn't load the massive 42GB enhanced dataset
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime

def analyze_lofep_results():
    """Analyze LOFEP results without loading the massive dataset"""
    
    print("🔍 Analyzing LOFEP Results (Memory-Efficient)")
    print("="*60)
    
    # Load performance history
    print("Loading feature performance history...")
    try:
        perf_df = pd.read_csv('lofep_results/feature_performance_history_20250711_165917.csv', index_col=0)
        print(f"✓ Loaded {len(perf_df)} feature evaluations")
    except Exception as e:
        print(f"❌ Error loading performance history: {e}")
        return
    
    # Load selected features
    print("Loading selected features...")
    try:
        selected_df = pd.read_csv('lofep_results/selected_features_20250711_165917.csv')
        print(f"✓ Loaded {len(selected_df)} selected features")
    except Exception as e:
        print(f"❌ Error loading selected features: {e}")
        return
    
    print("\n" + "="*60)
    print("📊 FEATURE EVALUATION SUMMARY")
    print("="*60)
    
    # Analyze performance improvements
    improvements = []
    for idx, row in perf_df.iterrows():
        feature_name = idx
        sharpe_improvement = row['sharpe_improvement']
        improvements.append((feature_name, sharpe_improvement))
    
    # Sort by improvement
    improvements.sort(key=lambda x: x[1], reverse=True)
    
    # Show top performers
    print("\n🏆 TOP 10 FEATURE IMPROVEMENTS:")
    print("-" * 50)
    for i, (feature, improvement) in enumerate(improvements[:10]):
        status = "✅" if improvement > 0.02 else "⚠️" if improvement > 0 else "❌"
        print(f"{i+1:2d}. {status} {feature[:40]:<40} {improvement:+.4f}")
    
    # Show worst performers
    print("\n💥 WORST 10 FEATURES (Hurt Performance):")
    print("-" * 50)
    for i, (feature, improvement) in enumerate(improvements[-10:]):
        print(f"{i+1:2d}. ❌ {feature[:40]:<40} {improvement:+.4f}")
    
    # Statistics
    positive_improvements = [imp for _, imp in improvements if imp > 0.02]
    negative_improvements = [imp for _, imp in improvements if imp < -0.02]
    neutral_improvements = [imp for _, imp in improvements if -0.02 <= imp <= 0.02]
    
    print(f"\n📈 IMPROVEMENT STATISTICS:")
    print(f"Features evaluated: {len(improvements)}")
    print(f"Significantly helpful (>+0.02): {len(positive_improvements)}")
    print(f"Neutral (-0.02 to +0.02): {len(neutral_improvements)}")
    print(f"Harmful (<-0.02): {len(negative_improvements)}")
    
    if positive_improvements:
        print(f"Best improvement: +{max(positive_improvements):.4f}")
        print(f"Average positive improvement: +{np.mean(positive_improvements):.4f}")
    
    if negative_improvements:
        print(f"Worst degradation: {min(negative_improvements):.4f}")
        print(f"Average negative impact: {np.mean(negative_improvements):.4f}")
    
    # Baseline comparison
    baseline_sharpe = 1.7765  # From the logs
    
    print(f"\n🎯 PERFORMANCE PROJECTION:")
    print(f"Baseline Sharpe: {baseline_sharpe:.4f}")
    
    if positive_improvements:
        # Conservative estimate: take top 5 improvements
        top_5_improvements = sorted(positive_improvements, reverse=True)[:5]
        projected_improvement = sum(top_5_improvements)
        projected_sharpe = baseline_sharpe + projected_improvement
        
        print(f"Top 5 feature improvements: {[f'+{imp:.4f}' for imp in top_5_improvements]}")
        print(f"Projected total improvement: +{projected_improvement:.4f}")
        print(f"Projected final Sharpe: {projected_sharpe:.4f}")
        print(f"Improvement: {(projected_sharpe/baseline_sharpe - 1)*100:.1f}%")
    
    # Feature type analysis
    print(f"\n🔬 FEATURE TYPE ANALYSIS:")
    
    feature_types = {
        'Mathematical': ['_rank', '_log', '_zscore'],
        'Cross-sectional': ['_Market_Cap_Quintile', '_vs_'],
        'Temporal': ['_rolling_', '_momentum_'],
        'Ratio': ['_ratio', '_div_']
    }
    
    type_performance = {}
    for feature_type, indicators in feature_types.items():
        type_improvements = []
        for feature, improvement in improvements:
            if any(indicator in feature for indicator in indicators):
                type_improvements.append(improvement)
        
        if type_improvements:
            avg_improvement = np.mean(type_improvements)
            positive_count = sum(1 for imp in type_improvements if imp > 0.02)
            type_performance[feature_type] = {
                'count': len(type_improvements),
                'avg_improvement': avg_improvement,
                'positive_count': positive_count,
                'success_rate': positive_count / len(type_improvements) if type_improvements else 0
            }
    
    for feature_type, stats in type_performance.items():
        print(f"{feature_type:15} | {stats['count']:3d} features | "
              f"Avg: {stats['avg_improvement']:+.4f} | "
              f"Success: {stats['success_rate']:.1%} ({stats['positive_count']}/{stats['count']})")
    
    # Save summary
    summary = {
        'total_features_evaluated': len(improvements),
        'significantly_helpful': len(positive_improvements),
        'neutral': len(neutral_improvements),
        'harmful': len(negative_improvements),
        'baseline_sharpe': baseline_sharpe,
        'top_improvements': improvements[:10],
        'worst_features': improvements[-10:],
        'feature_type_performance': type_performance,
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    # Save results
    with open('lofep_results/analysis_summary.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n💾 Analysis saved to: lofep_results/analysis_summary.json")
    
    # Memory usage warning
    print(f"\n⚠️  MEMORY WARNING:")
    print(f"Enhanced dataset size: 42GB")
    print(f"This is too large to load on most systems.")
    print(f"Consider using the feature selection results instead of the full dataset.")
    
    return summary

def get_recommended_features(min_improvement=0.02):
    """Get list of recommended features based on performance"""
    
    try:
        perf_df = pd.read_csv('lofep_results/feature_performance_history_20250711_165917.csv', index_col=0)
        
        recommended = []
        for idx, row in perf_df.iterrows():
            if row['sharpe_improvement'] > min_improvement:
                recommended.append({
                    'feature': idx,
                    'improvement': row['sharpe_improvement'],
                    'stability': row.get('stability_score', 0)
                })
        
        # Sort by improvement
        recommended.sort(key=lambda x: x['improvement'], reverse=True)
        
        print(f"\n🎯 RECOMMENDED FEATURES (>{min_improvement:+.3f} Sharpe improvement):")
        print("-" * 60)
        for i, feat in enumerate(recommended):
            print(f"{i+1:2d}. {feat['feature'][:45]:<45} {feat['improvement']:+.4f}")
        
        return recommended
        
    except Exception as e:
        print(f"Error getting recommendations: {e}")
        return []

if __name__ == "__main__":
    # Kill any remaining Python processes that might be using memory
    import os
    print("🧹 Checking for memory-heavy processes...")
    
    summary = analyze_lofep_results()
    recommended = get_recommended_features()
    
    print(f"\n🎉 LOFEP Analysis Complete!")
    print(f"Found {len(recommended)} features worth adding to your model.")
