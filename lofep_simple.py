#!/usr/bin/env python3
"""
LOFEP Simple - Working Implementation
Simplified but robust feature engineering pipeline
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import warnings
from datetime import datetime
from pathlib import Path
from scipy.stats import spearmanr
from sklearn.metrics import mean_squared_error

warnings.filterwarnings('ignore')


class LOFEPSimple:
    """Simplified but robust feature engineering pipeline"""
    
    def __init__(self):
        self.baseline_performance = None
        self.selected_features = []
        self.feature_performance = {}
        
        print("🚀 LOFEP Simple - LightGBM-Optimized Feature Engineering")
        print("="*60)
    
    def clean_feature_name(self, name: str) -> str:
        """Clean feature names for LightGBM compatibility"""
        cleaned = str(name).replace(' ', '_').replace('-', '_').replace('&', 'And')
        cleaned = cleaned.replace('(', '').replace(')', '').replace('%', 'Pct')
        cleaned = cleaned.replace('/', '_div_').replace('*', '_mult_').replace('+', '_plus_')
        cleaned = cleaned.replace(':', '_').replace('"', '').replace("'", '')
        cleaned = cleaned.replace(',', '_').replace('.', '_').replace('=', '_eq_')
        cleaned = cleaned.replace('<', '_lt_').replace('>', '_gt_').replace('!', '_not_')
        cleaned = cleaned.replace('[', '_').replace(']', '_').replace('{', '_').replace('}', '_')
        cleaned = cleaned.replace('#', '_num_').replace('@', '_at_').replace('$', '_dollar_')
        
        # Remove consecutive underscores
        while '__' in cleaned:
            cleaned = cleaned.replace('__', '_')
        
        # Ensure it starts with letter or underscore
        if cleaned and cleaned[0].isdigit():
            cleaned = 'F_' + cleaned
        
        return cleaned
    
    def load_and_prepare_data(self):
        """Load and prepare data with robust preprocessing"""
        print("📊 Loading data...")
        
        # Load datasets
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        target_df = pd.read_csv('PureEURTarget.csv')
        
        print(f"✓ Features loaded: {features_df.shape}")
        print(f"✓ Target loaded: {target_df.shape}")
        
        # Standardize column names
        if 'P123 ID' in target_df.columns:
            target_df = target_df.rename(columns={'P123 ID': 'P123_ID'})
        if 'P123 ID' in features_df.columns:
            features_df = features_df.rename(columns={'P123 ID': 'P123_ID'})
        
        # Merge datasets
        merged_data = features_df.merge(
            target_df[['Date', 'P123_ID', 'Weighted_MixRel']], 
            on=['Date', 'P123_ID'], 
            how='inner'
        )
        
        # Convert date and sort
        merged_data['Date'] = pd.to_datetime(merged_data['Date'])
        merged_data = merged_data.sort_values('Date')
        
        print(f"✓ Merged data: {merged_data.shape}")
        print(f"✓ Date range: {merged_data['Date'].min()} to {merged_data['Date'].max()}")
        
        # Identify feature columns
        exclude_cols = ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel']
        feature_cols = [col for col in merged_data.columns 
                       if col not in exclude_cols and 
                       merged_data[col].dtype in ['int64', 'float64']]
        
        print(f"✓ Found {len(feature_cols)} numerical features")
        
        # Clean and prepare features
        X = merged_data[feature_cols].copy()
        
        # Handle missing values more aggressively
        print("🔧 Cleaning data...")
        
        # Remove columns with too many missing values (>50%)
        missing_pct = X.isnull().sum() / len(X)
        good_cols = missing_pct[missing_pct <= 0.5].index.tolist()
        X = X[good_cols]
        print(f"✓ Kept {len(good_cols)} features with <50% missing values")
        
        # Fill remaining missing values
        X = X.fillna(X.median())
        
        # Remove infinite values
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(0)
        
        # Remove constant columns
        constant_cols = X.columns[X.nunique() <= 1].tolist()
        X = X.drop(columns=constant_cols)
        print(f"✓ Removed {len(constant_cols)} constant columns")
        
        # Clean column names
        X.columns = [self.clean_feature_name(col) for col in X.columns]
        
        # Final check for any remaining issues
        assert not X.isnull().any().any(), "Still have NaN values!"
        assert not np.isinf(X.values).any(), "Still have infinite values!"
        
        print(f"✓ Final feature set: {X.shape}")
        
        return X, merged_data['Weighted_MixRel'], merged_data['Date']
    
    def calculate_performance_metrics(self, returns: pd.Series) -> dict:
        """Calculate performance metrics from returns"""
        
        if len(returns) == 0 or returns.isna().all():
            return {
                'sharpe_ratio': 0.0,
                'cagr': 0.0,
                'volatility': 0.0,
                'max_drawdown': 0.0,
                'hit_rate': 0.5
            }
        
        # Remove NaN values
        returns = returns.dropna()
        
        if len(returns) == 0:
            return {
                'sharpe_ratio': 0.0,
                'cagr': 0.0,
                'volatility': 0.0,
                'max_drawdown': 0.0,
                'hit_rate': 0.5
            }
        
        # Annualized metrics (assuming monthly returns)
        periods_per_year = 12
        
        # Basic metrics
        mean_return = returns.mean()
        volatility = returns.std()
        
        # Sharpe ratio
        sharpe_ratio = (mean_return * periods_per_year) / (volatility * np.sqrt(periods_per_year)) if volatility > 0 else 0
        
        # CAGR
        total_return = (1 + returns).prod() - 1
        years = len(returns) / periods_per_year
        cagr = (1 + total_return) ** (1 / years) - 1 if years > 0 and total_return > -1 else 0
        
        # Maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdowns.min()
        
        # Hit rate
        hit_rate = (returns > 0).mean()
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'cagr': cagr,
            'volatility': volatility * np.sqrt(periods_per_year),
            'max_drawdown': max_drawdown,
            'hit_rate': hit_rate
        }
    
    def simple_time_series_validation(self, X: pd.DataFrame, y: pd.Series, dates: pd.Series) -> dict:
        """Simple but robust time series validation"""
        
        print(f"🔍 Running validation on {X.shape[1]} features...")
        
        # Create simple train/test splits
        unique_dates = sorted(dates.unique())
        n_dates = len(unique_dates)
        
        # Use last 20% for testing, rest for training
        split_idx = int(n_dates * 0.8)
        train_end_date = unique_dates[split_idx]
        
        train_mask = dates < train_end_date
        test_mask = dates >= train_end_date
        
        print(f"✓ Train period: {dates[train_mask].min()} to {dates[train_mask].max()}")
        print(f"✓ Test period: {dates[test_mask].min()} to {dates[test_mask].max()}")
        print(f"✓ Train samples: {train_mask.sum()}, Test samples: {test_mask.sum()}")
        
        if train_mask.sum() < 1000 or test_mask.sum() < 100:
            print("❌ Insufficient data for validation")
            return {'sharpe_ratio': 0.0, 'cagr': 0.0, 'volatility': 0.0, 'max_drawdown': 0.0, 'hit_rate': 0.5}
        
        # Prepare data
        X_train = X[train_mask]
        y_train = y[train_mask]
        X_test = X[test_mask]
        y_test = y[test_mask]
        
        # Train LightGBM
        print("🤖 Training LightGBM...")
        
        train_data = lgb.Dataset(X_train, label=y_train)
        
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.1,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }
        
        model = lgb.train(
            params,
            train_data,
            num_boost_round=500,
            callbacks=[lgb.log_evaluation(0)]  # Suppress output
        )
        
        # Make predictions
        y_pred = model.predict(X_test)
        
        # Calculate performance metrics
        performance = self.calculate_performance_metrics(pd.Series(y_pred))
        
        # Add additional metrics
        performance['rmse'] = np.sqrt(mean_squared_error(y_test, y_pred))
        performance['correlation'] = np.corrcoef(y_test, y_pred)[0, 1] if len(y_test) > 1 else 0
        
        # Information coefficient
        try:
            ic, _ = spearmanr(y_test, y_pred)
            performance['information_coefficient'] = ic if not np.isnan(ic) else 0
        except:
            performance['information_coefficient'] = 0
        
        print(f"✓ Validation completed:")
        print(f"  Sharpe Ratio: {performance['sharpe_ratio']:.4f}")
        print(f"  CAGR: {performance['cagr']:.2%}")
        print(f"  Max Drawdown: {performance['max_drawdown']:.2%}")
        print(f"  Information Coefficient: {performance['information_coefficient']:.4f}")
        
        return performance
    
    def generate_mathematical_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Generate mathematical transformations"""
        
        print("🔧 Generating mathematical features...")
        
        X_enhanced = X.copy()
        original_features = X.columns.tolist()
        
        # Select top 50 features by variance for transformation (to keep it manageable)
        feature_variance = X.var().sort_values(ascending=False)
        top_features = feature_variance.head(50).index.tolist()
        
        print(f"✓ Transforming top {len(top_features)} features by variance")
        
        for feature in top_features:
            feature_data = X[feature]
            
            # Skip if constant
            if feature_data.nunique() <= 1:
                continue
            
            try:
                # Rank transformation (most important for LightGBM)
                X_enhanced[f"{feature}_rank"] = feature_data.rank(method='average')
                
                # Log transformation (handle negative values)
                X_enhanced[f"{feature}_log"] = np.log(np.abs(feature_data) + 1) * np.sign(feature_data)
                
                # Square root transformation
                X_enhanced[f"{feature}_sqrt"] = np.sqrt(np.abs(feature_data)) * np.sign(feature_data)
                
                # Z-score standardization
                if feature_data.std() > 0:
                    X_enhanced[f"{feature}_zscore"] = (feature_data - feature_data.mean()) / feature_data.std()
                
            except Exception as e:
                print(f"Warning: Error transforming {feature}: {e}")
                continue
        
        # Remove any new NaN or infinite values
        X_enhanced = X_enhanced.replace([np.inf, -np.inf], np.nan)
        X_enhanced = X_enhanced.fillna(0)
        
        new_features = len(X_enhanced.columns) - len(original_features)
        print(f"✓ Generated {new_features} mathematical features")
        print(f"✓ Total features: {len(X_enhanced.columns)}")
        
        return X_enhanced
    
    def run_pipeline(self):
        """Run the complete pipeline"""
        
        try:
            # Step 1: Load and prepare data
            X, y, dates = self.load_and_prepare_data()
            
            # Step 2: Establish baseline
            print("\n📈 Establishing baseline performance...")
            self.baseline_performance = self.simple_time_series_validation(X, y, dates)
            
            # Step 3: Generate enhanced features
            print("\n🔧 Generating enhanced features...")
            X_enhanced = self.generate_mathematical_features(X)
            
            # Step 4: Test enhanced model
            print("\n📈 Testing enhanced model...")
            enhanced_performance = self.simple_time_series_validation(X_enhanced, y, dates)
            
            # Step 5: Compare results
            print("\n" + "="*60)
            print("🎉 FINAL RESULTS")
            print("="*60)
            
            baseline_sharpe = self.baseline_performance['sharpe_ratio']
            enhanced_sharpe = enhanced_performance['sharpe_ratio']
            sharpe_improvement = enhanced_sharpe - baseline_sharpe
            
            print(f"Baseline Sharpe Ratio:    {baseline_sharpe:.4f}")
            print(f"Enhanced Sharpe Ratio:    {enhanced_sharpe:.4f}")
            print(f"Sharpe Improvement:       +{sharpe_improvement:.4f}")
            
            if baseline_sharpe > 0:
                improvement_pct = (sharpe_improvement / baseline_sharpe) * 100
                print(f"Relative Improvement:     {improvement_pct:+.1f}%")
            
            print(f"")
            print(f"Baseline CAGR:            {self.baseline_performance['cagr']:.2%}")
            print(f"Enhanced CAGR:            {enhanced_performance['cagr']:.2%}")
            print(f"CAGR Improvement:         +{enhanced_performance['cagr'] - self.baseline_performance['cagr']:.2%}")
            
            print(f"")
            print(f"Baseline Max Drawdown:    {self.baseline_performance['max_drawdown']:.2%}")
            print(f"Enhanced Max Drawdown:    {enhanced_performance['max_drawdown']:.2%}")
            
            print(f"")
            print(f"Original Features:        {len(X.columns)}")
            print(f"Enhanced Features:        {len(X_enhanced.columns)}")
            print(f"Features Added:           {len(X_enhanced.columns) - len(X.columns)}")
            
            print("="*60)
            
            # Save results
            results = {
                'baseline_performance': self.baseline_performance,
                'enhanced_performance': enhanced_performance,
                'improvement': {
                    'sharpe': sharpe_improvement,
                    'cagr': enhanced_performance['cagr'] - self.baseline_performance['cagr']
                }
            }
            
            return results
            
        except Exception as e:
            print(f"❌ Pipeline failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """Main execution"""
    pipeline = LOFEPSimple()
    results = pipeline.run_pipeline()
    
    if results:
        print("\n✅ Pipeline completed successfully!")
    else:
        print("\n❌ Pipeline failed!")


if __name__ == "__main__":
    main()
