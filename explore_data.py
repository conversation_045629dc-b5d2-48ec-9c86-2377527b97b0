#!/usr/bin/env python3
"""
Quick data exploration to understand file structure
"""

import pandas as pd
import numpy as np

def main():
    print("="*60)
    print("📊 EXPLORING DATA FILES")
    print("="*60)
    
    # Check features file
    print("1. Features file:")
    try:
        features = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv', nrows=5)
        print(f"   Shape (first 5 rows): {features.shape}")
        print(f"   Columns: {list(features.columns[:10])}")
        print("   Sample:")
        print(features.head(2))
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n2. Future returns file:")
    try:
        returns = pd.read_csv('PureEURFuture1WRet.csv', nrows=5)
        print(f"   Shape (first 5 rows): {returns.shape}")
        print(f"   Columns: {list(returns.columns[:10])}")
        print("   Sample:")
        print(returns.head(2))
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n3. Target file:")
    try:
        target = pd.read_csv('PureEURTarget.csv', nrows=5)
        print(f"   Shape (first 5 rows): {target.shape}")
        print(f"   Columns: {list(target.columns[:10])}")
        print("   Sample:")
        print(target.head(2))
    except Exception as e:
        print(f"   Error: {e}")

if __name__ == "__main__":
    main()
