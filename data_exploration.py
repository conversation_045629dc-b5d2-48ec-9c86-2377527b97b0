#!/usr/bin/env python3
"""
Data exploration script to understand the structure of the datasets
"""

import pandas as pd
import numpy as np
from datetime import datetime

def explore_data():
    print("="*60)
    print("📊 DATA EXPLORATION")
    print("="*60)
    
    # Load features
    print("Loading features...")
    try:
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        print(f"✓ Features loaded: {features_df.shape}")
        print(f"  Columns: {list(features_df.columns[:10])}...")
        print(f"  Date range: {features_df.iloc[:, 0].min()} to {features_df.iloc[:, 0].max()}")
        print(f"  Sample data:")
        print(features_df.head())
    except Exception as e:
        print(f"❌ Error loading features: {e}")
        return False
    
    # Load target (future returns)
    print("\nLoading future returns...")
    try:
        future_returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        print(f"✓ Future returns loaded: {future_returns_df.shape}")
        print(f"  Columns: {list(future_returns_df.columns[:10])}...")
        print(f"  Sample data:")
        print(future_returns_df.head())
    except Exception as e:
        print(f"❌ Error loading future returns: {e}")
        return False
    
    # Load target values
    print("\nLoading target values...")
    try:
        target_df = pd.read_csv('PureEURTarget.csv')
        print(f"✓ Target values loaded: {target_df.shape}")
        print(f"  Columns: {list(target_df.columns[:10])}...")
        print(f"  Sample data:")
        print(target_df.head())
    except Exception as e:
        print(f"❌ Error loading target values: {e}")
        return False
    
    # Analyze data structure
    print("\n" + "="*60)
    print("📋 DATA STRUCTURE ANALYSIS")
    print("="*60)
    
    # Check if first column is date
    features_first_col = features_df.columns[0]
    print(f"Features first column: {features_first_col}")
    
    # Try to identify date and ID columns
    potential_date_cols = [col for col in features_df.columns if 'date' in col.lower() or 'time' in col.lower()]
    potential_id_cols = [col for col in features_df.columns if 'id' in col.lower() or 'symbol' in col.lower() or 'ticker' in col.lower()]
    
    print(f"Potential date columns: {potential_date_cols}")
    print(f"Potential ID columns: {potential_id_cols}")
    
    # Check data types
    print(f"\nFeatures data types:")
    print(features_df.dtypes.value_counts())
    
    # Check for missing values
    print(f"\nMissing values in features: {features_df.isnull().sum().sum()}")
    print(f"Missing values in future returns: {future_returns_df.isnull().sum().sum()}")
    print(f"Missing values in targets: {target_df.isnull().sum().sum()}")
    
    # Check date ranges
    print(f"\n📅 DATE ANALYSIS")
    print("-" * 30)
    
    # Try to parse dates from first column
    try:
        features_dates = pd.to_datetime(features_df.iloc[:, 0])
        print(f"Features date range: {features_dates.min()} to {features_dates.max()}")
        print(f"Unique dates: {features_dates.nunique()}")
        print(f"Total observations: {len(features_df)}")
        print(f"Avg observations per date: {len(features_df) / features_dates.nunique():.1f}")
    except Exception as e:
        print(f"Could not parse dates from first column: {e}")
    
    # Check if we can merge the datasets
    print(f"\n🔗 MERGE ANALYSIS")
    print("-" * 30)
    
    # Check common columns
    features_cols = set(features_df.columns)
    returns_cols = set(future_returns_df.columns)
    target_cols = set(target_df.columns)
    
    common_features_returns = features_cols.intersection(returns_cols)
    common_features_target = features_cols.intersection(target_cols)
    common_returns_target = returns_cols.intersection(target_cols)
    
    print(f"Common columns (features & returns): {len(common_features_returns)}")
    print(f"Common columns (features & target): {len(common_features_target)}")
    print(f"Common columns (returns & target): {len(common_returns_target)}")
    
    if len(common_features_returns) > 0:
        print(f"Sample common columns: {list(common_features_returns)[:5]}")
    
    return True

if __name__ == "__main__":
    explore_data()
