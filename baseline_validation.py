#!/usr/bin/env python3
"""
Run baseline validation to establish true baseline performance
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility"""
    cleaned = str(name)
    
    replacements = {
        ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_', 
        '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_', 
        '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
        '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
        '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
        '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)
    cleaned = re.sub(r'_+', '_', cleaned)
    cleaned = cleaned.strip('_')
    
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    
    if not cleaned:
        cleaned = 'Feature_Unknown'
    
    return cleaned

def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3):
    """Create time-based train/validation splits"""
    splits = []
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    current_date = min_date
    while current_date < max_date:
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        current_date = val_start
    
    return splits

def run_baseline_validation():
    """Run baseline validation to establish true performance"""
    
    print("🎯 Running Baseline Validation")
    print("="*50)
    
    # Load data
    print("Loading comprehensive features file...")
    df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    print(f"Loaded data: {df.shape}")
    
    # Check for target columns
    print("\nLooking for target/return columns...")
    target_candidates = []
    for col in df.columns:
        if any(word in col.lower() for word in ['target', 'weighted', 'future', 'mixrel']):
            target_candidates.append(col)
    
    print(f"Target candidates: {target_candidates}")
    
    # Check if we have the LOFEP target files
    try:
        target_df = pd.read_csv('PureEURTarget.csv')
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        print(f"Found LOFEP target files:")
        print(f"  Target: {target_df.shape}")
        print(f"  Returns: {returns_df.shape}")
        
        # Merge with main data
        df = df.merge(target_df, left_on=['Date', 'P123 ID'], right_on=['Date', 'P123_ID'], how='inner')
        df = df.merge(returns_df, left_on=['Date', 'P123 ID'], right_on=['Date', 'P123_ID'], how='inner')
        print(f"Merged data: {df.shape}")
        
        # Check target columns again
        target_col = None
        returns_col = None
        for col in df.columns:
            if 'weighted' in col.lower() and 'mixrel' in col.lower():
                target_col = col
            elif 'future' in col.lower() and 'return' in col.lower():
                returns_col = col
        
        print(f"Target column: {target_col}")
        print(f"Returns column: {returns_col}")
        
    except Exception as e:
        print(f"Could not load LOFEP target files: {e}")
        print("Will try to find target in main dataset...")
        
        # Look for any column that could be a target
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        print(f"Numeric columns: {len(numeric_cols)}")
        
        # Use a reasonable proxy target if available
        target_col = None
        returns_col = None
        
        for col in df.columns:
            if 'return' in col.lower() and '1m' in col.lower():
                target_col = col
                returns_col = col
                break
        
        if not target_col:
            print("❌ No suitable target column found")
            return
    
    if not target_col:
        print("❌ No target column identified")
        return
    
    print(f"Using target: {target_col}")
    print(f"Using returns: {returns_col}")
    
    # Prepare features
    exclude_cols = ['Date', 'P123 ID', 'P123_ID', 'Ticker', target_col]
    if returns_col and returns_col != target_col:
        exclude_cols.append(returns_col)
    
    feature_cols = [col for col in df.columns 
                   if col not in exclude_cols and 
                   df[col].dtype in ['int64', 'float64']]
    
    print(f"Feature columns: {len(feature_cols)}")
    
    # Clean feature names
    feature_mapping = {}
    for col in feature_cols:
        cleaned = clean_feature_name(col)
        feature_mapping[col] = cleaned
    
    # Prepare data
    X = df[feature_cols].copy()
    X.columns = [feature_mapping[col] for col in X.columns]
    X = X.fillna(0)
    X = X.replace([np.inf, -np.inf], 0)
    
    y = df[target_col]
    dates = pd.to_datetime(df['Date'])
    returns = df[returns_col] if returns_col else y
    
    print(f"Final dataset: {X.shape}")
    print(f"Date range: {dates.min()} to {dates.max()}")
    
    # Load config
    with open('lofep_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Run validation
    print("\nRunning time series cross-validation...")
    
    splits = create_time_splits(dates, 
                               config['validation']['train_months'], 
                               config['validation']['validation_months'])
    
    print(f"Created {len(splits)} validation splits")
    
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
        try:
            # Create masks
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Prepare fold data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            
            # Remove NaN targets
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()
            
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            # Train model
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate portfolio performance
            val_returns_data = returns[val_mask][val_valid_mask]
            val_dates_data = dates[val_mask][val_valid_mask]
            
            if len(val_returns_data) > 0:
                # Create portfolio
                combined = pd.DataFrame({
                    'pred': y_pred,
                    'returns': val_returns_data,
                    'dates': val_dates_data
                })
                
                # Weekly rebalancing - select top 15 stocks
                portfolio_returns = []
                for date in combined['dates'].unique():
                    date_data = combined[combined['dates'] == date]
                    if len(date_data) >= 15:
                        top_stocks = date_data.nlargest(15, 'pred')
                        period_return = top_stocks['returns'].mean()
                        portfolio_returns.append(period_return)
                
                if len(portfolio_returns) > 0:
                    portfolio_returns = np.array(portfolio_returns)
                    sharpe = np.mean(portfolio_returns) / np.std(portfolio_returns) * np.sqrt(52) if np.std(portfolio_returns) > 0 else 0
                    fold_results.append(sharpe)
                    
                    print(f"Fold {fold_idx:2d} ({val_start.strftime('%Y-%m')}-{val_end.strftime('%Y-%m')}): Sharpe = {sharpe:6.3f}")
        
        except Exception as e:
            print(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Final results
    if fold_results:
        mean_sharpe = np.mean(fold_results)
        std_sharpe = np.std(fold_results)
        
        print(f"\n🎯 BASELINE VALIDATION RESULTS:")
        print(f"="*50)
        print(f"Mean Sharpe Ratio: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Number of folds: {len(fold_results)}")
        print(f"Min Sharpe: {np.min(fold_results):.4f}")
        print(f"Max Sharpe: {np.max(fold_results):.4f}")
        print(f"Features used: {len(feature_cols)}")
        
        # Compare to LOFEP baseline
        lofep_baseline = 1.7765
        print(f"\nComparison to LOFEP baseline:")
        print(f"LOFEP baseline: {lofep_baseline:.4f}")
        print(f"This validation: {mean_sharpe:.4f}")
        print(f"Difference: {mean_sharpe - lofep_baseline:+.4f}")
        
        # Save results
        results = {
            'baseline_sharpe_mean': mean_sharpe,
            'baseline_sharpe_std': std_sharpe,
            'num_folds': len(fold_results),
            'fold_results': fold_results,
            'features_used': len(feature_cols),
            'target_column': target_col,
            'returns_column': returns_col,
            'timestamp': datetime.now().isoformat()
        }
        
        import json
        with open('lofep_results/baseline_validation_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to: lofep_results/baseline_validation_results.json")
        
    else:
        print("❌ No successful validation folds")

if __name__ == "__main__":
    run_baseline_validation()
