#!/usr/bin/env python3
"""
EFFICIENT High-Trial Hyperparameter Optimization
- Multi-fidelity optimization using n_estimators as fidelity parameter
- HyperbandPruner for intelligent resource allocation
- Phase 1: 1000+ trials with early stopping and pruning
- Phase 2: Top trials with full 14,500 estimators
- Preserves signal while maximizing efficiency
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import optuna
from optuna.pruners import HyperbandPruner
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EfficientHyperparameterOptimizer:
    def __init__(self, phase1_trials=1000, phase2_trials=20):
        self.phase1_trials = phase1_trials
        self.phase2_trials = phase2_trials
        self.phase1_results = []
        self.phase2_results = []
        
    def load_and_prepare_data(self):
        """Load data using same method as proven system"""
        print("📊 Loading data for efficient optimization...")
        
        # Load datasets
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        target_df = pd.read_csv('PureEURTarget.csv')
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        
        # Clean feature names
        import re
        name_mapping = {}
        for col in features_df.columns:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')
            name_mapping[col] = clean_name
        features_df = features_df.rename(columns=name_mapping)
        
        # Merge datasets
        data = features_df.merge(
            target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        data = data.merge(
            returns_df[['Date', 'P123 ID', 'Future5DReturn']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        # Use FULL 20 years
        data['Date'] = pd.to_datetime(data['Date'])
        start_date = pd.to_datetime('2005-12-01')
        end_date = pd.to_datetime('2025-05-01')
        
        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        data = data.sort_values('Date').reset_index(drop=True)
        
        # Feature columns
        feature_cols = [col for col in data.columns if col not in 
                       ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']]
        
        print(f"✓ Data: {len(data)} samples, {len(feature_cols)} features")
        print(f"✓ Date range: {data['Date'].min().date()} to {data['Date'].max().date()}")
        
        self.data = data
        self.feature_cols = feature_cols
        
        return data
    
    def create_efficient_splits(self, n_splits=2):
        """Create time series splits optimized for efficiency"""
        print(f"📅 Creating {n_splits} time series splits with 6-month gaps...")
        
        unique_dates = sorted(self.data['Date'].unique())
        total_dates = len(unique_dates)
        dates_per_split = total_dates // (n_splits + 1)
        gap_days = 6 * 30  # 6 months
        
        splits = []
        
        for i in range(n_splits):
            train_end_idx = (i + 1) * dates_per_split
            train_end_date = unique_dates[min(train_end_idx, len(unique_dates) - 1)]
            
            val_start_date = train_end_date + timedelta(days=gap_days)
            val_start_idx = next((idx for idx, date in enumerate(unique_dates) 
                                if date >= val_start_date), len(unique_dates))
            
            val_end_idx = min(val_start_idx + dates_per_split, len(unique_dates) - 1)
            val_end_date = unique_dates[val_end_idx]
            
            if val_start_idx < len(unique_dates):
                splits.append({
                    'train_start': unique_dates[0],
                    'train_end': train_end_date,
                    'val_start': unique_dates[val_start_idx],
                    'val_end': val_end_date,
                    'fold': i + 1
                })
        
        print(f"✓ Created {len(splits)} splits with proper 6-month gaps")
        for split in splits:
            gap_days = (split['val_start'] - split['train_end']).days
            print(f"  Fold {split['fold']}: Gap = {gap_days} days")
        
        self.splits = splits
        return splits
    
    def convert_to_relevance_scores(self, targets, dates, n_levels=5):
        """Convert targets to relevance scores"""
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['relevance'] = 0
        
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']
            
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                valid_targets = date_targets[valid_mask]
                relevance_scores = pd.qcut(valid_targets, q=n_levels, labels=False, duplicates='drop')
                df.loc[date_mask & valid_mask, 'relevance'] = relevance_scores
        
        return df['relevance'].values
    
    def prepare_ranking_data(self, dates):
        """Prepare groups for ranking"""
        unique_dates = sorted(dates.unique())
        groups = []
        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)
        return groups
    
    def calculate_portfolio_metrics(self, predictions, returns, dates, top_k=20):
        """Calculate portfolio metrics"""
        portfolio_returns = []
        
        for date in sorted(dates.unique()):
            date_mask = dates == date
            date_predictions = predictions[date_mask]
            date_returns = returns[date_mask]
            
            if len(date_predictions) >= top_k:
                top_indices = np.argsort(date_predictions)[-top_k:]
                top_returns = date_returns.iloc[top_indices]
                portfolio_return = top_returns.mean() / 100.0
                portfolio_returns.append(portfolio_return)
        
        if len(portfolio_returns) == 0:
            return {'sharpe': 0}
        
        portfolio_returns = np.array(portfolio_returns)
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()
        
        # Annualize
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
        
        return {'sharpe': sharpe}
    
    def phase1_objective(self, trial, split):
        """Phase 1: Multi-fidelity objective with pruning"""
        
        # EXPANDED parameter ranges for comprehensive search
        params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'verbose': -1,
            'random_state': 42,
            
            # Multi-fidelity: n_estimators as fidelity parameter
            'n_estimators': trial.suggest_int('n_estimators', 500, 8000, step=500),
            
            # EXPANDED core parameters
            'max_depth': trial.suggest_int('max_depth', 6, 16),
            'learning_rate': trial.suggest_float('learning_rate', 0.0001, 0.01, log=True),
            'num_leaves': trial.suggest_int('num_leaves', 100, 600),
            'subsample': trial.suggest_float('subsample', 0.3, 0.8),
            'min_child_samples': trial.suggest_int('min_child_samples', 200, 1000),
            
            # EXPANDED regularization
            'lambda_l1': trial.suggest_float('lambda_l1', 0.001, 0.1, log=True),
            'lambda_l2': trial.suggest_float('lambda_l2', 0.001, 0.1, log=True),
            'min_split_gain': trial.suggest_float('min_split_gain', 0.001, 0.05, log=True),
            
            # EXPANDED feature selection
            'feature_fraction': trial.suggest_float('feature_fraction', 0.1, 0.8),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
            
            # EXPANDED other parameters
            'max_bin': trial.suggest_int('max_bin', 200, 1000),
            'min_data_in_bin': trial.suggest_int('min_data_in_bin', 5, 100),
            
            # Additional parameters for comprehensive search
            'path_smooth': trial.suggest_float('path_smooth', 1e-10, 1e-6, log=True),
            'bin_construct_sample_cnt': trial.suggest_int('bin_construct_sample_cnt', 200000, 1000000, step=100000),
        }
        
        try:
            # Prepare data
            train_mask = (self.data['Date'] >= split['train_start']) & (self.data['Date'] <= split['train_end'])
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])
            
            train_data = self.data[train_mask].copy()
            val_data = self.data[val_mask].copy()
            
            if len(train_data) == 0 or len(val_data) == 0:
                return -999
            
            # Prepare features and targets
            X_train = train_data[self.feature_cols].fillna(0)
            y_train = self.convert_to_relevance_scores(train_data['Weighted_MixRel'], train_data['Date'])
            train_groups = self.prepare_ranking_data(train_data['Date'])
            
            X_val = val_data[self.feature_cols].fillna(0)
            y_val = self.convert_to_relevance_scores(val_data['Weighted_MixRel'], val_data['Date'])
            val_groups = self.prepare_ranking_data(val_data['Date'])
            
            # Train model with early stopping for efficiency
            train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
            val_dataset = lgb.Dataset(X_val, label=y_val, group=val_groups, reference=train_dataset)
            
            # Train model with early stopping for efficiency (no pruning callback for now)
            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
            )
            
            # Make predictions
            val_predictions = model.predict(X_val)
            
            # Calculate portfolio metrics
            metrics = self.calculate_portfolio_metrics(
                val_predictions, 
                val_data['Future5DReturn'], 
                val_data['Date']
            )
            
            return metrics['sharpe']
            
        except optuna.TrialPruned:
            raise
        except Exception as e:
            print(f"Trial failed: {e}")
            return -999
    
    def phase2_objective(self, trial_params, split):
        """Phase 2: Full validation with 14,500 estimators"""
        
        # Use parameters from Phase 1 but with full estimators
        params = trial_params.copy()
        params['n_estimators'] = 14500  # FULL estimators
        
        try:
            # Prepare data (same as Phase 1)
            train_mask = (self.data['Date'] >= split['train_start']) & (self.data['Date'] <= split['train_end'])
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])
            
            train_data = self.data[train_mask].copy()
            val_data = self.data[val_mask].copy()
            
            if len(train_data) == 0 or len(val_data) == 0:
                return -999
            
            # Prepare features and targets
            X_train = train_data[self.feature_cols].fillna(0)
            y_train = self.convert_to_relevance_scores(train_data['Weighted_MixRel'], train_data['Date'])
            train_groups = self.prepare_ranking_data(train_data['Date'])
            
            X_val = val_data[self.feature_cols].fillna(0)
            y_val = self.convert_to_relevance_scores(val_data['Weighted_MixRel'], val_data['Date'])
            val_groups = self.prepare_ranking_data(val_data['Date'])
            
            # Train model with NO early stopping (full validation)
            train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
            val_dataset = lgb.Dataset(X_val, label=y_val, group=val_groups, reference=train_dataset)
            
            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Make predictions
            val_predictions = model.predict(X_val)
            
            # Calculate portfolio metrics
            metrics = self.calculate_portfolio_metrics(
                val_predictions, 
                val_data['Future5DReturn'], 
                val_data['Date']
            )
            
            return metrics['sharpe']
            
        except Exception as e:
            print(f"Phase 2 trial failed: {e}")
            return -999

    def run_phase1_optimization(self):
        """Phase 1: High-trial screening with multi-fidelity optimization"""
        print("\n" + "="*60)
        print("🚀 PHASE 1: HIGH-TRIAL SCREENING (Multi-Fidelity)")
        print("="*60)

        # Load data and create splits
        self.load_and_prepare_data()
        self.create_efficient_splits(n_splits=2)  # 2 folds for efficiency

        all_phase1_results = []

        # Run Phase 1 on each fold
        for split in self.splits:
            print(f"\n📊 PHASE 1 - FOLD {split['fold']}")
            print(f"Train: {split['train_start'].date()} to {split['train_end'].date()}")
            print(f"Val: {split['val_start'].date()} to {split['val_end'].date()}")

            # Create study with HyperbandPruner for intelligent resource allocation
            study = optuna.create_study(
                direction='maximize',
                sampler=optuna.samplers.TPESampler(seed=42),
                pruner=HyperbandPruner(
                    min_resource=500,      # Minimum n_estimators
                    max_resource=8000,     # Maximum n_estimators for Phase 1
                    reduction_factor=3     # Aggressive pruning
                )
            )

            # Optimize with many trials
            print(f"🔍 Running {self.phase1_trials} trials with HyperbandPruner...")

            study.optimize(
                lambda trial: self.phase1_objective(trial, split),
                n_trials=self.phase1_trials,
                show_progress_bar=True
            )

            # Store top trials from this fold
            fold_results = []
            for trial in study.trials:
                if trial.state == optuna.trial.TrialState.COMPLETE and trial.value is not None:
                    fold_results.append({
                        'fold': split['fold'],
                        'trial_number': trial.number,
                        'score': trial.value,
                        'params': trial.params
                    })

            # Sort by score and take top performers
            fold_results.sort(key=lambda x: x['score'], reverse=True)
            top_fold_results = fold_results[:self.phase2_trials//2]  # Top half for each fold

            all_phase1_results.extend(top_fold_results)

            print(f"✓ Fold {split['fold']} completed")
            print(f"  Best score: {study.best_value:.4f}")
            print(f"  Total trials: {len(study.trials)}")
            print(f"  Completed trials: {len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])}")
            print(f"  Pruned trials: {len([t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED])}")

        # Select overall top trials for Phase 2
        all_phase1_results.sort(key=lambda x: x['score'], reverse=True)
        self.phase1_results = all_phase1_results[:self.phase2_trials]

        print(f"\n🎯 PHASE 1 SUMMARY:")
        print(f"  Total trials run: {sum(len(study.trials) for study in [study])}")
        print(f"  Top {self.phase2_trials} trials selected for Phase 2")
        print(f"  Score range: {self.phase1_results[0]['score']:.4f} to {self.phase1_results[-1]['score']:.4f}")

        return self.phase1_results

    def run_phase2_validation(self):
        """Phase 2: Full validation of top trials with 14,500 estimators"""
        print("\n" + "="*60)
        print("🎯 PHASE 2: FULL VALIDATION (14,500 estimators)")
        print("="*60)

        phase2_results = []

        # Test each top trial from Phase 1
        for i, result in enumerate(self.phase1_results):
            print(f"\n📊 PHASE 2 - TRIAL {i+1}/{len(self.phase1_results)}")
            print(f"Phase 1 score: {result['score']:.4f}")

            trial_scores = []

            # Test on each fold with full estimators
            for split in self.splits:
                print(f"  Testing on Fold {split['fold']} with 14,500 estimators...")

                score = self.phase2_objective(result['params'], split)
                trial_scores.append(score)

                print(f"    Fold {split['fold']} score: {score:.4f}")

            # Average score across folds
            avg_score = np.mean([s for s in trial_scores if s > -999])

            phase2_result = {
                'trial_id': i + 1,
                'phase1_score': result['score'],
                'phase2_avg_score': avg_score,
                'phase2_scores': trial_scores,
                'params': result['params'],
                'improvement': avg_score - result['score']
            }

            phase2_results.append(phase2_result)

            print(f"  ✓ Trial {i+1} completed")
            print(f"    Phase 1 score: {result['score']:.4f}")
            print(f"    Phase 2 score: {avg_score:.4f}")
            print(f"    Improvement: {avg_score - result['score']:+.4f}")

        self.phase2_results = phase2_results
        return phase2_results

    def run_complete_optimization(self):
        """Run complete two-phase optimization"""
        print("="*60)
        print("🚀 EFFICIENT HIGH-TRIAL HYPERPARAMETER OPTIMIZATION")
        print("="*60)
        print(f"Phase 1: {self.phase1_trials} trials with multi-fidelity optimization")
        print(f"Phase 2: {self.phase2_trials} trials with full 14,500 estimators")

        # Phase 1: High-trial screening
        phase1_results = self.run_phase1_optimization()

        # Phase 2: Full validation
        phase2_results = self.run_phase2_validation()

        # Analyze results
        print("\n" + "="*60)
        print("📊 COMPLETE OPTIMIZATION RESULTS")
        print("="*60)

        # Sort by Phase 2 performance
        phase2_results.sort(key=lambda x: x['phase2_avg_score'], reverse=True)

        best_result = phase2_results[0]

        print(f"🏆 BEST HYPERPARAMETERS:")
        print(f"  Phase 2 Score: {best_result['phase2_avg_score']:.4f}")
        print(f"  Current Baseline: 2.187")

        if best_result['phase2_avg_score'] > 2.187:
            improvement = ((best_result['phase2_avg_score'] - 2.187) / 2.187) * 100
            print(f"  🎯 Improvement: {improvement:.1f}%")
        else:
            print(f"  📊 Current parameters are well-optimized")

        print(f"\n📋 BEST PARAMETERS:")
        for param, value in best_result['params'].items():
            print(f"  {param}: {value}")

        print(f"\n📈 TOP 5 TRIALS:")
        for i, result in enumerate(phase2_results[:5]):
            print(f"  {i+1}. Score: {result['phase2_avg_score']:.4f} "
                  f"(Phase 1: {result['phase1_score']:.4f}, "
                  f"Improvement: {result['improvement']:+.4f})")

        return phase2_results

def main():
    # Initialize optimizer with high trial counts
    optimizer = EfficientHyperparameterOptimizer(
        phase1_trials=1000,  # High-trial screening
        phase2_trials=20     # Top trials for full validation
    )

    # Run complete optimization
    results = optimizer.run_complete_optimization()

if __name__ == "__main__":
    main()
