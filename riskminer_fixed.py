#!/usr/bin/env python3
"""
RiskMiner Feature Discovery - FIXED VERSION
Uses the working basic approach with longer iterations and some enhanced operators
"""

import pandas as pd
import numpy as np
from scipy.stats import spearmanr
import warnings
import random
from datetime import datetime
import math

warnings.filterwarnings('ignore')

class MCTSNode:
    """Node class for Monte Carlo Tree Search"""
    def __init__(self, formula='', parent=None):
        self.formula = formula
        self.parent = parent
        self.children = []
        self.visits = 0
        self.value = 0
        self.ic_scores = []

    def is_fully_expanded(self):
        return len(self.children) > 0

class RiskMinerFixed:
    def __init__(self, max_iterations=5000, exploration_param=1.41):
        self.max_iterations = max_iterations
        self.exploration_param = exploration_param
        self.features_df = None
        self.target_df = None
        self.all_features = []
        self.discovered_features = []
        
    def load_data(self, features_path, target_path, target_column='Weighted_MixRel'):
        """Load features and target data"""
        print("📊 Loading data...")
        
        # Load datasets
        self.features_df = pd.read_csv(features_path)
        self.target_df = pd.read_csv(target_path)
        
        print(f"✓ Features loaded: {self.features_df.shape}")
        print(f"✓ Target loaded: {self.target_df.shape}")
        
        # Check column names and standardize
        if 'P123 ID' in self.target_df.columns:
            self.target_df = self.target_df.rename(columns={'P123 ID': 'P123_ID'})
        if 'P123 ID' in self.features_df.columns:
            self.features_df = self.features_df.rename(columns={'P123 ID': 'P123_ID'})
            
        # Merge on Date and P123_ID
        merged_data = self.features_df.merge(
            self.target_df[['Date', 'P123_ID', target_column]], 
            on=['Date', 'P123_ID'], 
            how='inner'
        )
        
        print(f"✓ Merged data: {merged_data.shape}")
        
        # Separate features and target - use original feature names (they worked before)
        exclude_cols = ['Date', 'P123_ID', 'Ticker', target_column]
        feature_cols = [col for col in merged_data.columns 
                       if col not in exclude_cols and merged_data[col].dtype in ['int64', 'float64']]
        
        self.X = merged_data[feature_cols].fillna(0)
        self.y = merged_data[target_column]
        self.all_features = feature_cols
        
        print(f"✓ Features for discovery: {len(self.all_features)}")
        print(f"✓ Sample size: {len(self.X)}")
        
        return self.X, self.y, self.all_features
    
    def ucb1(self, node):
        """Upper Confidence Bound for node selection"""
        if node.visits == 0:
            return np.inf
        
        parent_visits = node.parent.visits if node.parent is not None else 1
        exploitation = node.value / node.visits
        exploration = self.exploration_param * np.sqrt(np.log(parent_visits) / node.visits)
        return exploitation + exploration
    
    def select_best_node(self, node):
        """Select the most promising node using UCB1"""
        current_node = node
        while current_node.children:
            ucb_values = [self.ucb1(child) for child in current_node.children]
            current_node = current_node.children[np.argmax(ucb_values)]
        return current_node
    
    def generate_formula(self):
        """Generate enhanced formulas with some safe advanced operators"""
        
        # Enhanced but safe formula types
        formula_types = [
            'binary',           # feature1 op feature2 (60% of time)
            'binary',
            'binary', 
            'binary',
            'binary',
            'binary',
            'ratio_safe',       # feature1 / (feature2 + 1) 
            'difference',       # feature1 - feature2
            'product',          # feature1 * feature2
            'sum',              # feature1 + feature2
            'max_min',          # max(feature1, feature2)
            'weighted_avg',     # 0.7 * feature1 + 0.3 * feature2
            'power_safe',       # feature1^2 / (feature2 + 1)
            'abs_diff',         # abs(feature1 - feature2)
            'relative_safe'     # (feature1 - feature2) / (abs(feature1) + abs(feature2) + 1)
        ]
        
        formula_type = np.random.choice(formula_types)
        feature1 = np.random.choice(self.all_features)
        feature2 = np.random.choice(self.all_features)
        
        # Avoid self-operations
        while feature2 == feature1:
            feature2 = np.random.choice(self.all_features)
        
        # Clean feature names for safe evaluation
        f1_clean = f"`{feature1}`" if any(char in feature1 for char in [' ', '-', '&', '(', ')', '%', '/', '*', '+', ':', '"', "'", ',', '.', '=', '<', '>', '!']) else feature1
        f2_clean = f"`{feature2}`" if any(char in feature2 for char in [' ', '-', '&', '(', ')', '%', '/', '*', '+', ':', '"', "'", ',', '.', '=', '<', '>', '!']) else feature2
        
        if formula_type == 'binary':
            operators = ['+', '-', '*', '/']
            operator = np.random.choice(operators)
            if operator == '/':
                formula = f"{f1_clean} / ({f2_clean} + 0.001)"
            else:
                formula = f"{f1_clean} {operator} {f2_clean}"
                
        elif formula_type == 'ratio_safe':
            formula = f"{f1_clean} / ({f2_clean} + 1)"
            
        elif formula_type == 'difference':
            formula = f"{f1_clean} - {f2_clean}"
            
        elif formula_type == 'product':
            formula = f"{f1_clean} * {f2_clean}"
            
        elif formula_type == 'sum':
            formula = f"{f1_clean} + {f2_clean}"
            
        elif formula_type == 'max_min':
            func = np.random.choice(['max', 'min'])
            formula = f"{func}({f1_clean}, {f2_clean})"
            
        elif formula_type == 'weighted_avg':
            weight1 = np.random.uniform(0.1, 0.9)
            weight2 = 1 - weight1
            formula = f"{weight1:.2f} * {f1_clean} + {weight2:.2f} * {f2_clean}"
            
        elif formula_type == 'power_safe':
            formula = f"{f1_clean} ** 2 / ({f2_clean} + 1)"
            
        elif formula_type == 'abs_diff':
            formula = f"abs({f1_clean} - {f2_clean})"
            
        elif formula_type == 'relative_safe':
            formula = f"({f1_clean} - {f2_clean}) / (abs({f1_clean}) + abs({f2_clean}) + 1)"
            
        return formula
    
    def evaluate_formula(self, formula):
        """Safely evaluate a formula on the dataset"""
        try:
            # Use pandas eval with the dataframe directly
            result = self.X.eval(formula)
            
            # Handle infinite and extreme values
            result = pd.Series(result).replace([np.inf, -np.inf], np.nan)
            
            # Cap extreme values
            if result.std() > 0:
                mean_val = result.mean()
                std_val = result.std()
                lower_bound = mean_val - 5 * std_val
                upper_bound = mean_val + 5 * std_val
                result = result.clip(lower_bound, upper_bound)
            
            return result
            
        except Exception as e:
            return pd.Series(np.nan, index=self.X.index)
    
    def calculate_information_coefficient(self, feature_values):
        """Calculate Information Coefficient (Spearman correlation)"""
        # Remove NaN values
        valid_mask = ~(feature_values.isna() | self.y.isna())
        
        if valid_mask.sum() < 10:
            return 0.0
        
        feature_clean = feature_values[valid_mask]
        target_clean = self.y[valid_mask]
        
        try:
            ic, p_value = spearmanr(feature_clean, target_clean)
            return ic if not np.isnan(ic) else 0.0
        except:
            return 0.0
    
    def simulate_alpha_performance(self, node):
        """Evaluate the performance of a formula"""
        formula = node.formula
        
        # Evaluate the formula
        alpha_feature = self.evaluate_formula(formula)
        
        # Calculate Information Coefficient
        ic = self.calculate_information_coefficient(alpha_feature)
        
        # Store the IC score for analysis
        node.ic_scores.append(ic)
        
        return abs(ic)
    
    def expand(self, node):
        """Expand a node by adding a new child with a new formula - FIXED VERSION"""
        new_formula = self.generate_formula()
        child_node = MCTSNode(formula=new_formula, parent=node)
        node.children.append(child_node)
        return child_node
    
    def backpropagate(self, node, reward):
        """Backpropagate the reward up the tree"""
        while node is not None:
            node.visits += 1
            node.value += reward
            node = node.parent
    
    def run_feature_discovery(self):
        """Run the main MCTS feature discovery process"""
        print("🚀 Starting FIXED RiskMiner Feature Discovery...")
        print(f"Parameters: {self.max_iterations} iterations, exploration={self.exploration_param}")
        
        # Initialize root node
        root_node = MCTSNode(formula='ROOT')
        
        # Run MCTS iterations
        best_ic_so_far = 0
        for i in range(self.max_iterations):
            if (i + 1) % 500 == 0:
                print(f"Iteration {i + 1}/{self.max_iterations} - Best IC so far: {best_ic_so_far:.4f}")
            
            # Selection: Find the best node to expand
            node_to_expand = self.select_best_node(root_node)
            
            # Expansion: Add a new child node
            expanded_node = self.expand(node_to_expand)
            
            # Simulation: Evaluate the new formula
            reward = self.simulate_alpha_performance(expanded_node)
            
            # Track best IC found
            if reward > best_ic_so_far:
                best_ic_so_far = reward
                if reward > 0.03:  # Report significant improvements
                    print(f"  🎯 New best IC: {reward:.4f} at iteration {i + 1}")
                    print(f"     Formula: {expanded_node.formula}")
            
            # Backpropagation: Update node values
            self.backpropagate(expanded_node, reward)
        
        # Extract top discoveries
        self.extract_top_features(root_node)
        
        return self.discovered_features

    def extract_top_features(self, root_node, top_k=20):
        """Extract the top discovered features"""
        print("\n📈 Extracting top discovered features...")

        # Collect all nodes with formulas using iterative approach
        all_nodes = []
        nodes_to_visit = [root_node]
        visited = set()

        while nodes_to_visit:
            node = nodes_to_visit.pop()
            node_id = id(node)

            if node_id in visited:
                continue
            visited.add(node_id)

            if node.formula and node.formula != 'ROOT' and node.visits > 0:
                avg_ic = node.value / node.visits
                all_nodes.append((node.formula, avg_ic, node.visits, node.ic_scores))

            # Add children to visit list
            for child in node.children:
                if id(child) not in visited:
                    nodes_to_visit.append(child)

        # Sort by average IC score
        all_nodes.sort(key=lambda x: x[1], reverse=True)

        # Get top features
        self.discovered_features = []

        print(f"\n🏆 TOP {min(top_k, len(all_nodes))} DISCOVERED FEATURES:")
        print("="*80)

        for i, (formula, avg_ic, visits, ic_scores) in enumerate(all_nodes[:top_k]):
            feature_name = f"MCTS_Feature_{i+1:02d}"

            # Calculate statistics
            ic_std = np.std(ic_scores) if len(ic_scores) > 1 else 0
            ic_min = np.min(ic_scores)
            ic_max = np.max(ic_scores)

            self.discovered_features.append({
                'feature_name': feature_name,
                'formula': formula,
                'avg_ic': avg_ic,
                'ic_std': ic_std,
                'ic_min': ic_min,
                'ic_max': ic_max,
                'visits': visits,
                'rank': i + 1
            })

            print(f"{i+1:2d}. {feature_name}")
            print(f"    Formula: {formula}")
            print(f"    Avg IC: {avg_ic:.4f} ± {ic_std:.4f}")
            print(f"    Range: [{ic_min:.4f}, {ic_max:.4f}]")
            print(f"    Visits: {visits}")
            print()

        return self.discovered_features

    def save_discovered_features(self, filename=None):
        """Save discovered features to CSV"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'riskminer_fixed_features_{timestamp}.csv'

        df = pd.DataFrame(self.discovered_features)
        df.to_csv(filename, index=False)

        print(f"✅ Discovered features saved to: {filename}")
        return filename


def main():
    """Main execution function"""
    print("🔬 FIXED RiskMiner Feature Discovery for P123 Dataset")
    print("🛠️ Fixed tree expansion with 5000 iterations and enhanced operators")
    print("="*70)

    # Initialize Fixed RiskMiner
    riskminer = RiskMinerFixed(max_iterations=5000, exploration_param=1.41)

    # Load data
    features_path = 'Final_Comprehensive_ML_Features_v10.csv'
    target_path = 'PureEURTarget.csv'

    X, y, all_features = riskminer.load_data(features_path, target_path)

    # Run feature discovery
    discovered_features = riskminer.run_feature_discovery()

    # Save results
    filename = riskminer.save_discovered_features()

    print(f"\n🎉 Feature discovery completed!")
    print(f"📊 Discovered {len(discovered_features)} promising feature combinations")
    print(f"💾 Results saved to: {filename}")


if __name__ == "__main__":
    main()
