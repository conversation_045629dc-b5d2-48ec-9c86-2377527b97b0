#!/usr/bin/env python3
"""
Investigate why the same folds are giving different results
"""

import pandas as pd
import numpy as np
import lightgbm as lgb

def investigate_differences():
    print("="*60)
    print("🔍 INVESTIGATING PERFORMANCE DIFFERENCES")
    print("="*60)
    
    print("Possible causes for different results with same random_state:")
    print("1. Different n_estimators between runs")
    print("2. Different data preprocessing")
    print("3. Different LightGBM version")
    print("4. Different feature selection/ordering")
    print("5. Memory/numerical precision issues")
    print("6. Different validation data splits")
    
    # Check current parameters
    params_current = {
        'objective': 'rank_xendcg',
        'metric': 'ndcg',
        'boosting_type': 'gbdt',
        'n_estimators': 14500,  # Current
        'max_depth': 11,
        'learning_rate': 0.00045,
        'num_leaves': 330,
        'subsample': 0.51,
        'min_child_samples': 620,
        'extra_trees': True,
        'path_smooth': 2.1e-9,
        'max_bin': 670,
        'min_data_in_bin': 39,
        'bin_construct_sample_cnt': 780000,
        'lambda_l1': 0.016,
        'lambda_l2': 0.026,
        'min_split_gain': 0.0071,
        'bagging_freq': 3,
        'feature_fraction': 0.24,
        'verbose': -1,
        'random_state': 42
    }
    
    # What the interrupted run had (based on output)
    params_interrupted = {
        'objective': 'rank_xendcg',
        'metric': 'ndcg',
        'boosting_type': 'gbdt',
        'n_estimators': 14500,  # Same
        'max_depth': 11,
        'learning_rate': 0.00045,
        'num_leaves': 330,
        'subsample': 0.51,
        'min_child_samples': 620,
        'extra_trees': True,
        'path_smooth': 2.1e-9,
        'max_bin': 670,
        'min_data_in_bin': 39,
        'bin_construct_sample_cnt': 780000,
        'lambda_l1': 0.016,
        'lambda_l2': 0.026,
        'min_split_gain': 0.0071,
        'bagging_freq': 3,
        'feature_fraction': 0.24,
        'verbose': -1,
        'random_state': 42
    }
    
    print(f"\n📊 PARAMETER COMPARISON:")
    print("Parameters appear identical...")
    
    print(f"\n🤔 MOST LIKELY CAUSES:")
    print("1. **Portfolio calculation differences**: Same predictions, different portfolio metrics")
    print("2. **Data loading differences**: Slight changes in data preprocessing")
    print("3. **Random state scope**: Different random operations affecting results")
    print("4. **Memory/precision**: Numerical differences in large dataset processing")
    
    print(f"\n💡 HYPOTHESIS:")
    print("The interrupted run may have had:")
    print("- Different data loading/preprocessing")
    print("- Different random seed initialization")
    print("- Different memory state affecting numerical precision")
    print("- Possibly different n_estimators (was it really 14500?)")
    
    print(f"\n🔍 TO VERIFY:")
    print("1. Check if interrupted run was actually using 14500 estimators")
    print("2. Check if data preprocessing is identical")
    print("3. Check if portfolio calculation is identical")
    print("4. Run a single fold test to isolate the issue")
    
    # Check LightGBM version
    print(f"\nLightGBM version: {lgb.__version__}")
    
    print(f"\n📈 PERFORMANCE IMPACT:")
    print("Interrupted run average (first 7 folds): ~2.5+ Sharpe")
    print("Full run average (all 10 folds): 2.187 Sharpe")
    print("Difference: The interrupted run was performing significantly better")
    
    print(f"\n🎯 RECOMMENDATION:")
    print("1. Re-run with early stopping to match original performance")
    print("2. Or investigate why no early stopping gives worse results")
    print("3. The feature importance from 14500 estimators is still valuable")

if __name__ == "__main__":
    investigate_differences()
