#!/usr/bin/env python3
"""
Quick test of no early stopping with fewer estimators to see feature importance difference
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import shap
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def quick_test():
    print("="*60)
    print("🚀 QUICK TEST: NO EARLY STOPPING")
    print("="*60)
    
    # Load and prepare data (same as main system)
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    # Clean feature names
    import re
    name_mapping = {}
    for col in features_df.columns:
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
        clean_name = re.sub(r'_+', '_', clean_name)
        clean_name = clean_name.strip('_')
        name_mapping[col] = clean_name
    features_df = features_df.rename(columns=name_mapping)
    
    # Merge datasets
    data = features_df.merge(
        target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
        left_on=['Date', 'P123_ID'], 
        right_on=['Date', 'P123 ID'],
        how='inner'
    )
    
    data = data.merge(
        returns_df[['Date', 'P123 ID', 'Future5DReturn']], 
        left_on=['Date', 'P123_ID'], 
        right_on=['Date', 'P123 ID'],
        how='inner'
    )
    
    # Filter time period
    data['Date'] = pd.to_datetime(data['Date'])
    start_date = pd.to_datetime('2020-01-01')  # Use recent data for speed
    end_date = pd.to_datetime('2022-12-31')
    
    data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
    data = data.sort_values('Date').reset_index(drop=True)
    
    feature_cols = [col for col in data.columns if col not in 
                   ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']]
    
    print(f"Quick test data: {len(data)} samples, {len(feature_cols)} features")
    print(f"Date range: {data['Date'].min().date()} to {data['Date'].max().date()}")
    
    # Simple train/test split
    split_date = pd.to_datetime('2021-06-01')
    train_data = data[data['Date'] < split_date]
    test_data = data[data['Date'] >= split_date]
    
    print(f"Train: {len(train_data)} samples")
    print(f"Test: {len(test_data)} samples")
    
    # Convert to relevance scores
    def convert_to_relevance_scores(targets, dates, n_levels=5):
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['relevance'] = 0
        
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']
            
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                valid_targets = date_targets[valid_mask]
                relevance_scores = pd.qcut(valid_targets, q=n_levels, labels=False, duplicates='drop')
                df.loc[date_mask & valid_mask, 'relevance'] = relevance_scores
        
        return df['relevance'].values
    
    def prepare_ranking_data(dates):
        unique_dates = sorted(dates.unique())
        groups = []
        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)
        return groups
    
    # Prepare training data
    X_train = train_data[feature_cols].fillna(0)
    y_train = convert_to_relevance_scores(train_data['Weighted_MixRel'], train_data['Date'])
    train_groups = prepare_ranking_data(train_data['Date'])
    
    X_test = test_data[feature_cols].fillna(0)
    y_test = convert_to_relevance_scores(test_data['Weighted_MixRel'], test_data['Date'])
    test_groups = prepare_ranking_data(test_data['Date'])
    
    # Test with different n_estimators
    estimator_counts = [100, 500, 1000]
    
    for n_est in estimator_counts:
        print(f"\n🧠 Testing with {n_est} estimators (NO EARLY STOPPING)...")
        
        params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'n_estimators': n_est,
            'max_depth': 11,
            'learning_rate': 0.00045,
            'num_leaves': 330,
            'subsample': 0.51,
            'min_child_samples': 620,
            'extra_trees': True,
            'path_smooth': 2.1e-9,
            'max_bin': 670,
            'min_data_in_bin': 39,
            'bin_construct_sample_cnt': 780000,
            'lambda_l1': 0.016,
            'lambda_l2': 0.026,
            'min_split_gain': 0.0071,
            'bagging_freq': 3,
            'feature_fraction': 0.24,
            'verbose': -1,
            'random_state': 42
        }
        
        # Train model
        train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
        test_dataset = lgb.Dataset(X_test, label=y_test, group=test_groups, reference=train_dataset)
        
        model = lgb.train(
            params,
            train_dataset,
            valid_sets=[test_dataset],
            callbacks=[lgb.log_evaluation(0)]
        )
        
        # Check feature importance
        feature_importance = model.feature_importance(importance_type='gain')
        nonzero_features = np.sum(feature_importance > 0)
        zero_features = np.sum(feature_importance == 0)
        
        print(f"  Features with non-zero importance: {nonzero_features}/{len(feature_cols)} ({nonzero_features/len(feature_cols)*100:.1f}%)")
        print(f"  Features with zero importance: {zero_features}/{len(feature_cols)} ({zero_features/len(feature_cols)*100:.1f}%)")
        
        # Quick SHAP on small sample
        sample_size = 100
        sample_data = test_data.sample(n=sample_size, random_state=42)
        X_sample = sample_data[feature_cols].fillna(0)
        
        explainer = shap.TreeExplainer(model)
        shap_values = explainer.shap_values(X_sample)
        shap_importance = np.abs(shap_values).mean(axis=0)
        
        nonzero_shap = np.sum(shap_importance > 0)
        zero_shap = np.sum(shap_importance == 0)
        
        print(f"  SHAP non-zero features: {nonzero_shap}/{len(feature_cols)} ({nonzero_shap/len(feature_cols)*100:.1f}%)")
        print(f"  SHAP zero features: {zero_shap}/{len(feature_cols)} ({zero_shap/len(feature_cols)*100:.1f}%)")

if __name__ == "__main__":
    quick_test()
