#!/usr/bin/env python3
"""
Fix the final validation for LOFEP results
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
from pathlib import Path
import json
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility"""
    import re
    
    # Convert to string and handle any encoding issues
    cleaned = str(name)
    
    # Replace common problematic characters with meaningful substitutions
    replacements = {
        ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_', 
        '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_', 
        '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
        '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
        '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
        '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    # Remove any remaining special characters (keep only alphanumeric and underscore)
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)
    
    # Remove consecutive underscores
    cleaned = re.sub(r'_+', '_', cleaned)
    
    # Remove leading/trailing underscores
    cleaned = cleaned.strip('_')
    
    # Ensure it starts with letter or underscore (not digit)
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    
    # Ensure it's not empty
    if not cleaned:
        cleaned = 'Feature_Unknown'
    
    return cleaned

def load_config():
    """Load LOFEP configuration"""
    with open('lofep_config.yaml', 'r') as f:
        return yaml.safe_load(f)

def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3):
    """Create time-based train/validation splits"""
    
    splits = []
    
    # Sort dates and get unique dates
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    # Create monthly periods
    current_date = min_date
    
    while current_date < max_date:
        # Calculate split dates
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        # Check if we have enough data for validation
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        # Move to next validation period
        current_date = val_start
    
    return splits

def run_final_validation():
    """Run final validation with proper feature name cleaning"""
    
    print("🔧 Running Fixed Final Validation")
    print("="*50)
    
    # Load configuration
    config = load_config()
    
    # Load the enhanced dataset
    print("Loading enhanced dataset...")
    enhanced_data = pd.read_csv('lofep_results/enhanced_dataset_20250711_165917.csv')
    print(f"Enhanced dataset shape: {enhanced_data.shape}")
    
    # Load selected features
    print("Loading selected features...")
    selected_df = pd.read_csv('lofep_results/selected_features_20250711_165917.csv')
    selected_features = selected_df['feature_name'].tolist()
    print(f"Selected features: {len(selected_features)}")
    
    # Load performance history to get the truly beneficial features
    print("Loading performance history...")
    perf_df = pd.read_csv('lofep_results/feature_performance_history_20250711_165917.csv')
    
    # Find features with positive Sharpe improvement
    beneficial_features = []
    for idx, row in perf_df.iterrows():
        if row['sharpe_improvement'] > 0.02:  # Minimum threshold
            feature_name = row.name if isinstance(row.name, str) else idx
            beneficial_features.append(feature_name)
            print(f"✓ {feature_name}: +{row['sharpe_improvement']:.4f} Sharpe")
    
    print(f"\nFound {len(beneficial_features)} beneficial features")
    
    # Prepare data for validation
    exclude_cols = ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']
    
    # Get baseline features (original features)
    all_features = [col for col in enhanced_data.columns 
                   if col not in exclude_cols and 
                   enhanced_data[col].dtype in ['int64', 'float64']]
    
    # Identify original features (those without transformation indicators)
    transformation_indicators = ['_rank', '_log', '_sqrt', '_zscore', '_percentile',
                               '_rolling_', '_momentum_', '_mean_reversion_', '_volatility_',
                               '_vs_', '_ratio', '_x_', '_div_', '_Market_Cap_Quintile']
    
    original_features = []
    for col in all_features:
        if not any(indicator in col for indicator in transformation_indicators):
            original_features.append(col)
    
    print(f"Original features: {len(original_features)}")
    
    # Create final feature set: original + beneficial generated features
    final_features = original_features + beneficial_features
    final_features = list(set(final_features))  # Remove duplicates
    
    print(f"Final feature set: {len(final_features)} features")
    
    # Clean feature names
    feature_mapping = {}
    cleaned_features = []
    for feature in final_features:
        if feature in enhanced_data.columns:
            cleaned_name = clean_feature_name(feature)
            feature_mapping[feature] = cleaned_name
            cleaned_features.append(cleaned_name)
    
    # Prepare final dataset
    X = enhanced_data[list(feature_mapping.keys())].copy()
    X.columns = [feature_mapping[col] for col in X.columns]
    X = X.fillna(0)
    
    y = enhanced_data['Weighted_MixRel']
    dates = pd.to_datetime(enhanced_data['Date'])
    returns = enhanced_data['Future5DReturn']
    
    print(f"Final validation dataset: {X.shape}")
    
    # Run time series cross-validation
    print("\nRunning time series cross-validation...")
    
    splits = create_time_splits(dates, 
                               config['validation']['train_months'], 
                               config['validation']['validation_months'])
    
    print(f"Created {len(splits)} validation splits")
    
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
        try:
            # Create masks for this fold
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            # Check if we have enough data
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Prepare data for this fold
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            X_train = X_train.replace([np.inf, -np.inf], 0)
            X_val = X_val.replace([np.inf, -np.inf], 0)
            
            # Remove NaN from target
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()
            
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            # Train LightGBM model
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate Sharpe ratio (simplified)
            val_returns_data = returns[val_mask][val_valid_mask]
            
            if len(val_returns_data) > 0:
                # Create portfolio returns based on predictions
                combined = pd.DataFrame({
                    'pred': y_pred,
                    'returns': val_returns_data,
                    'dates': dates[val_mask][val_valid_mask]
                })
                
                # Group by date and select top 15 stocks
                portfolio_returns = []
                for date in combined['dates'].unique():
                    date_data = combined[combined['dates'] == date]
                    if len(date_data) >= 15:
                        top_stocks = date_data.nlargest(15, 'pred')
                        period_return = top_stocks['returns'].mean()
                        portfolio_returns.append(period_return)
                
                if len(portfolio_returns) > 0:
                    portfolio_returns = np.array(portfolio_returns)
                    sharpe = np.mean(portfolio_returns) / np.std(portfolio_returns) * np.sqrt(52) if np.std(portfolio_returns) > 0 else 0
                    fold_results.append(sharpe)
                    
                    print(f"Fold {fold_idx}: Sharpe = {sharpe:.4f}")
        
        except Exception as e:
            print(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Calculate final results
    if fold_results:
        mean_sharpe = np.mean(fold_results)
        std_sharpe = np.std(fold_results)
        
        print(f"\n🎉 Final Validation Results:")
        print(f"Mean Sharpe Ratio: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Number of folds: {len(fold_results)}")
        print(f"Min Sharpe: {np.min(fold_results):.4f}")
        print(f"Max Sharpe: {np.max(fold_results):.4f}")
        
        # Save results
        results = {
            'final_sharpe_mean': mean_sharpe,
            'final_sharpe_std': std_sharpe,
            'num_folds': len(fold_results),
            'fold_results': fold_results,
            'beneficial_features': beneficial_features,
            'final_features_count': len(final_features),
            'timestamp': datetime.now().isoformat()
        }
        
        with open('lofep_results/final_validation_fixed.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to: lofep_results/final_validation_fixed.json")
        
    else:
        print("❌ No successful validation folds")

if __name__ == "__main__":
    run_final_validation()
