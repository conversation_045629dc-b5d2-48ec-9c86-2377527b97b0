#!/usr/bin/env python3
"""
LightGBM Regression Validation System - Standard Regression (No Ranking)
Uses original high-estimator hyperparameters with continuous target regression
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import shap
from datetime import datetime, timedelta
import warnings
import json
from sklearn.metrics import mean_squared_error, mean_absolute_error
warnings.filterwarnings('ignore')

class LightGBMRegressionValidator:
    def __init__(self):
        # Original optimized hyperparameters (high estimators, regression)
        self.params = {
            'objective': 'regression',  # Standard regression instead of ranking
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            
            # ORIGINAL HIGH-PERFORMANCE HYPERPARAMETERS
            'n_estimators': 14500,  # FULL original hyperparameters
            'max_depth': 11,
            'learning_rate': 0.00045,
            'num_leaves': 330,
            'subsample': 0.51,
            'min_child_samples': 620,
            'extra_trees': True,
            'path_smooth': 2.1e-9,
            'max_bin': 670,
            'min_data_in_bin': 39,
            'bin_construct_sample_cnt': 780000,
            'lambda_l1': 0.016,
            'lambda_l2': 0.026,
            'min_split_gain': 0.0071,
            'bagging_freq': 3,
            'feature_fraction': 0.24,
            'verbose': -1,
            'random_state': 42,
            'n_jobs': -1
        }
        
        # Enhanced categorical feature handling
        self.categorical_features = [
            'Industry_Code', 'SubIndustry_Code', 'SubSector_Code',
            'Death_Cross_Event_Binary', 'Golden_Cross_Event_Binary',
            'Nordic_Exchanges', 'Banks_SubSector', 'Cyclicals_Sector',
            'Energy_Sector', 'Financial_Sector', 'Healthcare_Sector',
            'Industrial_Sector', 'Materials_Sector', 'Tech_Sector'
        ]
        
        self.categorical_indices = []
        self.data = None
        self.feature_cols = None
        self.splits = []
        self.results = []
        self.name_mapping = {}

    def clean_feature_names(self, df):
        """Clean feature names for LightGBM compatibility"""
        name_mapping = {}
        df_clean = df.copy()

        for col in df.columns:
            clean_name = col.replace(' ', '_').replace('(', '').replace(')', '').replace('%', 'Pct')
            clean_name = clean_name.replace('/', '_').replace('-', '_').replace('+', 'Plus')
            clean_name = clean_name.replace('&', 'And').replace('__', '_')
            # Remove special JSON characters that LightGBM doesn't support
            clean_name = clean_name.replace('"', '').replace("'", '').replace('\\', '')
            clean_name = clean_name.replace('[', '').replace(']', '').replace('{', '').replace('}', '')
            clean_name = clean_name.replace(':', '').replace(',', '').replace('.', '_')
            clean_name = clean_name.replace('*', 'Star').replace('?', 'Q').replace('!', 'Excl')
            # Ensure it starts with letter or underscore
            if clean_name and clean_name[0].isdigit():
                clean_name = 'F_' + clean_name
            if clean_name != col:
                name_mapping[clean_name] = col
                df_clean = df_clean.rename(columns={col: clean_name})

        return df_clean, name_mapping

    def process_categorical_features(self, X, feature_cols):
        """Process categorical features"""
        print("🏷️ Processing categorical features...")
        
        for i, col in enumerate(feature_cols):
            if any(cat in col for cat in self.categorical_features):
                self.categorical_indices.append(i)
                print(f"  ✓ Processed categorical feature: {col} (index {i})")
        
        print(f"✓ Found {len(self.categorical_indices)} categorical features")
        return X

    def remove_infinite_values(self, X, y, dates, tickers, returns):
        """Remove rows with infinite values"""
        print("🧹 Checking for infinite values...")

        # Convert to numeric and handle infinite values
        X_numeric = pd.DataFrame(X).select_dtypes(include=[np.number])
        if len(X_numeric.columns) > 0:
            inf_mask = np.isinf(X_numeric.values).any(axis=1)

            if inf_mask.sum() > 0:
                print(f"Removing {inf_mask.sum()} rows with infinite values")
                X = X[~inf_mask]
                y = y[~inf_mask]
                dates = dates[~inf_mask]
                tickers = tickers[~inf_mask]
                returns = returns[~inf_mask]
            else:
                print("✓ No infinite values found")
        else:
            print("✓ No numeric columns to check for infinite values")

        return X, y, dates, tickers, returns

    def load_and_prepare_data(self):
        """Load and merge all data files"""
        print("📊 Loading data...")

        # Load datasets
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        print(f"✓ Features loaded: {features_df.shape}")

        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        print(f"✓ Future returns loaded: {returns_df.shape}")

        target_df = pd.read_csv('PureEURTarget.csv')
        print(f"✓ Targets loaded: {target_df.shape}")

        # Clean feature names
        print("🧹 Cleaning feature names...")
        features_df, self.name_mapping = self.clean_feature_names(features_df)
        target_df, _ = self.clean_feature_names(target_df)
        returns_df, _ = self.clean_feature_names(returns_df)

        # Merge datasets
        print("🔗 Merging datasets...")
        data = features_df.merge(target_df, on=['Date', 'P123_ID'], how='inner')
        data = data.merge(returns_df, on=['Date', 'P123_ID'], how='inner')

        print(f"✓ Merged data: {data.shape}")

        # Convert dates and filter time period
        data['Date'] = pd.to_datetime(data['Date'])
        start_date = pd.to_datetime('2005-12-01')
        end_date = pd.to_datetime('2025-05-01')
        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        print(f"✓ Filtered to {start_date.date()} - {end_date.date()}: {data.shape}")

        # Sort by date
        data = data.sort_values('Date').reset_index(drop=True)

        # Identify feature columns (exclude metadata and object columns)
        exclude_cols = ['Date', 'P123_ID', 'Ticker', 'Ticker_x', 'Ticker_y', 'Weighted_MixRel', 'Future5DReturn']
        feature_cols = [col for col in data.columns if col not in exclude_cols and data[col].dtype in ['int64', 'float64', 'bool']]

        print(f"✓ Feature columns: {len(feature_cols)}")

        # Extract arrays for preprocessing
        X = data[feature_cols].copy()
        y = data['Weighted_MixRel'].values  # Use continuous target for regression
        dates = data['Date'].values
        tickers = data['Ticker'].values if 'Ticker' in data.columns else data['P123_ID'].values
        returns = data['Future5DReturn'].values

        # Process categorical features
        X = self.process_categorical_features(X, feature_cols)

        # Remove infinite values
        X_clean, y_clean, dates_clean, tickers_clean, returns_clean = self.remove_infinite_values(
            X.values, y, dates, tickers, returns
        )

        # Reconstruct cleaned data
        data_clean = pd.DataFrame(X_clean, columns=feature_cols)
        data_clean['Date'] = dates_clean
        data_clean['P123_ID'] = tickers_clean
        data_clean['Weighted_MixRel'] = y_clean
        data_clean['Future5DReturn'] = returns_clean

        print(f"✓ Final cleaned data: {data_clean.shape}")

        self.data = data_clean
        self.feature_cols = feature_cols

        return data_clean

    def create_time_series_splits(self):
        """Create time series splits with 6-month gaps"""
        print("📅 Creating 10 time series splits with 6-month gaps...")
        
        dates = pd.to_datetime(self.data['Date'])
        date_range = dates.max() - dates.min()
        total_days = date_range.days
        
        print(f"Date range: {dates.min().date()} to {dates.max().date()}")
        print(f"Total unique dates: {dates.nunique()}")
        
        splits = []
        
        for i in range(10):
            # Calculate training period (expanding window)
            train_start = dates.min()
            train_end = dates.min() + timedelta(days=int(total_days * (0.2 + i * 0.08)))
            
            # 6-month gap
            gap_start = train_end
            gap_end = train_end + timedelta(days=180)
            
            # Validation period (18 months)
            val_start = gap_end
            val_end = gap_end + timedelta(days=540)
            
            if val_end <= dates.max():
                splits.append({
                    'fold': i + 1,
                    'train_start': train_start,
                    'train_end': train_end,
                    'val_start': val_start,
                    'val_end': val_end
                })
        
        print(f"✓ Created {len(splits)} valid splits")
        for split in splits:
            print(f"  Fold {split['fold']}: Train {split['train_start'].date()} to {split['train_end'].date()}, Val {split['val_start'].date()} to {split['val_end'].date()}")
        
        self.splits = splits
        return splits

    def calculate_portfolio_metrics(self, predictions, returns, dates, top_k=20):
        """Calculate portfolio performance metrics using regression predictions"""
        portfolio_returns = []

        # Convert to pandas Series for easier handling
        dates_series = pd.to_datetime(dates)
        returns_series = pd.Series(returns.values if hasattr(returns, 'values') else returns)
        predictions_series = pd.Series(predictions)

        # Group by date and select top k stocks
        unique_dates = sorted(dates_series.unique())

        for date in unique_dates:
            date_mask = dates_series == date
            date_indices = np.where(date_mask)[0]

            if len(date_indices) >= top_k:
                date_predictions = predictions_series.iloc[date_indices]
                date_returns = returns_series.iloc[date_indices]

                # Select top k stocks by prediction
                top_indices = np.argsort(date_predictions.values)[-top_k:]
                top_returns = date_returns.iloc[top_indices]
                portfolio_return = top_returns.mean() / 100.0  # Convert percentage to decimal
                portfolio_returns.append(portfolio_return)

        if len(portfolio_returns) == 0:
            return {'sharpe': 0, 'cagr': 0, 'max_drawdown': 0, 'total_return': 0}

        portfolio_returns = np.array(portfolio_returns)

        # Calculate performance metrics
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()

        # Annualize (assuming weekly rebalancing)
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0

        # CAGR
        cumulative_return = np.prod(1 + portfolio_returns) - 1
        n_years = len(portfolio_returns) / 52
        cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 else 0

        # Max drawdown
        cumulative_wealth = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_wealth)
        drawdowns = (cumulative_wealth - running_max) / running_max
        max_drawdown = np.min(drawdowns)

        return {
            'sharpe': sharpe,
            'cagr': cagr,
            'max_drawdown': max_drawdown,
            'total_return': cumulative_return,
            'n_periods': len(portfolio_returns),
            'volatility': annual_volatility
        }

    def run_validation(self):
        """Run the complete regression validation process"""
        print("="*60)
        print("🚀 STARTING LIGHTGBM REGRESSION VALIDATION")
        print("="*60)

        # Load data
        self.load_and_prepare_data()

        # Create splits
        self.create_time_series_splits()

        # Run validation for each fold
        for split in self.splits:
            print(f"\n📊 Processing Fold {split['fold']}")
            print("-" * 40)

            # Prepare train/validation data
            train_mask = (self.data['Date'] >= split['train_start']) & (self.data['Date'] <= split['train_end'])
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])

            train_data = self.data[train_mask].copy()
            val_data = self.data[val_mask].copy()

            print(f"Train: {len(train_data)} samples, {train_data['Date'].nunique()} dates")
            print(f"Val: {len(val_data)} samples, {val_data['Date'].nunique()} dates")

            if len(train_data) == 0 or len(val_data) == 0:
                print("⚠️ Skipping fold due to insufficient data")
                continue

            # Prepare features and targets
            X_train = train_data[self.feature_cols]
            y_train = train_data['Weighted_MixRel'].values  # Continuous target for regression

            X_val = val_data[self.feature_cols]
            y_val = val_data['Weighted_MixRel'].values

            # Handle missing values in features
            X_train = X_train.fillna(0)
            X_val = X_val.fillna(0)

            # Handle missing values in targets
            train_valid_mask = ~np.isnan(y_train)
            val_valid_mask = ~np.isnan(y_val)

            if train_valid_mask.sum() == 0 or val_valid_mask.sum() == 0:
                print("⚠️ Skipping fold due to insufficient valid targets")
                continue

            # Filter out NaN targets
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            val_data_clean = val_data[val_valid_mask]

            print(f"After NaN filtering - Train: {len(X_train)}, Val: {len(X_val)}")

            # Train model
            print(f"🧠 Training LightGBM Regression ({self.params['n_estimators']} ESTIMATORS)...")

            # Disable categorical features to avoid warnings and potential memory issues
            train_dataset = lgb.Dataset(
                X_train,
                label=y_train
            )
            val_dataset = lgb.Dataset(
                X_val,
                label=y_val,
                reference=train_dataset
            )

            # Train without early stopping to allow full feature usage
            model = lgb.train(
                self.params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )

            # Make predictions
            val_predictions = model.predict(X_val)

            # Calculate portfolio performance
            metrics = self.calculate_portfolio_metrics(
                val_predictions,
                val_data_clean['Future5DReturn'],
                val_data_clean['Date']
            )

            # Calculate regression metrics
            rmse = np.sqrt(mean_squared_error(y_val, val_predictions))
            mae = mean_absolute_error(y_val, val_predictions)

            # Store results
            result = {
                'fold': split['fold'],
                'train_start': split['train_start'],
                'train_end': split['train_end'],
                'val_start': split['val_start'],
                'val_end': split['val_end'],
                'train_samples': len(train_data),
                'val_samples': len(val_data),
                'sharpe': metrics['sharpe'],
                'cagr': metrics['cagr'],
                'max_drawdown': metrics['max_drawdown'],
                'total_return': metrics['total_return'],
                'rmse': rmse,
                'mae': mae,
                'model': model  # Store model for SHAP analysis
            }

            self.results.append(result)

            # Display results
            print(f"✓ Fold {split['fold']} Results:")
            print(f"  Sharpe Ratio: {metrics['sharpe']:.3f}")
            print(f"  CAGR: {metrics['cagr']*100:.1f}%")
            print(f"  Max Drawdown: {metrics['max_drawdown']*100:.1f}%")
            print(f"  Total Return: {metrics['total_return']*100:.1f}%")
            print(f"  RMSE: {rmse:.4f}")
            print(f"  MAE: {mae:.4f}")

        return self.results

    def run_comprehensive_shap_analysis(self):
        """Run SHAP analysis on the best performing model"""
        print("\n" + "="*60)
        print("🔍 RUNNING COMPREHENSIVE SHAP ANALYSIS")
        print("="*60)

        if not self.results:
            print("❌ No validation results available for SHAP analysis")
            return None

        # Find best model (highest Sharpe ratio)
        best_result = max(self.results, key=lambda x: x['sharpe'])
        best_model = best_result['model']

        print(f"Using model from Fold {best_result['fold']} (Sharpe: {best_result['sharpe']:.3f})")

        # Prepare sample data for SHAP analysis
        sample_size = min(1000, len(self.data))
        sample_data = self.data.sample(n=sample_size, random_state=42)
        X_sample = sample_data[self.feature_cols].fillna(0)

        print(f"Running SHAP analysis on {sample_size} samples...")

        # Create SHAP explainer
        explainer = shap.TreeExplainer(best_model)
        shap_values = explainer.shap_values(X_sample)

        # Calculate feature importance
        feature_importance = np.abs(shap_values).mean(axis=0)

        # Create results dataframe
        results_df = pd.DataFrame({
            'Feature_Clean': self.feature_cols,
            'Feature_Original': [self.name_mapping.get(col, col) for col in self.feature_cols],
            'SHAP_Importance': feature_importance
        }).sort_values('SHAP_Importance', ascending=False)

        results_df['Rank'] = range(1, len(results_df) + 1)

        # Display top 30 features
        print(f"\n🏆 TOP 30 MOST IMPORTANT FEATURES (REGRESSION)")
        print("="*80)
        print(f"{'Rank':<5} {'Feature Name':<50} {'SHAP Importance':<15}")
        print("-"*80)

        for _, row in results_df.head(30).iterrows():
            print(f"{row['Rank']:<5} {row['Feature_Original']:<50} {row['SHAP_Importance']:<15.6f}")

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        shap_filename = f'shap_feature_importance_regression_{timestamp}.csv'
        results_df.to_csv(shap_filename, index=False)

        print(f"\n✅ SHAP results saved to: {shap_filename}")
        print(f"📊 Total features analyzed: {len(results_df)}")
        print(f"🎯 Top feature: {results_df.iloc[0]['Feature_Original']}")
        print(f"📈 Importance range: {results_df['SHAP_Importance'].min():.6f} to {results_df['SHAP_Importance'].max():.6f}")

        return results_df

    def save_results(self):
        """Save validation results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'lightgbm_regression_validation_results_{timestamp}.txt'

        with open(filename, 'w') as f:
            f.write("="*80 + "\n")
            f.write("🚀 LIGHTGBM REGRESSION VALIDATION RESULTS\n")
            f.write("="*80 + "\n\n")

            f.write("CONFIGURATION:\n")
            f.write("- Objective: regression (continuous target)\n")
            f.write(f"- Estimators: {self.params['n_estimators']} (original high-performance hyperparameters)\n")
            f.write("- Time Period: 2005-12-01 to 2025-05-01\n")
            f.write("- Cross-Validation: 10-fold time series with 6-month gaps\n")
            f.write("- Portfolio: Top 20 long-only, equal-weighted, weekly rebalancing\n")
            f.write(f"- Features: {len(self.feature_cols)} total features ({self.params['feature_fraction']*100:.0f}% usage)\n")
            f.write(f"- Data: {len(self.data)} samples\n\n")

            f.write("HYPERPARAMETERS:\n")
            for key, value in self.params.items():
                f.write(f"- {key}: {value}\n")
            f.write("\n")

            f.write("="*80 + "\n")
            f.write("📊 INDIVIDUAL FOLD RESULTS\n")
            f.write("="*80 + "\n\n")

            for result in self.results:
                f.write(f"Fold {result['fold']}: Train {result['train_start'].date()} to {result['train_end'].date()}, ")
                f.write(f"Val {result['val_start'].date()} to {result['val_end'].date()}\n")
                f.write(f"  Sharpe Ratio: {result['sharpe']:.3f}\n")
                f.write(f"  CAGR: {result['cagr']*100:.1f}%\n")
                f.write(f"  Max Drawdown: {result['max_drawdown']*100:.1f}%\n")
                f.write(f"  Total Return: {result['total_return']*100:.1f}%\n")
                f.write(f"  RMSE: {result['rmse']:.4f}\n")
                f.write(f"  MAE: {result['mae']:.4f}\n\n")

            # Calculate summary statistics
            sharpes = [r['sharpe'] for r in self.results]
            cagrs = [r['cagr'] for r in self.results]
            drawdowns = [r['max_drawdown'] for r in self.results]

            f.write("="*80 + "\n")
            f.write("📈 SUMMARY STATISTICS\n")
            f.write("="*80 + "\n\n")

            f.write(f"Average Sharpe Ratio: {np.mean(sharpes):.3f} ± {np.std(sharpes):.3f}\n")
            f.write(f"Average CAGR: {np.mean(cagrs)*100:.1f}% ± {np.std(cagrs)*100:.1f}%\n")
            f.write(f"Average Max Drawdown: {np.mean(drawdowns)*100:.1f}% ± {np.std(drawdowns)*100:.1f}%\n\n")

            f.write(f"Best Performing Fold: Fold {max(self.results, key=lambda x: x['sharpe'])['fold']} ")
            f.write(f"(Sharpe {max(sharpes):.3f}, CAGR {max(cagrs)*100:.1f}%)\n")
            f.write(f"Worst Performing Fold: Fold {min(self.results, key=lambda x: x['sharpe'])['fold']} ")
            f.write(f"(Sharpe {min(sharpes):.3f}, CAGR {min(cagrs)*100:.1f}%)\n\n")

            f.write("Consistency Metrics:\n")
            f.write(f"- All folds positive returns: {'YES' if all(r['total_return'] > 0 for r in self.results) else 'NO'}\n")
            f.write(f"- Sharpe > 1.0 in all folds: {'YES' if all(r['sharpe'] > 1.0 for r in self.results) else 'NO'}\n")
            f.write(f"- CAGR > 25% in all folds: {'YES' if all(r['cagr'] > 0.25 for r in self.results) else 'NO'}\n")

        print(f"✅ Results saved to: {filename}")
        return filename

def main():
    """Main execution function"""
    validator = LightGBMRegressionValidator()

    # Run validation
    results = validator.run_validation()

    if results:
        # Save results
        validator.save_results()

        # Run SHAP analysis
        validator.run_comprehensive_shap_analysis()

        # Print summary
        sharpes = [r['sharpe'] for r in results]
        cagrs = [r['cagr'] for r in results]

        print("\n" + "="*60)
        print("🏆 REGRESSION VALIDATION SUMMARY")
        print("="*60)
        print(f"Average Sharpe Ratio: {np.mean(sharpes):.3f} ± {np.std(sharpes):.3f}")
        print(f"Average CAGR: {np.mean(cagrs)*100:.1f}% ± {np.std(cagrs)*100:.1f}%")
        print(f"Completed {len(results)} folds successfully")
    else:
        print("❌ No results generated")

if __name__ == "__main__":
    main()
