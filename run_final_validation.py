#!/usr/bin/env python3
"""
Run final validation using original data + top selected features
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import yaml
import re
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM compatibility"""
    cleaned = str(name)
    
    # Replace problematic characters
    replacements = {
        ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_', 
        '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_', 
        '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
        '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
        '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
        '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    # Remove any remaining special characters
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)
    cleaned = re.sub(r'_+', '_', cleaned)
    cleaned = cleaned.strip('_')
    
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    
    if not cleaned:
        cleaned = 'Feature_Unknown'
    
    return cleaned

def create_time_splits(dates: pd.Series, train_months: int = 12, validation_months: int = 3):
    """Create time-based train/validation splits"""
    splits = []
    unique_dates = sorted(dates.unique())
    min_date = min(unique_dates)
    max_date = max(unique_dates)
    
    current_date = min_date
    while current_date < max_date:
        train_start = current_date
        train_end = train_start + pd.DateOffset(months=train_months)
        val_start = train_end
        val_end = val_start + pd.DateOffset(months=validation_months)
        
        if val_end <= max_date:
            splits.append((train_start, train_end, val_start, val_end))
        
        current_date = val_start
    
    return splits

def get_top_features(min_improvement=0.1, max_features=50):
    """Get top performing features"""
    perf_df = pd.read_csv('lofep_results/feature_performance_history_20250711_165917.csv', index_col=0)
    
    improvements = []
    for idx, row in perf_df.iterrows():
        if row['sharpe_improvement'] > min_improvement:
            improvements.append((idx, row['sharpe_improvement']))
    
    improvements.sort(key=lambda x: x[1], reverse=True)
    top_features = [feat for feat, _ in improvements[:max_features]]
    
    print(f"Selected {len(top_features)} features with >{min_improvement:+.3f} Sharpe improvement:")
    for i, (feat, imp) in enumerate(improvements[:10]):
        print(f"  {i+1:2d}. {feat[:50]:<50} {imp:+.4f}")
    
    return top_features

def run_final_validation():
    """Run final validation with top features"""
    
    print("🚀 Running Final Validation with Top Features")
    print("="*60)
    
    # Load original data
    print("Loading original datasets...")
    # Use the comprehensive features file that contains everything
    print("Loading comprehensive features file...")
    merged_data = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    print(f"Loaded comprehensive data: {merged_data.shape}")
    
    # Data is already merged in the comprehensive file
    print(f"Data columns: {list(merged_data.columns[:10])}...")  # Show first 10 columns
    
    # Get top features
    top_features = get_top_features(min_improvement=0.1, max_features=30)
    
    # Check which top features exist in our data
    available_features = []
    missing_features = []
    
    for feature in top_features:
        if feature in merged_data.columns:
            available_features.append(feature)
        else:
            missing_features.append(feature)
    
    print(f"\nFeature availability:")
    print(f"Available: {len(available_features)}")
    print(f"Missing: {len(missing_features)}")
    
    if missing_features:
        print("Missing features (these were generated by LOFEP):")
        for feat in missing_features[:5]:
            print(f"  - {feat}")
        if len(missing_features) > 5:
            print(f"  ... and {len(missing_features)-5} more")
    
    # Use baseline features + available top features
    exclude_cols = ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']
    baseline_features = [col for col in merged_data.columns 
                        if col not in exclude_cols and 
                        merged_data[col].dtype in ['int64', 'float64']]
    
    print(f"\nBaseline features: {len(baseline_features)}")
    print(f"Additional top features available: {len(available_features)}")
    
    # Create final feature set
    final_features = list(set(baseline_features + available_features))
    print(f"Final feature set: {len(final_features)} features")
    
    # Clean feature names
    feature_mapping = {}
    for feature in final_features:
        cleaned_name = clean_feature_name(feature)
        feature_mapping[feature] = cleaned_name
    
    # Prepare data
    X = merged_data[final_features].copy()
    X.columns = [feature_mapping[col] for col in X.columns]
    X = X.fillna(0)
    X = X.replace([np.inf, -np.inf], 0)
    
    y = merged_data['Weighted_MixRel']
    dates = pd.to_datetime(merged_data['Date'])
    returns = merged_data['Future5DReturn']
    
    print(f"Final dataset: {X.shape}")
    
    # Load config for validation parameters
    with open('lofep_config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Run time series cross-validation
    print("\nRunning time series cross-validation...")
    
    splits = create_time_splits(dates, 
                               config['validation']['train_months'], 
                               config['validation']['validation_months'])
    
    print(f"Created {len(splits)} validation splits")
    
    fold_results = []
    lgb_params = config['lightgbm']['params'].copy()
    training_params = config['lightgbm']['training'].copy()
    
    for fold_idx, (train_start, train_end, val_start, val_end) in enumerate(splits):
        try:
            # Create masks
            train_mask = (dates >= train_start) & (dates < train_end)
            val_mask = (dates >= val_start) & (dates < val_end)
            
            if train_mask.sum() < 100 or val_mask.sum() < 20:
                continue
            
            # Prepare fold data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            
            # Remove NaN targets
            train_valid_mask = ~y_train.isna()
            val_valid_mask = ~y_val.isna()
            
            X_train = X_train[train_valid_mask]
            y_train = y_train[train_valid_mask]
            X_val = X_val[val_valid_mask]
            y_val = y_val[val_valid_mask]
            
            if len(X_train) < 50 or len(X_val) < 10:
                continue
            
            # Train model
            train_data = lgb.Dataset(X_train, label=y_train)
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=training_params['num_boost_round'],
                callbacks=[
                    lgb.early_stopping(training_params['early_stopping_rounds']),
                    lgb.log_evaluation(0)
                ]
            )
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate portfolio performance
            val_returns_data = returns[val_mask][val_valid_mask]
            val_dates_data = dates[val_mask][val_valid_mask]
            
            if len(val_returns_data) > 0:
                # Create portfolio
                combined = pd.DataFrame({
                    'pred': y_pred,
                    'returns': val_returns_data,
                    'dates': val_dates_data
                })
                
                # Weekly rebalancing - select top 15 stocks each week
                portfolio_returns = []
                for date in combined['dates'].unique():
                    date_data = combined[combined['dates'] == date]
                    if len(date_data) >= 15:
                        top_stocks = date_data.nlargest(15, 'pred')
                        period_return = top_stocks['returns'].mean()
                        portfolio_returns.append(period_return)
                
                if len(portfolio_returns) > 0:
                    portfolio_returns = np.array(portfolio_returns)
                    sharpe = np.mean(portfolio_returns) / np.std(portfolio_returns) * np.sqrt(52) if np.std(portfolio_returns) > 0 else 0
                    fold_results.append(sharpe)
                    
                    print(f"Fold {fold_idx:2d} ({val_start.strftime('%Y-%m')}-{val_end.strftime('%Y-%m')}): Sharpe = {sharpe:6.3f}")
        
        except Exception as e:
            print(f"Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Final results
    if fold_results:
        mean_sharpe = np.mean(fold_results)
        std_sharpe = np.std(fold_results)
        
        print(f"\n🎉 FINAL VALIDATION RESULTS:")
        print(f"="*50)
        print(f"Mean Sharpe Ratio: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Number of folds: {len(fold_results)}")
        print(f"Min Sharpe: {np.min(fold_results):.4f}")
        print(f"Max Sharpe: {np.max(fold_results):.4f}")
        print(f"Baseline Sharpe: 1.7765")
        print(f"Improvement: {mean_sharpe - 1.7765:+.4f}")
        print(f"% Improvement: {(mean_sharpe/1.7765 - 1)*100:+.1f}%")
        
        # Save results
        results = {
            'final_sharpe_mean': mean_sharpe,
            'final_sharpe_std': std_sharpe,
            'baseline_sharpe': 1.7765,
            'improvement': mean_sharpe - 1.7765,
            'num_folds': len(fold_results),
            'fold_results': fold_results,
            'features_used': len(final_features),
            'top_features_available': len(available_features),
            'timestamp': datetime.now().isoformat()
        }
        
        import json
        with open('lofep_results/final_validation_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to: lofep_results/final_validation_results.json")
        
    else:
        print("❌ No successful validation folds")

if __name__ == "__main__":
    run_final_validation()
