#!/usr/bin/env python3
"""
Regenerate the top 50 beneficial LOFEP features for final validation
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM"""
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', str(name))
    cleaned = re.sub(r'_+', '_', cleaned).strip('_')
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    return cleaned or 'Feature_Unknown'

def create_market_cap_quintiles(df):
    """Create Market_Cap_Quintile grouping"""
    print("Creating Market_Cap_Quintile grouping...")

    # Initialize as float column
    df['Market_Cap_Quintile'] = np.nan

    # Check if MarketCap column exists
    if 'MarketCap' not in df.columns:
        print("  ⚠️  MarketCap column not found, skipping quintile creation")
        return df

    for date in df['Date'].unique():
        date_mask = df['Date'] == date
        date_data = df[date_mask]

        if len(date_data) >= 5:
            try:
                # Create quintiles (1=smallest, 5=largest)
                quintiles = pd.qcut(date_data['MarketCap'], q=5, labels=False, duplicates='drop') + 1
                df.loc[date_mask, 'Market_Cap_Quintile'] = quintiles.astype(float)
            except Exception as e:
                print(f"  Warning: Could not create quintiles for date {date}: {e}")
                continue

    print(f"  ✓ Created quintiles for {df['Market_Cap_Quintile'].notna().sum()} rows")
    return df

def generate_rolling_features(df, base_col, windows=[6, 12]):
    """Generate rolling mean and momentum features"""
    new_features = {}
    
    if base_col not in df.columns:
        return new_features
    
    # Sort by date for proper rolling calculations
    df_sorted = df.sort_values(['Date']).copy()
    
    for window in windows:
        # Rolling mean
        rolling_mean_col = f"{base_col}_rolling_mean_{window}M"
        df_sorted[rolling_mean_col] = df_sorted.groupby('P123 ID')[base_col].rolling(
            window=window, min_periods=1
        ).mean().reset_index(0, drop=True)
        new_features[rolling_mean_col] = df_sorted[rolling_mean_col]
        
        # Momentum (current vs rolling mean)
        momentum_col = f"{base_col}_momentum_{window}M"
        df_sorted[momentum_col] = df_sorted[base_col] / (df_sorted[rolling_mean_col] + 1e-8) - 1
        new_features[momentum_col] = df_sorted[momentum_col]
    
    return new_features

def generate_cross_sectional_features(df, base_col, grouping_col='Market_Cap_Quintile'):
    """Generate cross-sectional features within groups"""
    new_features = {}
    
    if base_col not in df.columns or grouping_col not in df.columns:
        return new_features
    
    # Group by date and quintile, then calculate transformations
    for date in df['Date'].unique():
        date_mask = df['Date'] == date
        date_data = df[date_mask].copy()
        
        if len(date_data) < 5:
            continue
        
        # Rank within quintile
        rank_col = f"{base_col}_{grouping_col}_rank"
        if rank_col not in new_features:
            new_features[rank_col] = pd.Series(index=df.index, dtype=float)
        
        # Z-score within quintile
        zscore_col = f"{base_col}_{grouping_col}_zscore"
        if zscore_col not in new_features:
            new_features[zscore_col] = pd.Series(index=df.index, dtype=float)
        
        for quintile in date_data[grouping_col].dropna().unique():
            quintile_mask = (date_data[grouping_col] == quintile)
            quintile_data = date_data[quintile_mask]
            
            if len(quintile_data) >= 2:
                # Rank within quintile
                ranks = quintile_data[base_col].rank(pct=True)
                new_features[rank_col].loc[date_data.index[quintile_mask]] = ranks
                
                # Z-score within quintile
                mean_val = quintile_data[base_col].mean()
                std_val = quintile_data[base_col].std()
                if std_val > 0:
                    zscores = (quintile_data[base_col] - mean_val) / std_val
                    new_features[zscore_col].loc[date_data.index[quintile_mask]] = zscores
    
    return new_features

def generate_vs_historical_median_features(df, base_col):
    """Generate vs_historical_median features"""
    new_features = {}
    
    if base_col not in df.columns:
        return new_features
    
    # Sort by date
    df_sorted = df.sort_values(['Date']).copy()
    
    # Calculate expanding historical median for each stock
    vs_median_col = f"{base_col}_vs_historical_median"
    df_sorted[vs_median_col] = df_sorted.groupby('P123 ID')[base_col].apply(
        lambda x: x / (x.expanding().median() + 1e-8) - 1
    ).reset_index(0, drop=True)
    
    new_features[vs_median_col] = df_sorted[vs_median_col]
    
    return new_features

def regenerate_top_features():
    """Regenerate the top 50 beneficial features"""
    
    print("🔧 Regenerating Top 50 LOFEP Features")
    print("="*60)
    
    # Load the merged dataset
    print("Loading merged dataset...")
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    # Merge data
    merged_data = features_df.merge(target_df, on=['Date', 'P123 ID'], how='inner')
    merged_data = merged_data.merge(returns_df, on=['Date', 'P123 ID'], how='inner')
    
    print(f"Merged data: {merged_data.shape}")
    
    # Create Market_Cap_Quintile grouping
    merged_data = create_market_cap_quintiles(merged_data)
    
    # Define the top 50 features to regenerate
    top_features = [
        'Nordic_Exchanges_rolling_mean_6M',
        'close0_GetSeriesIWM_USA_momentum_6M', 
        'Sales_Trend_CTR_2Y_Growth_rolling_mean_6M',
        'MACD_Histogram_Normalized_rolling_mean_6M',
        'Month_of_Yr_vs_historical_median',
        'F_10d_Return_Rank_vs_Sub_Industry_rolling_mean_12M',
        'Pct_Days_Above_50dMA_3M_vs_historical_median',
        'Wyckoff_Accum_Phase_Detector_Market_Cap_Quintile_rank',
        'ATRN_6M_Vs_1M_rolling_mean_12M',
        'Healthcare_Sector_momentum_6M',
        'Price_Trend_Entropy_Proxy2_rolling_mean_6M',
        'Abs_Return_6M_rolling_mean_6M',
        'Cash_Fr_Fin_To_EV_rolling_mean_6M',
        'Downside_Magnitude_rolling_mean_6M',
        'Field_Divergence_Indicator_rolling_mean_12M',
        'Vol_Adjusted_6M_Return_rolling_mean_12M',
        'WeeksToQ_Under_5_momentum_12M',
        'Interest_Exp_QTR_vs_historical_median',
        'EV_vs_historical_median',
        'Annual_Range_per_Unit_of_Vol_rolling_mean_12M',
        'Healthcare_Sector_rolling_mean_6M',
        'Abs_Return_1Y_zscore',
        'EPS_Negative_Revision_Weeks_in_Year_Market_Cap_Quintile_zscore',
        'Volatility_of_Monthly_Returns_1Y_rolling_mean_12M',
        'Energy_Sector_vs_market_median'
    ]
    
    print(f"Regenerating {len(top_features)} top features...")
    
    # Track successfully generated features
    generated_features = {}
    
    # Process each feature type
    for feature_name in top_features:
        try:
            print(f"Processing: {feature_name}")
            
            # Parse feature name to understand transformation
            if '_rolling_mean_' in feature_name:
                # Extract base column and window
                parts = feature_name.split('_rolling_mean_')
                base_col = parts[0]
                window_str = parts[1].replace('M', '')
                window = int(window_str)
                
                if base_col in merged_data.columns:
                    new_features = generate_rolling_features(merged_data, base_col, [window])
                    generated_features.update(new_features)
                    print(f"  ✓ Generated rolling mean feature")
                else:
                    print(f"  ❌ Base column {base_col} not found")
            
            elif '_momentum_' in feature_name:
                # Extract base column and window
                parts = feature_name.split('_momentum_')
                base_col = parts[0]
                window_str = parts[1].replace('M', '')
                window = int(window_str)
                
                if base_col in merged_data.columns:
                    new_features = generate_rolling_features(merged_data, base_col, [window])
                    generated_features.update(new_features)
                    print(f"  ✓ Generated momentum feature")
                else:
                    print(f"  ❌ Base column {base_col} not found")
            
            elif '_Market_Cap_Quintile_' in feature_name:
                # Extract base column and transformation
                parts = feature_name.split('_Market_Cap_Quintile_')
                base_col = parts[0]
                transformation = parts[1]
                
                if base_col in merged_data.columns:
                    new_features = generate_cross_sectional_features(merged_data, base_col)
                    generated_features.update(new_features)
                    print(f"  ✓ Generated cross-sectional feature")
                else:
                    print(f"  ❌ Base column {base_col} not found")
            
            elif '_vs_historical_median' in feature_name:
                # Extract base column
                base_col = feature_name.replace('_vs_historical_median', '')
                
                if base_col in merged_data.columns:
                    new_features = generate_vs_historical_median_features(merged_data, base_col)
                    generated_features.update(new_features)
                    print(f"  ✓ Generated vs historical median feature")
                else:
                    print(f"  ❌ Base column {base_col} not found")
            
            elif '_zscore' in feature_name:
                # Simple z-score transformation
                base_col = feature_name.replace('_zscore', '')
                
                if base_col in merged_data.columns:
                    zscore_col = f"{base_col}_zscore"
                    merged_data[zscore_col] = (merged_data[base_col] - merged_data[base_col].mean()) / merged_data[base_col].std()
                    generated_features[zscore_col] = merged_data[zscore_col]
                    print(f"  ✓ Generated z-score feature")
                else:
                    print(f"  ❌ Base column {base_col} not found")
            
            else:
                print(f"  ⚠️  Unknown feature type: {feature_name}")
        
        except Exception as e:
            print(f"  ❌ Error generating {feature_name}: {str(e)}")
            continue
    
    print(f"\n📊 Feature Generation Summary:")
    print(f"Attempted: {len(top_features)}")
    print(f"Successfully generated: {len(generated_features)}")
    
    if generated_features:
        # Add generated features to the dataset
        for feat_name, feat_data in generated_features.items():
            merged_data[feat_name] = feat_data
        
        print(f"Final dataset shape: {merged_data.shape}")
        
        # Save enhanced dataset with top features
        output_file = 'enhanced_dataset_top50_features.csv'
        print(f"Saving enhanced dataset to {output_file}...")
        merged_data.to_csv(output_file, index=False)
        
        print(f"✅ Enhanced dataset saved with {len(generated_features)} new features!")
        
        return merged_data, list(generated_features.keys())
    else:
        print("❌ No features were successfully generated")
        return merged_data, []

if __name__ == "__main__":
    enhanced_data, new_features = regenerate_top_features()
    
    if new_features:
        print(f"\n🎉 Ready for final validation with {len(new_features)} regenerated features!")
    else:
        print("\n❌ Feature regeneration failed")
