#!/usr/bin/env python3
"""
Debug ranking issues
"""

import pandas as pd
import numpy as np

def debug_ranking():
    print("Loading data to debug ranking...")
    
    # Load and merge data (simplified version)
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    target_df = pd.read_csv('PureEURTarget.csv')
    
    # Clean feature names
    import re
    name_mapping = {}
    for col in features_df.columns:
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
        clean_name = re.sub(r'_+', '_', clean_name)
        clean_name = clean_name.strip('_')
        name_mapping[col] = clean_name
    features_df = features_df.rename(columns=name_mapping)
    
    # Merge
    data = features_df.merge(
        target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
        left_on=['Date', 'P123_ID'], 
        right_on=['Date', 'P123 ID'],
        how='inner'
    )
    
    # Filter to small date range for debugging
    data['Date'] = pd.to_datetime(data['Date'])
    start_date = pd.to_datetime('2005-12-01')
    end_date = pd.to_datetime('2005-12-31')  # Just one month
    
    data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
    print(f"Debug data shape: {data.shape}")
    print(f"Unique dates: {data['Date'].nunique()}")
    print(f"Date range: {data['Date'].min()} to {data['Date'].max()}")
    
    # Check target distribution
    print(f"\nTarget stats:")
    print(f"  Min: {data['Weighted_MixRel'].min()}")
    print(f"  Max: {data['Weighted_MixRel'].max()}")
    print(f"  NaN count: {data['Weighted_MixRel'].isna().sum()}")
    
    # Check stocks per date
    stocks_per_date = data.groupby('Date').size()
    print(f"\nStocks per date:")
    print(f"  Min: {stocks_per_date.min()}")
    print(f"  Max: {stocks_per_date.max()}")
    print(f"  Mean: {stocks_per_date.mean():.1f}")
    
    # Test ranking function
    def convert_to_ranks(targets, dates):
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['rank'] = 0
        
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']
            
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                valid_targets = date_targets[valid_mask]
                ranks = valid_targets.rank(method='dense', ascending=True) - 1
                df.loc[date_mask & valid_mask, 'rank'] = ranks.astype(int)
        
        return df['rank'].values
    
    # Test on first few dates
    first_dates = sorted(data['Date'].unique())[:3]
    test_data = data[data['Date'].isin(first_dates)]
    
    print(f"\nTesting ranking on {len(test_data)} samples from {len(first_dates)} dates...")
    
    ranks = convert_to_ranks(test_data['Weighted_MixRel'], test_data['Date'])
    
    print(f"Rank stats:")
    print(f"  Min rank: {ranks.min()}")
    print(f"  Max rank: {ranks.max()}")
    print(f"  Unique ranks: {len(np.unique(ranks))}")
    
    # Check ranks by date
    for date in first_dates:
        date_mask = test_data['Date'] == date
        date_ranks = ranks[date_mask]
        print(f"  {date.date()}: {len(date_ranks)} stocks, ranks {date_ranks.min()}-{date_ranks.max()}")
    
    # Test groups
    def prepare_ranking_data(dates):
        unique_dates = sorted(dates.unique())
        groups = []
        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)
        return groups
    
    groups = prepare_ranking_data(test_data['Date'])
    print(f"\nGroups: {groups}")
    print(f"Total samples: {sum(groups)}")
    print(f"Expected samples: {len(test_data)}")

if __name__ == "__main__":
    debug_ranking()
