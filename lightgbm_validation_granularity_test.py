#!/usr/bin/env python3
"""
LightGBM Granularity Test - Compare 5 vs 100 Relevance Labels
Tests whether more granular relevance labels improve ranking performance
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import shap
from datetime import datetime, timedelta
import warnings
import json
from sklearn.metrics import mean_squared_error, mean_absolute_error
warnings.filterwarnings('ignore')

class LightGBMGranularityTester:
    def __init__(self, n_relevance_levels=5):
        # Use comprehensive hyperparameters for fair comparison
        self.params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            
            # Comprehensive hyperparameters (same as ranking validation)
            'n_estimators': 1500,
            'max_depth': 8,
            'learning_rate': 0.03,
            'num_leaves': 100,
            'subsample': 0.8,
            'min_child_samples': 50,
            'feature_fraction': 0.8,
            'lambda_l1': 0.001,
            'lambda_l2': 0.001,
            'min_split_gain': 0.001,
            'bagging_freq': 3,
            'verbose': -1,
            'random_state': 42,
            'n_jobs': -1,
            
            # Enhanced LambdaRank parameters
            'ndcg_eval_at': [10, 20, 50],
            'lambdarank_truncation_level': 100,
            'lambdarank_norm': True
        }
        
        # Test parameter: number of relevance levels
        self.n_relevance_levels = n_relevance_levels
        
        # Enhanced categorical feature handling
        self.categorical_features = [
            'Industry_Code', 'SubIndustry_Code', 'SubSector_Code',
            'Death_Cross_Event_Binary', 'Golden_Cross_Event_Binary',
            'Nordic_Exchanges', 'Banks_SubSector', 'Cyclicals_Sector',
            'Energy_Sector', 'Financial_Sector', 'Healthcare_Sector',
            'Industrial_Sector', 'Materials_Sector', 'Tech_Sector'
        ]
        
        self.categorical_indices = []
        self.data = None
        self.feature_cols = None
        self.splits = []
        self.results = []
        self.name_mapping = {}

    def clean_feature_names(self, df):
        """Clean feature names for LightGBM compatibility"""
        name_mapping = {}
        df_clean = df.copy()
        
        for col in df.columns:
            clean_name = col.replace(' ', '_').replace('(', '').replace(')', '').replace('%', 'Pct')
            clean_name = clean_name.replace('/', '_').replace('-', '_').replace('+', 'Plus')
            clean_name = clean_name.replace('&', 'And').replace('__', '_')
            # Remove special characters that LightGBM doesn't support
            clean_name = clean_name.replace('"', '').replace("'", '').replace('\\', '')
            clean_name = clean_name.replace('[', '').replace(']', '').replace('{', '').replace('}', '')
            clean_name = clean_name.replace(':', '').replace(',', '').replace('.', '_')
            clean_name = clean_name.replace('*', 'Star').replace('?', 'Q').replace('!', 'Excl')
            # Ensure it starts with letter or underscore
            if clean_name and clean_name[0].isdigit():
                clean_name = 'F_' + clean_name
            if clean_name != col:
                name_mapping[clean_name] = col
                df_clean = df_clean.rename(columns={col: clean_name})
        
        return df_clean, name_mapping

    def process_categorical_features(self, X, feature_cols):
        """Process categorical features"""
        print(f"🏷️ Processing categorical features...")
        
        for i, col in enumerate(feature_cols):
            if any(cat in col for cat in self.categorical_features):
                self.categorical_indices.append(i)
                print(f"  ✓ Processed categorical feature: {col} (index {i})")
        
        print(f"✓ Found {len(self.categorical_indices)} categorical features")
        return X

    def remove_infinite_values_enhanced(self, X, y_relevance, y_continuous, dates, tickers, returns, query_ids):
        """Remove rows with infinite values - enhanced version for dual targets"""
        print("🧹 Checking for infinite values...")
        
        # Check for infinite values in features
        inf_mask = np.isinf(X).any(axis=1)
        
        if inf_mask.sum() > 0:
            print(f"Removing {inf_mask.sum()} rows with infinite values")
            X = X[~inf_mask]
            y_relevance = y_relevance[~inf_mask]
            y_continuous = y_continuous[~inf_mask]
            dates = dates[~inf_mask]
            tickers = tickers[~inf_mask]
            returns = returns[~inf_mask]
            query_ids = query_ids[~inf_mask]
        else:
            print("✓ No infinite values found")
        
        return X, y_relevance, y_continuous, dates, tickers, returns, query_ids

    def load_and_prepare_data(self):
        """Load and merge all data files - identical to ranking validation"""
        print("📊 Loading data...")

        # Load datasets
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        print(f"✓ Features loaded: {features_df.shape}")

        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        print(f"✓ Future returns loaded: {returns_df.shape}")

        target_df = pd.read_csv('PureEURTarget.csv')
        print(f"✓ Targets loaded: {target_df.shape}")

        # Clean feature names
        print("🧹 Cleaning feature names...")
        features_df, self.name_mapping = self.clean_feature_names(features_df)
        target_df, _ = self.clean_feature_names(target_df)
        returns_df, _ = self.clean_feature_names(returns_df)

        # Merge datasets
        print("🔗 Merging datasets...")
        data = features_df.merge(target_df, on=['Date', 'P123_ID'], how='inner')
        data = data.merge(returns_df, on=['Date', 'P123_ID'], how='inner')
        print(f"✓ Merged data: {data.shape}")

        # Filter date range
        data['Date'] = pd.to_datetime(data['Date'])
        start_date = pd.to_datetime('2005-12-01')
        end_date = pd.to_datetime('2025-05-01')
        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        print(f"✓ Filtered to {start_date.date()} - {end_date.date()}: {data.shape}")

        # Sort by date and stock
        data = data.sort_values(['Date', 'P123_ID']).reset_index(drop=True)

        # Create daily query groups for ranking
        print("🎯 Creating daily query groups...")
        data['QueryID'] = data['Date'].rank(method='dense').astype(int)
        
        query_stats = data.groupby('QueryID').agg({
            'Date': 'first',
            'P123_ID': 'count',
            'Weighted_MixRel': ['mean', 'std']
        }).round(4)
        query_stats.columns = ['Date', 'StockCount', 'Target_Mean', 'Target_Std']
        print(f"✓ Created {len(query_stats)} query groups (trading days)")
        print(f"✓ Average stocks per day: {query_stats['StockCount'].mean():.0f}")

        # Create relevance labels with specified granularity
        print(f"🏷️ Creating {self.n_relevance_levels}-level relevance labels...")
        def create_relevance_labels(group_data, n_levels=self.n_relevance_levels):
            """Convert continuous targets to discrete relevance levels within each day"""
            if len(group_data) == 1:
                return pd.Series([n_levels//2], index=group_data.index)

            # Handle NaN values first
            target_values = group_data['Weighted_MixRel']
            valid_mask = ~target_values.isna()

            if valid_mask.sum() == 0:
                # All NaN values, assign middle relevance
                return pd.Series([n_levels//2] * len(group_data), index=group_data.index)

            if valid_mask.sum() == 1:
                # Only one valid value, assign highest relevance to it
                result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                result.loc[valid_mask] = n_levels - 1
                return result

            try:
                # Use quantile-based binning for balanced distribution on valid values only
                valid_targets = target_values[valid_mask]
                labels = pd.qcut(
                    valid_targets,
                    q=n_levels,
                    labels=range(n_levels),
                    duplicates='drop'
                )

                # Create result series with middle value for NaN entries
                result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                result.loc[valid_mask] = labels.astype(int)
                return result

            except:
                # Fall back to rank-based approach for edge cases
                valid_targets = target_values[valid_mask]
                ranks = valid_targets.rank(method='dense', ascending=False)
                max_rank = ranks.max()

                if max_rank == 1:
                    # All valid values are the same
                    result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                    result.loc[valid_mask] = n_levels - 1
                    return result

                # Scale ranks to 0-(n_levels-1) range
                relevance = ((ranks - 1) / (max_rank - 1) * (n_levels - 1))
                relevance = (n_levels - 1 - relevance).round()

                # Create result series with middle value for NaN entries
                result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                result.loc[valid_mask] = relevance.astype(int)
                return result

        # Apply relevance labeling by date (optimized for large n_levels)
        print("Applying relevance labels by date...")
        if self.n_relevance_levels <= 20:
            # Use groupby.apply for smaller number of levels
            data['Relevance'] = data.groupby('Date').apply(
                lambda x: create_relevance_labels(x)
            ).droplevel(0)
        else:
            # Use optimized approach for large number of levels
            print(f"Using optimized approach for {self.n_relevance_levels} levels...")
            relevance_list = []
            unique_dates = sorted(data['Date'].unique())

            for i, date in enumerate(unique_dates):
                if i % 100 == 0:
                    print(f"  Processing date {i+1}/{len(unique_dates)}: {date.date()}")

                date_mask = data['Date'] == date
                date_data = data[date_mask]
                date_relevance = create_relevance_labels(date_data)
                relevance_list.extend(date_relevance.values)

            data['Relevance'] = relevance_list
        
        # Check for any remaining NaN values in relevance labels
        nan_relevance = data['Relevance'].isna().sum()
        if nan_relevance > 0:
            print(f"Warning: {nan_relevance} NaN relevance labels found, filling with middle value")
            data['Relevance'] = data['Relevance'].fillna(self.n_relevance_levels//2)

        # Verify relevance distribution
        relevance_dist = data['Relevance'].value_counts().sort_index()
        print(f"✓ Relevance label distribution:")
        for label, count in relevance_dist.items():
            pct = count / len(data) * 100
            print(f"    Label {label}: {count:,} ({pct:.1f}%)")

        # Identify feature columns (exclude metadata and new columns)
        exclude_cols = ['Date', 'P123_ID', 'Ticker', 'Ticker_x', 'Ticker_y', 'Weighted_MixRel', 'Future5DReturn', 'QueryID', 'Relevance']
        feature_cols = [col for col in data.columns if col not in exclude_cols and data[col].dtype in ['int64', 'float64', 'bool']]

        print(f"✓ Feature columns: {len(feature_cols)}")

        # Extract arrays for preprocessing
        X = data[feature_cols].copy()
        y_relevance = data['Relevance'].values  # Discrete labels for training
        y_continuous = data['Weighted_MixRel'].values  # Continuous for evaluation
        dates = data['Date'].values
        tickers = data['Ticker'].values if 'Ticker' in data.columns else data['P123_ID'].values
        returns = data['Future5DReturn'].values
        query_ids = data['QueryID'].values

        # Process categorical features
        X = self.process_categorical_features(X, feature_cols)

        # Remove infinite values (enhanced version)
        X_clean, y_rel_clean, y_cont_clean, dates_clean, tickers_clean, returns_clean, query_clean = self.remove_infinite_values_enhanced(
            X.values, y_relevance, y_continuous, dates, tickers, returns, query_ids
        )

        # Reconstruct cleaned data
        data_clean = pd.DataFrame(X_clean, columns=feature_cols)
        data_clean['Date'] = dates_clean
        data_clean['P123_ID'] = tickers_clean
        data_clean['Relevance'] = y_rel_clean
        data_clean['Weighted_MixRel'] = y_cont_clean
        data_clean['Future5DReturn'] = returns_clean
        data_clean['QueryID'] = query_clean

        print(f"✓ Final cleaned data: {data_clean.shape}")

        self.data = data_clean
        self.feature_cols = feature_cols

        return data_clean

    def create_time_series_splits(self):
        """Create time series splits - identical to ranking validation"""
        print("📅 Creating 10 time series splits with 6-month gaps...")

        dates = pd.to_datetime(self.data['Date'])
        date_range = dates.max() - dates.min()
        total_days = date_range.days

        print(f"Date range: {dates.min().date()} to {dates.max().date()}")
        print(f"Total unique dates: {dates.nunique()}")

        splits = []

        for i in range(10):
            # Calculate training period (expanding window)
            train_start = dates.min()
            train_end = dates.min() + timedelta(days=int(total_days * (0.2 + i * 0.08)))

            # 6-month gap
            gap_start = train_end
            gap_end = train_end + timedelta(days=180)

            # Validation period (18 months)
            val_start = gap_end
            val_end = gap_end + timedelta(days=540)

            if val_end <= dates.max():
                splits.append({
                    'fold': i + 1,
                    'train_start': train_start,
                    'train_end': train_end,
                    'val_start': val_start,
                    'val_end': val_end
                })

        print(f"✓ Created {len(splits)} valid splits")
        for split in splits:
            print(f"  Fold {split['fold']}: Train {split['train_start'].date()} to {split['train_end'].date()}, Val {split['val_start'].date()} to {split['val_end'].date()}")

        self.splits = splits
        return splits

    def prepare_ranking_data_from_queries(self, query_ids):
        """Prepare data for LightGBM ranking using QueryID groups"""
        # Count samples per query group
        unique_queries = sorted(np.unique(query_ids))

        groups = []
        for query_id in unique_queries:
            query_mask = query_ids == query_id
            group_size = query_mask.sum()
            if group_size > 0:
                groups.append(group_size)

        return groups

    def calculate_portfolio_metrics_dual_target(self, predictions, actual_returns, processed_target, dates, query_ids, top_k=20):
        """Calculate portfolio performance using dual target approach - identical to ranking validation"""
        portfolio_returns = []
        ndcg_10_scores = []
        ndcg_20_scores = []

        # Group by query (trading day) and select top k stocks
        unique_queries = sorted(query_ids.unique())

        for query_id in unique_queries:
            query_mask = query_ids == query_id
            query_indices = np.where(query_mask)[0]  # Get actual indices

            query_predictions = predictions[query_mask]
            query_actual_returns = actual_returns.iloc[query_indices]
            query_processed_target = processed_target.iloc[query_indices]

            if len(query_predictions) >= top_k:
                # Select top k stocks by ranking prediction
                top_indices = np.argsort(query_predictions)[-top_k:]

                # Calculate portfolio return using ACTUAL future returns
                top_actual_returns = query_actual_returns.iloc[top_indices]
                portfolio_return = top_actual_returns.mean() / 100.0  # Convert percentage to decimal
                portfolio_returns.append(portfolio_return)

                # Calculate NDCG metrics using processed target for ranking quality
                ndcg_10 = self.calculate_ndcg_k(query_processed_target.values, query_predictions, 10)
                ndcg_20 = self.calculate_ndcg_k(query_processed_target.values, query_predictions, 20)
                ndcg_10_scores.append(ndcg_10)
                ndcg_20_scores.append(ndcg_20)

        if len(portfolio_returns) == 0:
            return {'sharpe': 0, 'cagr': 0, 'max_drawdown': 0, 'total_return': 0, 'ndcg_10': 0, 'ndcg_20': 0}

        portfolio_returns = np.array(portfolio_returns)

        # Calculate performance metrics
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()

        # Annualize (assuming weekly rebalancing)
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0

        # CAGR
        cumulative_return = np.prod(1 + portfolio_returns) - 1
        n_years = len(portfolio_returns) / 52
        cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 else 0

        # Max drawdown
        cumulative_wealth = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_wealth)
        drawdowns = (cumulative_wealth - running_max) / running_max
        max_drawdown = np.min(drawdowns)

        return {
            'sharpe': sharpe,
            'cagr': cagr,
            'max_drawdown': max_drawdown,
            'total_return': cumulative_return,
            'n_periods': len(portfolio_returns),
            'ndcg_10': np.mean(ndcg_10_scores) if ndcg_10_scores else 0,
            'ndcg_20': np.mean(ndcg_20_scores) if ndcg_20_scores else 0,
            'volatility': annual_volatility
        }

    def calculate_ndcg_k(self, y_true, y_pred, k):
        """Calculate NDCG@k metric"""
        try:
            # Sort by predictions (descending)
            sorted_indices = np.argsort(y_pred)[::-1][:k]

            # Get true relevance scores for top k predictions
            true_relevance = y_true[sorted_indices]

            # Calculate DCG
            dcg = np.sum((2**true_relevance - 1) / np.log2(np.arange(2, len(true_relevance) + 2)))

            # Calculate IDCG (ideal DCG)
            ideal_relevance = np.sort(y_true)[::-1][:k]
            idcg = np.sum((2**ideal_relevance - 1) / np.log2(np.arange(2, len(ideal_relevance) + 2)))

            # Calculate NDCG
            ndcg = dcg / idcg if idcg > 0 else 0
            return ndcg
        except:
            return 0

    def run_validation(self):
        """Run the complete validation process - identical to ranking validation"""
        print("="*80)
        print(f"🚀 STARTING LIGHTGBM GRANULARITY TEST ({self.n_relevance_levels} LEVELS)")
        print("="*80)

        # Load data
        self.load_and_prepare_data()

        # Create splits
        self.create_time_series_splits()

        # Run validation for each fold
        for split in self.splits:
            print(f"\n📊 Processing Fold {split['fold']}")
            print("-" * 40)

            # Prepare train/validation data
            train_mask = (self.data['Date'] >= split['train_start']) & (self.data['Date'] <= split['train_end'])
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])

            train_data = self.data[train_mask].copy()
            val_data = self.data[val_mask].copy()

            print(f"Train: {len(train_data)} samples, {train_data['Date'].nunique()} dates")
            print(f"Val: {len(val_data)} samples, {val_data['Date'].nunique()} dates")

            if len(train_data) == 0 or len(val_data) == 0:
                print("⚠️ Skipping fold due to insufficient data")
                continue

            # Prepare features and targets
            X_train = train_data[self.feature_cols]
            X_val = val_data[self.feature_cols]

            # Create relevance labels within each fold to ensure full range
            print(f"🏷️ Creating {self.n_relevance_levels} relevance labels for this fold...")

            def create_fold_relevance_labels_simple(data, n_levels):
                """Create relevance labels within fold data - simplified approach"""
                relevance_list = []
                unique_dates = sorted(data['Date'].unique())

                for date in unique_dates:
                    date_mask = data['Date'] == date
                    date_data = data[date_mask]
                    target_values = date_data['Weighted_MixRel'].values

                    # Handle NaN values
                    valid_mask = ~np.isnan(target_values)

                    if valid_mask.sum() <= 1:
                        # Not enough valid values for ranking
                        result = np.full(len(date_data), n_levels//2)
                        if valid_mask.sum() == 1:
                            result[valid_mask] = n_levels - 1
                        relevance_list.extend(result.tolist())
                        continue

                    # Use percentile-based ranking for robust label assignment
                    valid_targets = target_values[valid_mask]

                    # Create percentile-based labels
                    labels = np.zeros(len(date_data), dtype=int)
                    labels.fill(n_levels//2)  # Default for NaN values

                    for i, target in enumerate(valid_targets):
                        # Find which percentile bin this target falls into
                        percentile_rank = (valid_targets < target).sum() / len(valid_targets) * 100
                        label = min(int(percentile_rank / (100 / n_levels)), n_levels - 1)
                        valid_idx = np.where(valid_mask)[0][i]
                        labels[valid_idx] = label

                    relevance_list.extend(labels.tolist())

                return np.array(relevance_list)

            y_train = create_fold_relevance_labels_simple(train_data, self.n_relevance_levels)
            y_val = create_fold_relevance_labels_simple(val_data, self.n_relevance_levels)

            train_groups = self.prepare_ranking_data_from_queries(train_data['QueryID'])
            val_groups = self.prepare_ranking_data_from_queries(val_data['QueryID'])

            # DEBUG: Verify we're using discrete relevance labels
            print(f"🔍 DEBUG - Training target stats:")
            print(f"    y_train unique values: {np.unique(y_train)}")
            print(f"    y_train distribution: {np.bincount(y_train.astype(int))}")
            print(f"    Categorical indices: {len(self.categorical_indices)} features")
            print(f"    Train groups: {len(train_groups)} groups, total samples: {sum(train_groups)}")

            # Handle missing values
            X_train = X_train.fillna(0)
            X_val = X_val.fillna(0)

            # Train model
            print(f"🧠 Training LightGBM ({self.params['n_estimators']} ESTIMATORS, {self.n_relevance_levels} RELEVANCE LEVELS)...")

            # Disable categorical features for 100-level test to avoid potential issues
            train_dataset = lgb.Dataset(
                X_train,
                label=y_train,
                group=train_groups
            )
            val_dataset = lgb.Dataset(
                X_val,
                label=y_val,
                group=val_groups,
                reference=train_dataset
            )

            # Train without early stopping to allow full feature usage
            model = lgb.train(
                self.params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )

            # Make predictions
            val_predictions = model.predict(X_val)

            # Calculate portfolio performance using actual future returns
            # This implements the dual approach: ranking predictions + continuous returns
            metrics = self.calculate_portfolio_metrics_dual_target(
                val_predictions,           # Ranking predictions from discrete labels
                val_data['Future5DReturn'], # Actual continuous future returns
                val_data['Weighted_MixRel'], # Processed target for comparison
                val_data['Date'],
                val_data['QueryID']
            )

            # Store results
            result = {
                'fold': split['fold'],
                'train_start': split['train_start'],
                'train_end': split['train_end'],
                'val_start': split['val_start'],
                'val_end': split['val_end'],
                'train_samples': len(train_data),
                'val_samples': len(val_data),
                'n_relevance_levels': self.n_relevance_levels,
                'sharpe': metrics['sharpe'],
                'cagr': metrics['cagr'],
                'max_drawdown': metrics['max_drawdown'],
                'total_return': metrics['total_return'],
                'ndcg_10': metrics.get('ndcg_10', 0),
                'ndcg_20': metrics.get('ndcg_20', 0),
                'model': model  # Store model for potential SHAP analysis
            }

            self.results.append(result)

            # Display results
            print(f"✓ Fold {split['fold']} Results:")
            print(f"  Sharpe Ratio: {metrics['sharpe']:.3f}")
            print(f"  CAGR: {metrics['cagr']*100:.1f}%")
            print(f"  Max Drawdown: {metrics['max_drawdown']*100:.1f}%")
            print(f"  Total Return: {metrics['total_return']*100:.1f}%")
            print(f"  NDCG@10: {metrics.get('ndcg_10', 0):.3f}")
            print(f"  NDCG@20: {metrics.get('ndcg_20', 0):.3f}")

        return self.results

    def save_results(self, suffix=""):
        """Save validation results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'lightgbm_granularity_test_{self.n_relevance_levels}levels_{timestamp}{suffix}.txt'

        with open(filename, 'w') as f:
            f.write("="*80 + "\n")
            f.write(f"🚀 LIGHTGBM GRANULARITY TEST - {self.n_relevance_levels} RELEVANCE LEVELS\n")
            f.write("="*80 + "\n\n")

            f.write("CONFIGURATION:\n")
            f.write("- Objective: rank_xendcg (ranking with discrete relevance labels)\n")
            f.write(f"- Relevance Levels: {self.n_relevance_levels} (0 to {self.n_relevance_levels-1})\n")
            f.write(f"- Estimators: {self.params['n_estimators']} (comprehensive hyperparameters)\n")
            f.write("- Time Period: 2005-12-01 to 2025-05-01\n")
            f.write("- Cross-Validation: Time series with 6-month gaps\n")
            f.write("- Portfolio: Top 20 long-only, equal-weighted, weekly rebalancing\n")
            f.write(f"- Features: {len(self.feature_cols)} total features ({self.params['feature_fraction']*100:.0f}% usage)\n")
            f.write(f"- Data: {len(self.data)} samples\n\n")

            f.write("="*80 + "\n")
            f.write("📊 INDIVIDUAL FOLD RESULTS\n")
            f.write("="*80 + "\n\n")

            for result in self.results:
                f.write(f"Fold {result['fold']}: Train {result['train_start'].date()} to {result['train_end'].date()}, ")
                f.write(f"Val {result['val_start'].date()} to {result['val_end'].date()}\n")
                f.write(f"  Sharpe Ratio: {result['sharpe']:.3f}\n")
                f.write(f"  CAGR: {result['cagr']*100:.1f}%\n")
                f.write(f"  Max Drawdown: {result['max_drawdown']*100:.1f}%\n")
                f.write(f"  Total Return: {result['total_return']*100:.1f}%\n")
                f.write(f"  NDCG@10: {result['ndcg_10']:.3f}\n")
                f.write(f"  NDCG@20: {result['ndcg_20']:.3f}\n\n")

            # Calculate summary statistics
            sharpes = [r['sharpe'] for r in self.results]
            cagrs = [r['cagr'] for r in self.results]
            drawdowns = [r['max_drawdown'] for r in self.results]
            ndcg_10s = [r.get('ndcg_10', 0) for r in self.results]
            ndcg_20s = [r.get('ndcg_20', 0) for r in self.results]

            f.write("="*80 + "\n")
            f.write("📈 SUMMARY STATISTICS\n")
            f.write("="*80 + "\n\n")

            f.write(f"Average Sharpe Ratio: {np.mean(sharpes):.3f} ± {np.std(sharpes):.3f}\n")
            f.write(f"Average CAGR: {np.mean(cagrs)*100:.1f}% ± {np.std(cagrs)*100:.1f}%\n")
            f.write(f"Average Max Drawdown: {np.mean(drawdowns)*100:.1f}% ± {np.std(drawdowns)*100:.1f}%\n")
            f.write(f"Average NDCG@10: {np.mean(ndcg_10s):.3f} ± {np.std(ndcg_10s):.3f}\n")
            f.write(f"Average NDCG@20: {np.mean(ndcg_20s):.3f} ± {np.std(ndcg_20s):.3f}\n\n")

            f.write(f"Best Performing Fold: Fold {max(self.results, key=lambda x: x['sharpe'])['fold']} ")
            f.write(f"(Sharpe {max(sharpes):.3f}, CAGR {max(cagrs)*100:.1f}%)\n")
            f.write(f"Worst Performing Fold: Fold {min(self.results, key=lambda x: x['sharpe'])['fold']} ")
            f.write(f"(Sharpe {min(sharpes):.3f}, CAGR {min(cagrs)*100:.1f}%)\n\n")

            f.write("Consistency Metrics:\n")
            f.write(f"- All folds positive returns: {'YES' if all(r['total_return'] > 0 for r in self.results) else 'NO'}\n")
            f.write(f"- Sharpe > 1.0 in all folds: {'YES' if all(r['sharpe'] > 1.0 for r in self.results) else 'NO'}\n")
            f.write(f"- CAGR > 25% in all folds: {'YES' if all(r['cagr'] > 0.25 for r in self.results) else 'NO'}\n")

        print(f"✅ Results saved to: {filename}")
        return filename


def run_granularity_comparison():
    """Run comparison between 5 and 100 relevance levels"""
    print("="*80)
    print("🔬 LIGHTGBM GRANULARITY COMPARISON: 5 vs 100 RELEVANCE LEVELS")
    print("="*80)

    results_comparison = {}

    # Test 1: 5 relevance levels (current approach)
    print("\n" + "="*60)
    print("📊 TESTING 5 RELEVANCE LEVELS (CURRENT APPROACH)")
    print("="*60)

    validator_5 = LightGBMGranularityTester(n_relevance_levels=5)
    results_5 = validator_5.run_validation()
    filename_5 = validator_5.save_results(suffix="_5levels")

    # Calculate summary stats for 5 levels
    sharpes_5 = [r['sharpe'] for r in results_5]
    cagrs_5 = [r['cagr'] for r in results_5]
    ndcg_10s_5 = [r.get('ndcg_10', 0) for r in results_5]

    results_comparison['5_levels'] = {
        'results': results_5,
        'avg_sharpe': np.mean(sharpes_5),
        'std_sharpe': np.std(sharpes_5),
        'avg_cagr': np.mean(cagrs_5),
        'std_cagr': np.std(cagrs_5),
        'avg_ndcg_10': np.mean(ndcg_10s_5),
        'filename': filename_5
    }

    print(f"\n✅ 5 LEVELS SUMMARY:")
    print(f"   Average Sharpe: {np.mean(sharpes_5):.3f} ± {np.std(sharpes_5):.3f}")
    print(f"   Average CAGR: {np.mean(cagrs_5)*100:.1f}% ± {np.std(cagrs_5)*100:.1f}%")
    print(f"   Average NDCG@10: {np.mean(ndcg_10s_5):.3f}")

    # Test 2: 100 relevance levels (granular approach)
    print("\n" + "="*60)
    print("📊 TESTING 100 RELEVANCE LEVELS (GRANULAR APPROACH)")
    print("="*60)

    validator_100 = LightGBMGranularityTester(n_relevance_levels=100)
    results_100 = validator_100.run_validation()
    filename_100 = validator_100.save_results(suffix="_100levels")

    # Calculate summary stats for 100 levels
    sharpes_100 = [r['sharpe'] for r in results_100]
    cagrs_100 = [r['cagr'] for r in results_100]
    ndcg_10s_100 = [r.get('ndcg_10', 0) for r in results_100]

    results_comparison['100_levels'] = {
        'results': results_100,
        'avg_sharpe': np.mean(sharpes_100),
        'std_sharpe': np.std(sharpes_100),
        'avg_cagr': np.mean(cagrs_100),
        'std_cagr': np.std(cagrs_100),
        'avg_ndcg_10': np.mean(ndcg_10s_100),
        'filename': filename_100
    }

    print(f"\n✅ 100 LEVELS SUMMARY:")
    print(f"   Average Sharpe: {np.mean(sharpes_100):.3f} ± {np.std(sharpes_100):.3f}")
    print(f"   Average CAGR: {np.mean(cagrs_100)*100:.1f}% ± {np.std(cagrs_100)*100:.1f}%")
    print(f"   Average NDCG@10: {np.mean(ndcg_10s_100):.3f}")

    # Generate comparison report
    print("\n" + "="*80)
    print("🏆 GRANULARITY COMPARISON RESULTS")
    print("="*80)

    # Calculate improvements
    sharpe_improvement = (np.mean(sharpes_100) - np.mean(sharpes_5)) / np.mean(sharpes_5) * 100
    cagr_improvement = (np.mean(cagrs_100) - np.mean(cagrs_5)) / np.mean(cagrs_5) * 100
    ndcg_improvement = (np.mean(ndcg_10s_100) - np.mean(ndcg_10s_5)) / np.mean(ndcg_10s_5) * 100 if np.mean(ndcg_10s_5) > 0 else 0

    print(f"\n📊 PERFORMANCE COMPARISON:")
    print(f"   Sharpe Ratio:")
    print(f"     5 levels:  {np.mean(sharpes_5):.3f} ± {np.std(sharpes_5):.3f}")
    print(f"     100 levels: {np.mean(sharpes_100):.3f} ± {np.std(sharpes_100):.3f}")
    print(f"     Improvement: {sharpe_improvement:+.1f}%")

    print(f"\n   CAGR:")
    print(f"     5 levels:  {np.mean(cagrs_5)*100:.1f}% ± {np.std(cagrs_5)*100:.1f}%")
    print(f"     100 levels: {np.mean(cagrs_100)*100:.1f}% ± {np.std(cagrs_100)*100:.1f}%")
    print(f"     Improvement: {cagr_improvement:+.1f}%")

    print(f"\n   NDCG@10 (Ranking Quality):")
    print(f"     5 levels:  {np.mean(ndcg_10s_5):.3f}")
    print(f"     100 levels: {np.mean(ndcg_10s_100):.3f}")
    print(f"     Improvement: {ndcg_improvement:+.1f}%")

    # Determine winner
    print(f"\n🏆 WINNER:")
    if np.mean(sharpes_100) > np.mean(sharpes_5):
        print(f"   100 RELEVANCE LEVELS wins with {sharpe_improvement:+.1f}% better Sharpe ratio!")
        print(f"   This confirms that more granular relevance labels improve ranking performance.")
    else:
        print(f"   5 RELEVANCE LEVELS wins with {-sharpe_improvement:+.1f}% better Sharpe ratio.")
        print(f"   This suggests that too much granularity might hurt performance.")

    # Statistical significance test (simple)
    from scipy import stats
    try:
        t_stat, p_value = stats.ttest_ind(sharpes_100, sharpes_5)
        print(f"\n📈 STATISTICAL SIGNIFICANCE:")
        print(f"   T-statistic: {t_stat:.3f}")
        print(f"   P-value: {p_value:.3f}")
        if p_value < 0.05:
            print(f"   Result: STATISTICALLY SIGNIFICANT difference (p < 0.05)")
        else:
            print(f"   Result: NOT statistically significant (p >= 0.05)")
    except:
        print(f"\n📈 STATISTICAL SIGNIFICANCE: Unable to calculate (scipy not available)")

    # Save comparison report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    comparison_filename = f'granularity_comparison_report_{timestamp}.txt'

    with open(comparison_filename, 'w') as f:
        f.write("="*80 + "\n")
        f.write("🔬 LIGHTGBM GRANULARITY COMPARISON REPORT\n")
        f.write("="*80 + "\n\n")

        f.write("EXPERIMENT DESIGN:\n")
        f.write("- Compared 5 vs 100 relevance levels for LightGBM ranking\n")
        f.write("- Same hyperparameters, data, and validation methodology\n")
        f.write("- Same time series cross-validation with 6-month gaps\n")
        f.write("- Same portfolio construction (top 20, equal-weighted)\n\n")

        f.write("RESULTS SUMMARY:\n")
        f.write(f"5 Relevance Levels:\n")
        f.write(f"  Average Sharpe: {np.mean(sharpes_5):.3f} ± {np.std(sharpes_5):.3f}\n")
        f.write(f"  Average CAGR: {np.mean(cagrs_5)*100:.1f}% ± {np.std(cagrs_5)*100:.1f}%\n")
        f.write(f"  Average NDCG@10: {np.mean(ndcg_10s_5):.3f}\n\n")

        f.write(f"100 Relevance Levels:\n")
        f.write(f"  Average Sharpe: {np.mean(sharpes_100):.3f} ± {np.std(sharpes_100):.3f}\n")
        f.write(f"  Average CAGR: {np.mean(cagrs_100)*100:.1f}% ± {np.std(cagrs_100)*100:.1f}%\n")
        f.write(f"  Average NDCG@10: {np.mean(ndcg_10s_100):.3f}\n\n")

        f.write(f"IMPROVEMENTS:\n")
        f.write(f"  Sharpe Ratio: {sharpe_improvement:+.1f}%\n")
        f.write(f"  CAGR: {cagr_improvement:+.1f}%\n")
        f.write(f"  NDCG@10: {ndcg_improvement:+.1f}%\n\n")

        f.write(f"CONCLUSION:\n")
        if np.mean(sharpes_100) > np.mean(sharpes_5):
            f.write(f"100 relevance levels significantly outperform 5 levels.\n")
            f.write(f"More granular relevance labels improve ranking model performance.\n")
        else:
            f.write(f"5 relevance levels outperform 100 levels.\n")
            f.write(f"Too much granularity may hurt ranking model performance.\n")

    print(f"\n✅ Comparison report saved to: {comparison_filename}")
    print(f"✅ Individual results saved to: {filename_5} and {filename_100}")

    return results_comparison


def main():
    """Main execution function"""
    print("🚀 Starting LightGBM Granularity Test...")

    # Run the comparison
    comparison_results = run_granularity_comparison()

    print("\n" + "="*80)
    print("✅ GRANULARITY TEST COMPLETED SUCCESSFULLY!")
    print("="*80)

    # Print final summary
    results_5 = comparison_results['5_levels']
    results_100 = comparison_results['100_levels']

    print(f"\nFINAL SUMMARY:")
    print(f"5 Levels:   Sharpe {results_5['avg_sharpe']:.3f}, CAGR {results_5['avg_cagr']*100:.1f}%")
    print(f"100 Levels: Sharpe {results_100['avg_sharpe']:.3f}, CAGR {results_100['avg_cagr']*100:.1f}%")

    improvement = (results_100['avg_sharpe'] - results_5['avg_sharpe']) / results_5['avg_sharpe'] * 100
    print(f"Improvement: {improvement:+.1f}% Sharpe ratio with 100 levels")


if __name__ == "__main__":
    main()
