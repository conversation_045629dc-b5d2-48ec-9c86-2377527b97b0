#!/usr/bin/env python3
"""
Enhanced LightGBM Validation System with comprehensive ranking optimization
- 10-fold time series CV with 6-month gaps
- Categorical feature handling
- LambdaRank optimization with NDCG evaluation
- Portfolio construction and performance tracking
- SHAP analysis for feature importance
- Results saving and comprehensive evaluation
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import shap
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import LabelEncoder
from datetime import datetime, timedelta
import warnings
import pickle
import json
warnings.filterwarnings('ignore')

class LightGBMValidationSystem:
    def __init__(self):
        self.results = []
        self.feature_importance = {}
        self.models = []
        
        # Hyperparameters from comprehensive_lambdarank_full_validation.py
        self.params = {
            'objective': 'rank_xendcg',  # Keep original objective as specified
            'metric': 'ndcg',
            'boosting_type': 'gbdt',

            # NEW: Hyperparameters from comprehensive validation file
            'n_estimators': 1500,          # Moderate number for full validation
            'max_depth': 8,
            'learning_rate': 0.03,         # Slightly lower for stability
            'num_leaves': 100,
            'subsample': 0.8,
            'min_child_samples': 50,
            'feature_fraction': 0.8,
            # Regularization
            'lambda_l1': 0.001,
            'lambda_l2': 0.001,
            'min_split_gain': 0.001,
            'bagging_freq': 3,
            'verbose': -1,
            'random_state': 42,
            'n_jobs': -1,

            # Enhanced LambdaRank parameters
            'ndcg_eval_at': [10, 20, 50],
            'lambdarank_truncation_level': 100,
            'lambdarank_norm': True
        }

        # COMMENTED OUT: Original optimized hyperparameters (for reference)
        # self.params_original = {
        #     'n_estimators': 14500,  # FULL original hyperparameters
        #     'max_depth': 11,
        #     'learning_rate': 0.00045,
        #     'num_leaves': 330,
        #     'subsample': 0.51,
        #     'min_child_samples': 620,
        #     'extra_trees': True,
        #     'path_smooth': 2.1e-9,
        #     'max_bin': 670,
        #     'min_data_in_bin': 39,
        #     'bin_construct_sample_cnt': 780000,
        #     'lambda_l1': 0.016,
        #     'lambda_l2': 0.026,
        #     'min_split_gain': 0.0071,
        #     'bagging_freq': 3,
        #     'feature_fraction': 0.24,
        #     'verbose': -1,
        #     'random_state': 42
        # }

        # Enhanced categorical feature handling based on actual dataset analysis
        self.categorical_features = [
            # Core categorical codes (definitely categorical)
            'Industry_Code', 'SubIndustry_Code', 'SubSector_Code',

            # Binary/Flag features (categorical)
            'Death_Cross_Event_Binary', 'Golden_Cross_Event_Binary',

            # Sector/Industry boolean flags (categorical)
            'Banks_SubSector', 'Cyclicals_Sector', 'Energy_Sector',
            'Financial_Sector', 'Healthcare_Sector', 'Industrial_Sector',
            'Materials_Sector', 'Tech_Sector',

            # Geographic/Exchange features (categorical)
            'Nordic_Exchanges',

            # Note: Avoiding rank features as they are ordinal (order matters)
            # and LightGBM categorical handling treats them as nominal
        ]
        self.categorical_indices = []
        self.label_encoders = {}
    
    def clean_feature_names(self, df):
        """Clean feature names to remove special characters that LightGBM can't handle"""
        import re

        # Create mapping of old to new names
        name_mapping = {}
        for col in df.columns:
            # Replace special characters with underscores
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            # Remove multiple consecutive underscores
            clean_name = re.sub(r'_+', '_', clean_name)
            # Remove leading/trailing underscores
            clean_name = clean_name.strip('_')
            name_mapping[col] = clean_name

        # Rename columns
        df = df.rename(columns=name_mapping)
        return df, name_mapping

    def remove_infinite_values(self, X, y, dates, tickers, returns):
        """Remove rows with infinite values"""
        print("🧹 Checking for infinite values...")

        # Check for infinite values in features
        inf_mask = np.isinf(X).any(axis=1)

        if inf_mask.sum() > 0:
            print(f"Removing {inf_mask.sum()} rows with infinite values")
            X = X[~inf_mask]
            y = y[~inf_mask]
            dates = dates[~inf_mask]
            tickers = tickers[~inf_mask]
            returns = returns[~inf_mask]
        else:
            print("✓ No infinite values found")

        return X, y, dates, tickers, returns

    def remove_infinite_values_enhanced(self, X, y_relevance, y_continuous, dates, tickers, returns, query_ids):
        """Remove rows with infinite values - enhanced version for dual targets"""
        print("🧹 Checking for infinite values...")

        # Check for infinite values in features
        inf_mask = np.isinf(X).any(axis=1)

        if inf_mask.sum() > 0:
            print(f"Removing {inf_mask.sum()} rows with infinite values")
            X = X[~inf_mask]
            y_relevance = y_relevance[~inf_mask]
            y_continuous = y_continuous[~inf_mask]
            dates = dates[~inf_mask]
            tickers = tickers[~inf_mask]
            returns = returns[~inf_mask]
            query_ids = query_ids[~inf_mask]
        else:
            print("✓ No infinite values found")

        return X, y_relevance, y_continuous, dates, tickers, returns, query_ids

    def process_categorical_features(self, X, feature_columns):
        """Process categorical features for LightGBM"""
        print("🏷️ Processing categorical features...")

        self.categorical_indices = []

        for i, col in enumerate(feature_columns):
            if any(cat in col for cat in self.categorical_features):
                self.categorical_indices.append(i)

                # Convert to string and encode
                if X.iloc[:, i].dtype != 'int':
                    le = LabelEncoder()
                    X.iloc[:, i] = le.fit_transform(X.iloc[:, i].astype(str))
                    self.label_encoders[col] = le

                print(f"  ✓ Processed categorical feature: {col} (index {i})")

        print(f"✓ Found {len(self.categorical_indices)} categorical features")
        return X

    def load_and_prepare_data(self):
        """Load and merge all data files with enhanced preprocessing"""
        print("📊 Loading data...")

        # Load features
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        print(f"✓ Features loaded: {features_df.shape}")

        # Load future returns (for performance evaluation)
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        print(f"✓ Future returns loaded: {returns_df.shape}")

        # Load targets (for ranking)
        target_df = pd.read_csv('PureEURTarget.csv')
        print(f"✓ Targets loaded: {target_df.shape}")

        # Clean feature names
        print("🧹 Cleaning feature names...")
        features_df, self.name_mapping = self.clean_feature_names(features_df)

        # Merge datasets
        print("🔗 Merging datasets...")

        # First merge features with targets
        data = features_df.merge(
            target_df[['Date', 'P123 ID', 'Weighted_MixRel']],
            left_on=['Date', 'P123_ID'],
            right_on=['Date', 'P123 ID'],
            how='inner'
        )

        # Then merge with returns
        data = data.merge(
            returns_df[['Date', 'P123 ID', 'Future5DReturn']],
            left_on=['Date', 'P123_ID'],
            right_on=['Date', 'P123 ID'],
            how='inner'
        )

        print(f"✓ Merged data: {data.shape}")

        # Convert dates and filter time period
        data['Date'] = pd.to_datetime(data['Date'])

        # Filter to specified time period: 2005-12-01 to 2025-05-01
        start_date = pd.to_datetime('2005-12-01')
        end_date = pd.to_datetime('2025-05-01')

        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        print(f"✓ Filtered to {start_date.date()} - {end_date.date()}: {data.shape}")

        # Sort by date and stock
        data = data.sort_values(['Date', 'P123_ID']).reset_index(drop=True)

        # Create daily query groups for ranking
        print("🎯 Creating daily query groups...")
        data['QueryID'] = data['Date'].rank(method='dense').astype(int)

        query_stats = data.groupby('QueryID').agg({
            'Date': 'first',
            'P123_ID': 'count',
            'Weighted_MixRel': ['mean', 'std']
        }).round(4)
        query_stats.columns = ['Date', 'StockCount', 'Target_Mean', 'Target_Std']
        print(f"✓ Created {len(query_stats)} query groups (trading days)")
        print(f"✓ Average stocks per day: {query_stats['StockCount'].mean():.0f}")

        # Create quintile-based relevance labels for ranking
        print("🏷️ Creating quintile-based relevance labels...")
        def create_relevance_labels(group_data, n_levels=5):
            """Convert continuous targets to discrete relevance levels within each day"""
            target_values = group_data['Weighted_MixRel'].dropna()  # Remove NaN values

            if len(target_values) == 0:
                # All NaN values - return middle relevance level
                return pd.Series([n_levels//2] * len(group_data), index=group_data.index)

            if len(target_values) == 1:
                # Single value - assign middle relevance level
                result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                return result

            try:
                # Use quantile-based binning for balanced distribution
                labels = pd.qcut(
                    target_values,
                    q=n_levels,
                    labels=range(n_levels),
                    duplicates='drop'
                )

                # Create result series with proper index
                result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                result.loc[target_values.index] = labels.astype(int)
                return result

            except Exception as e:
                print(f"Warning: qcut failed for group with {len(target_values)} values: {e}")
                # Fall back to rank-based approach for edge cases
                ranks = target_values.rank(method='dense', ascending=False)
                max_rank = ranks.max()

                if max_rank == 1 or pd.isna(max_rank):
                    # All values are the same or NaN
                    result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                    return result

                # Scale ranks to 0-(n_levels-1) range
                relevance = ((ranks - 1) / (max_rank - 1) * (n_levels - 1))
                relevance = (n_levels - 1 - relevance).round().astype(int)

                # Create result series with proper index
                result = pd.Series([n_levels//2] * len(group_data), index=group_data.index)
                result.loc[target_values.index] = relevance
                return result

        # Apply relevance labeling by date
        print("Applying relevance labels by date...")
        data['Relevance'] = data.groupby('Date').apply(
            lambda x: create_relevance_labels(x)
        ).droplevel(0)

        # Check for any remaining NaN values in relevance labels
        nan_relevance = data['Relevance'].isna().sum()
        if nan_relevance > 0:
            print(f"Warning: {nan_relevance} NaN relevance labels found, filling with middle value")
            data['Relevance'] = data['Relevance'].fillna(2)  # Fill with middle relevance level

        # Verify relevance distribution
        relevance_dist = data['Relevance'].value_counts().sort_index()
        print(f"✓ Relevance label distribution:")
        for label, count in relevance_dist.items():
            pct = count / len(data) * 100
            print(f"    Label {label}: {count:,} ({pct:.1f}%)")

        # Identify feature columns (exclude metadata and new columns)
        feature_cols = [col for col in data.columns if col not in
                       ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn', 'QueryID', 'Relevance']]

        print(f"✓ Feature columns: {len(feature_cols)}")

        # Extract arrays for preprocessing
        X = data[feature_cols].copy()
        y_relevance = data['Relevance'].values  # Discrete labels for training
        y_continuous = data['Weighted_MixRel'].values  # Continuous for evaluation
        dates = data['Date'].values
        tickers = data['Ticker'].values if 'Ticker' in data.columns else data['P123_ID'].values
        returns = data['Future5DReturn'].values
        query_ids = data['QueryID'].values

        # Process categorical features
        X = self.process_categorical_features(X, feature_cols)

        # Remove infinite values (enhanced version)
        X_clean, y_rel_clean, y_cont_clean, dates_clean, tickers_clean, returns_clean, query_clean = self.remove_infinite_values_enhanced(
            X.values, y_relevance, y_continuous, dates, tickers, returns, query_ids
        )

        # Reconstruct cleaned data
        data_clean = pd.DataFrame(X_clean, columns=feature_cols)
        data_clean['Date'] = dates_clean
        data_clean['P123_ID'] = tickers_clean
        data_clean['Relevance'] = y_rel_clean
        data_clean['Weighted_MixRel'] = y_cont_clean
        data_clean['Future5DReturn'] = returns_clean
        data_clean['QueryID'] = query_clean

        print(f"✓ Final cleaned data: {data_clean.shape}")

        self.data = data_clean
        self.feature_cols = feature_cols

        return data_clean
    
    def create_time_series_splits(self, n_splits=10, gap_months=6):
        """Create time series splits with gaps"""
        print(f"📅 Creating {n_splits} time series splits with {gap_months}-month gaps...")
        
        # Get unique dates
        unique_dates = sorted(self.data['Date'].unique())
        print(f"Date range: {unique_dates[0].date()} to {unique_dates[-1].date()}")
        print(f"Total unique dates: {len(unique_dates)}")
        
        # Calculate split points
        total_dates = len(unique_dates)
        dates_per_split = total_dates // (n_splits + 1)  # +1 to leave room for final test
        gap_days = gap_months * 30  # Approximate gap in days
        
        splits = []
        
        for i in range(n_splits):
            # Training end
            train_end_idx = (i + 1) * dates_per_split
            train_end_date = unique_dates[min(train_end_idx, len(unique_dates) - 1)]
            
            # Gap
            val_start_date = train_end_date + timedelta(days=gap_days)
            
            # Find validation start index
            val_start_idx = next((idx for idx, date in enumerate(unique_dates) 
                                if date >= val_start_date), len(unique_dates))
            
            # Validation end (next split or end of data)
            val_end_idx = min(val_start_idx + dates_per_split, len(unique_dates) - 1)
            val_end_date = unique_dates[val_end_idx]
            
            if val_start_idx < len(unique_dates):
                splits.append({
                    'train_start': unique_dates[0],
                    'train_end': train_end_date,
                    'val_start': unique_dates[val_start_idx],
                    'val_end': val_end_date,
                    'fold': i + 1
                })
        
        print(f"✓ Created {len(splits)} valid splits")
        for i, split in enumerate(splits):
            print(f"  Fold {split['fold']}: Train {split['train_start'].date()} to {split['train_end'].date()}, "
                  f"Val {split['val_start'].date()} to {split['val_end'].date()}")
        
        self.splits = splits
        return splits
    
    def convert_to_relevance_scores(self, targets, dates, n_levels=5):
        """Convert continuous targets to relevance scores (0-4) within each date group"""
        # Create a copy to avoid modifying original data
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['relevance'] = 0

        # Convert to relevance scores within each date group
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']

            # Handle NaN values by giving them the lowest relevance (0)
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                # Create relevance scores (0 = worst, n_levels-1 = best)
                valid_targets = date_targets[valid_mask]
                # Use quantile-based binning
                relevance_scores = pd.qcut(valid_targets, q=n_levels, labels=False, duplicates='drop')
                df.loc[date_mask & valid_mask, 'relevance'] = relevance_scores
                # NaN values keep relevance 0

        return df['relevance'].values

    def calculate_ndcg_k(self, y_true, y_pred, k):
        """Calculate NDCG@k for ranking evaluation"""
        if len(y_true) < k:
            k = len(y_true)

        # Sort by prediction (descending)
        sorted_indices = np.argsort(y_pred)[::-1]

        # Get top-k predictions and actual values
        top_k_true = y_true[sorted_indices[:k]]

        # Calculate DCG@k
        dcg = np.sum(top_k_true / np.log2(np.arange(2, k + 2)))

        # Calculate IDCG@k (ideal DCG)
        ideal_sorted = np.sort(y_true)[::-1]
        idcg = np.sum(ideal_sorted[:k] / np.log2(np.arange(2, k + 2)))

        # NDCG@k
        if idcg == 0:
            return 0
        return dcg / idcg

    def prepare_ranking_data(self, X, y, dates):
        """Prepare data for LightGBM ranking (group by date)"""
        # Create groups (one group per date)
        unique_dates = sorted(dates.unique())
        groups = []

        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)

        return groups

    def prepare_ranking_data_from_queries(self, query_ids):
        """Prepare data for LightGBM ranking using QueryID groups"""
        # Count samples per query group
        unique_queries = sorted(query_ids.unique())

        groups = []
        for query_id in unique_queries:
            query_mask = query_ids == query_id
            group_size = query_mask.sum()
            if group_size > 0:
                groups.append(group_size)

        return groups

    def calculate_portfolio_metrics_dual_target(self, predictions, actual_returns, processed_target, dates, query_ids, top_k=20):
        """
        Calculate portfolio performance using dual target approach:
        - Use ranking predictions (from discrete relevance labels) to select stocks
        - Use actual continuous returns to calculate performance
        """
        portfolio_returns = []
        ndcg_10_scores = []
        ndcg_20_scores = []

        # Group by query (trading day) and select top k stocks
        unique_queries = sorted(query_ids.unique())

        for query_id in unique_queries:
            query_mask = query_ids == query_id
            query_indices = np.where(query_mask)[0]  # Get actual indices

            query_predictions = predictions[query_mask]
            query_actual_returns = actual_returns.iloc[query_indices]
            query_processed_target = processed_target.iloc[query_indices]

            if len(query_predictions) >= top_k:
                # Select top k stocks by ranking prediction
                top_indices = np.argsort(query_predictions)[-top_k:]

                # Calculate portfolio return using ACTUAL future returns
                top_actual_returns = query_actual_returns.iloc[top_indices]
                portfolio_return = top_actual_returns.mean() / 100.0  # Convert percentage to decimal
                portfolio_returns.append(portfolio_return)

                # Calculate NDCG metrics using processed target for ranking quality
                ndcg_10 = self.calculate_ndcg_k(query_processed_target.values, query_predictions, 10)
                ndcg_20 = self.calculate_ndcg_k(query_processed_target.values, query_predictions, 20)
                ndcg_10_scores.append(ndcg_10)
                ndcg_20_scores.append(ndcg_20)

        if len(portfolio_returns) == 0:
            return {'sharpe': 0, 'cagr': 0, 'max_drawdown': 0, 'total_return': 0, 'ndcg_10': 0, 'ndcg_20': 0}

        portfolio_returns = np.array(portfolio_returns)

        # Calculate performance metrics
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()

        # Annualize (assuming weekly rebalancing)
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0

        # CAGR
        cumulative_return = np.prod(1 + portfolio_returns) - 1
        n_years = len(portfolio_returns) / 52
        cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 else 0

        # Max drawdown
        cumulative_wealth = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_wealth)
        drawdowns = (cumulative_wealth - running_max) / running_max
        max_drawdown = np.min(drawdowns)

        return {
            'sharpe': sharpe,
            'cagr': cagr,
            'max_drawdown': max_drawdown,
            'total_return': cumulative_return,
            'n_periods': len(portfolio_returns),
            'ndcg_10': np.mean(ndcg_10_scores) if ndcg_10_scores else 0,
            'ndcg_20': np.mean(ndcg_20_scores) if ndcg_20_scores else 0,
            'volatility': annual_volatility
        }

    def calculate_portfolio_metrics(self, predictions, returns, dates, top_k=20):
        """Calculate enhanced portfolio performance metrics with NDCG"""
        portfolio_returns = []
        ndcg_10_scores = []
        ndcg_20_scores = []

        # Group by date and select top k stocks
        for date in sorted(dates.unique()):
            date_mask = dates == date
            date_predictions = predictions[date_mask]
            date_returns = returns[date_mask]

            if len(date_predictions) >= top_k:
                # Get top k stocks by prediction
                top_indices = np.argsort(date_predictions)[-top_k:]
                top_returns = date_returns.iloc[top_indices]

                # Equal-weighted portfolio return
                portfolio_return = top_returns.mean() / 100.0  # Convert percentage to decimal
                portfolio_returns.append(portfolio_return)

                # Calculate NDCG metrics
                ndcg_10 = self.calculate_ndcg_k(date_returns.values, date_predictions, 10)
                ndcg_20 = self.calculate_ndcg_k(date_returns.values, date_predictions, 20)
                ndcg_10_scores.append(ndcg_10)
                ndcg_20_scores.append(ndcg_20)
        
        if len(portfolio_returns) == 0:
            return {'sharpe': 0, 'cagr': 0, 'max_drawdown': 0, 'total_return': 0}
        
        portfolio_returns = np.array(portfolio_returns)
        
        # Calculate metrics
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()
        
        # Annualize (assuming weekly rebalancing)
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
        
        # CAGR
        cumulative_return = np.prod(1 + portfolio_returns) - 1
        n_years = len(portfolio_returns) / 52
        cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 else 0
        
        # Max drawdown
        cumulative_wealth = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_wealth)
        drawdowns = (cumulative_wealth - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        return {
            'sharpe': sharpe,
            'cagr': cagr,
            'max_drawdown': max_drawdown,
            'total_return': cumulative_return,
            'n_periods': len(portfolio_returns),
            'ndcg_10': np.mean(ndcg_10_scores) if ndcg_10_scores else 0,
            'ndcg_20': np.mean(ndcg_20_scores) if ndcg_20_scores else 0,
            'volatility': annual_volatility
        }
    
    def run_validation(self):
        """Run the complete validation process"""
        print("="*60)
        print("🚀 STARTING LIGHTGBM VALIDATION")
        print("="*60)
        
        # Load data
        self.load_and_prepare_data()
        
        # Create splits
        self.create_time_series_splits()
        
        # Run validation for each fold
        for split in self.splits:
            print(f"\n📊 Processing Fold {split['fold']}")
            print("-" * 40)
            
            # Prepare train/validation data
            train_mask = (self.data['Date'] >= split['train_start']) & (self.data['Date'] <= split['train_end'])
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])
            
            train_data = self.data[train_mask].copy()
            val_data = self.data[val_mask].copy()
            
            print(f"Train: {len(train_data)} samples, {train_data['Date'].nunique()} dates")
            print(f"Val: {len(val_data)} samples, {val_data['Date'].nunique()} dates")
            
            if len(train_data) == 0 or len(val_data) == 0:
                print("⚠️ Skipping fold due to insufficient data")
                continue
            
            # Prepare features and targets
            X_train = train_data[self.feature_cols]
            y_train = train_data['Relevance'].values  # Use pre-computed relevance labels
            train_groups = self.prepare_ranking_data_from_queries(train_data['QueryID'])

            X_val = val_data[self.feature_cols]
            y_val = val_data['Relevance'].values  # Use pre-computed relevance labels
            val_groups = self.prepare_ranking_data_from_queries(val_data['QueryID'])

            # DEBUG: Verify we're using discrete relevance labels
            print(f"🔍 DEBUG - Training target stats:")
            print(f"    y_train unique values: {np.unique(y_train)}")
            print(f"    y_train distribution: {np.bincount(y_train.astype(int))}")
            print(f"    Categorical indices: {len(self.categorical_indices)} features")
            print(f"    Train groups: {len(train_groups)} groups, total samples: {sum(train_groups)}")
            
            # Handle missing values
            X_train = X_train.fillna(0)
            X_val = X_val.fillna(0)
            
            # Train model
            print(f"🧠 Training LightGBM ({self.params['n_estimators']} ESTIMATORS, COMPREHENSIVE HYPERPARAMS)...")

            train_dataset = lgb.Dataset(
                X_train,
                label=y_train,
                group=train_groups,
                categorical_feature=self.categorical_indices if self.categorical_indices else 'auto'
            )
            val_dataset = lgb.Dataset(
                X_val,
                label=y_val,
                group=val_groups,
                reference=train_dataset,
                categorical_feature=self.categorical_indices if self.categorical_indices else 'auto'
            )

            # Train without early stopping to allow full feature usage
            model = lgb.train(
                self.params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Make predictions
            val_predictions = model.predict(X_val)
            
            # Calculate portfolio performance using actual future returns
            # This implements the dual approach: ranking predictions + continuous returns
            metrics = self.calculate_portfolio_metrics_dual_target(
                val_predictions,           # Ranking predictions from discrete labels
                val_data['Future5DReturn'], # Actual continuous future returns
                val_data['Weighted_MixRel'], # Processed target for comparison
                val_data['Date'],
                val_data['QueryID']
            )
            
            # Store results
            result = {
                'fold': split['fold'],
                'train_start': split['train_start'],
                'train_end': split['train_end'],
                'val_start': split['val_start'],
                'val_end': split['val_end'],
                'train_samples': len(train_data),
                'val_samples': len(val_data),
                'sharpe': metrics['sharpe'],
                'cagr': metrics['cagr'],
                'max_drawdown': metrics['max_drawdown'],
                'total_return': metrics['total_return'],
                'n_periods': metrics['n_periods']
            }
            
            self.results.append(result)
            self.models.append(model)
            
            print(f"✓ Fold {split['fold']} Results:")
            print(f"  Sharpe Ratio: {metrics['sharpe']:.3f}")
            print(f"  CAGR: {metrics['cagr']*100:.1f}%")
            print(f"  Max Drawdown: {metrics['max_drawdown']*100:.1f}%")
            print(f"  Total Return: {metrics['total_return']*100:.1f}%")
        
        return self.results

    def generate_final_predictions(self):
        """Generate predictions for the final date in May 2025"""
        print("\n" + "="*60)
        print("🔮 GENERATING FINAL PREDICTIONS")
        print("="*60)

        # Get the latest date available (should be in April/May 2025)
        latest_date = self.data['Date'].max()
        final_data = self.data[self.data['Date'] == latest_date]

        print(f"Final prediction date: {latest_date.date()}")
        print(f"Number of stocks: {len(final_data)}")

        # Use the best performing model (highest Sharpe ratio)
        if len(self.models) == 0:
            print("❌ No trained models available")
            return None

        best_fold_idx = np.argmax([r['sharpe'] for r in self.results])
        best_model = self.models[best_fold_idx]

        print(f"Using model from Fold {best_fold_idx + 1} (Sharpe: {self.results[best_fold_idx]['sharpe']:.3f})")

        # Prepare features
        X_final = final_data[self.feature_cols].fillna(0)

        # Generate predictions
        predictions = best_model.predict(X_final)

        # Create results DataFrame
        final_predictions = pd.DataFrame({
            'Date': final_data['Date'],
            'P123_ID': final_data['P123_ID'],
            'Ticker': final_data['Ticker'],
            'Prediction': predictions,
            'Weighted_MixRel': final_data['Weighted_MixRel']
        })

        # Sort by prediction (highest first)
        final_predictions = final_predictions.sort_values('Prediction', ascending=False)

        print(f"\n📊 TOP 20 PREDICTIONS FOR {latest_date.date()}:")
        print("-" * 60)
        top_20 = final_predictions.head(20)
        for i, row in top_20.iterrows():
            print(f"{row.name+1:2d}. {row['Ticker']:12s} | Pred: {row['Prediction']:8.4f} | Target: {row['Weighted_MixRel']:8.2f}")

        return final_predictions

    def run_comprehensive_shap_analysis(self):
        """Run SHAP analysis on all folds and average the results"""
        print("\n" + "="*60)
        print("🔍 COMPREHENSIVE SHAP ANALYSIS (ALL FOLDS)")
        print("="*60)

        if len(self.models) == 0:
            print("❌ No trained models available")
            return None

        print(f"Running SHAP analysis on all {len(self.models)} folds...")

        # Store SHAP values from all folds
        all_fold_importance = []

        for fold_idx, (model, split) in enumerate(zip(self.models, self.splits)):
            print(f"\n📊 Processing Fold {fold_idx + 1}...")

            # Get validation data for this fold
            val_mask = (self.data['Date'] >= split['val_start']) & (self.data['Date'] <= split['val_end'])
            val_data = self.data[val_mask]

            if len(val_data) == 0:
                print(f"⚠️ No validation data for Fold {fold_idx + 1}")
                continue

            # Sample for SHAP (use subset for speed but ensure good coverage)
            sample_size = min(500, len(val_data))  # 500 per fold = 5000 total samples
            sample_data = val_data.sample(n=sample_size, random_state=42)
            X_sample = sample_data[self.feature_cols].fillna(0)

            print(f"  Computing SHAP values for {len(X_sample)} samples...")

            try:
                # Create SHAP explainer for this fold's model
                explainer = shap.TreeExplainer(model)
                shap_values = explainer.shap_values(X_sample)

                # Calculate feature importance for this fold
                fold_importance = np.abs(shap_values).mean(axis=0)
                all_fold_importance.append(fold_importance)

                print(f"  ✓ Fold {fold_idx + 1} SHAP analysis complete")

            except Exception as e:
                print(f"  ❌ Error in Fold {fold_idx + 1}: {e}")
                continue

        if len(all_fold_importance) == 0:
            print("❌ No SHAP analysis completed successfully")
            return None

        print(f"\n🔄 Averaging SHAP values across {len(all_fold_importance)} folds...")

        # Average importance across all folds
        avg_importance = np.mean(all_fold_importance, axis=0)
        std_importance = np.std(all_fold_importance, axis=0)

        # Create comprehensive feature importance DataFrame
        importance_df = pd.DataFrame({
            'feature': self.feature_cols,
            'avg_importance': avg_importance,
            'std_importance': std_importance,
            'cv_importance': std_importance / (avg_importance + 1e-10)  # Coefficient of variation
        }).sort_values('avg_importance', ascending=False)

        # Add rank
        importance_df['rank'] = range(1, len(importance_df) + 1)

        print(f"\n📊 TOP 20 MOST IMPORTANT FEATURES (AVERAGED ACROSS ALL FOLDS):")
        print("-" * 80)
        print(f"{'Rank':<4} {'Feature':<40} {'Avg Importance':<15} {'Std':<10} {'CV':<8}")
        print("-" * 80)

        for i, (_, row) in enumerate(importance_df.head(20).iterrows()):
            print(f"{row['rank']:3d}. {row['feature']:<40} {row['avg_importance']:<15.6f} "
                  f"{row['std_importance']:<10.6f} {row['cv_importance']:<8.3f}")

        # Save to CSV (FULL 14500 estimators, no early stopping)
        csv_filename = 'comprehensive_feature_importance_FULL_14500_no_early_stopping.csv'
        importance_df.to_csv(csv_filename, index=False)
        print(f"\n💾 Feature importance saved to: {csv_filename}")
        print(f"   Total features ranked: {len(importance_df)}")

        # Store results
        self.feature_importance = importance_df

        # Print summary statistics
        print(f"\n📈 FEATURE IMPORTANCE SUMMARY:")
        print(f"  Most important feature: {importance_df.iloc[0]['feature']}")
        print(f"  Highest importance score: {importance_df.iloc[0]['avg_importance']:.6f}")
        print(f"  Features with importance > 0.00001: {len(importance_df[importance_df['avg_importance'] > 0.00001])}")
        print(f"  Features with importance > 0.000001: {len(importance_df[importance_df['avg_importance'] > 0.000001])}")

        return importance_df

def main():
    # Initialize validation system
    validator = LightGBMValidationSystem()

    # Run validation
    results = validator.run_validation()

    # Print summary
    print("\n" + "="*60)
    print("📊 VALIDATION SUMMARY")
    print("="*60)

    if len(results) > 0:
        sharpes = [r['sharpe'] for r in results]
        cagrs = [r['cagr'] for r in results]
        drawdowns = [r['max_drawdown'] for r in results]

        # Extract NDCG metrics (handle missing keys)
        ndcg_10s = [r.get('ndcg_10', 0) for r in results]
        ndcg_20s = [r.get('ndcg_20', 0) for r in results]
        volatilities = [r['volatility'] for r in results]

        print(f"Average Sharpe Ratio: {np.mean(sharpes):.3f} ± {np.std(sharpes):.3f}")
        print(f"Average CAGR: {np.mean(cagrs)*100:.1f}% ± {np.std(cagrs)*100:.1f}%")
        print(f"Average Max Drawdown: {np.mean(drawdowns)*100:.1f}% ± {np.std(drawdowns)*100:.1f}%")
        print(f"Average NDCG@10: {np.mean(ndcg_10s):.3f} ± {np.std(ndcg_10s):.3f}")
        print(f"Average NDCG@20: {np.mean(ndcg_20s):.3f} ± {np.std(ndcg_20s):.3f}")
        print(f"Average Volatility: {np.mean(volatilities)*100:.1f}% ± {np.std(volatilities)*100:.1f}%")

        print(f"\nFold-by-fold results:")
        for result in results:
            print(f"Fold {result['fold']}: Sharpe={result['sharpe']:.3f}, "
                  f"CAGR={result['cagr']*100:.1f}%, DD={result['max_drawdown']*100:.1f}%, "
                  f"NDCG@10={result['ndcg_10']:.3f}, NDCG@20={result['ndcg_20']:.3f}")

        # Generate final predictions
        final_predictions = validator.generate_final_predictions()

        # Run comprehensive SHAP analysis across all folds
        feature_importance = validator.run_comprehensive_shap_analysis()

        # Save comprehensive results
        print("\n💾 Saving results...")

        # Save validation results
        results_summary = {
            'validation_results': results,
            'average_metrics': {
                'sharpe': np.mean([r['sharpe'] for r in results]),
                'cagr': np.mean([r['cagr'] for r in results]),
                'max_drawdown': np.mean([r['max_drawdown'] for r in results]),
                'ndcg_10': np.mean([r['ndcg_10'] for r in results]),
                'ndcg_20': np.mean([r['ndcg_20'] for r in results]),
                'volatility': np.mean([r['volatility'] for r in results])
            },
            'model_parameters': validator.params,
            'categorical_features': validator.categorical_features,
            'categorical_indices': validator.categorical_indices,
            'feature_count': len(validator.feature_cols),
            'data_shape': validator.data.shape,
            'timestamp': pd.Timestamp.now().isoformat()
        }

        # Save to JSON
        with open('enhanced_lightgbm_validation_results.json', 'w') as f:
            import json
            json.dump(results_summary, f, indent=2, default=str)

        # Save final predictions
        final_predictions.to_csv('enhanced_final_predictions.csv', index=False)

        print(f"✓ Results saved to enhanced_lightgbm_validation_results.json")
        print(f"✓ Final predictions saved to enhanced_final_predictions.csv")

        print(f"\n🎯 ANALYSIS COMPLETE!")
        print(f"✓ {len(results)} fold validation completed")
        print(f"✓ Final predictions generated")
        print(f"✓ SHAP feature importance computed")
        print(f"✓ All results saved to files")

    else:
        print("❌ No results generated")

if __name__ == "__main__":
    main()
