#!/usr/bin/env python3
"""
Fixed baseline validation using actual features (not target as feature)
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import re
from datetime import datetime

def clean_feature_name(name: str) -> str:
    """Clean feature names for LightGBM"""
    cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', str(name))
    cleaned = re.sub(r'_+', '_', cleaned).strip('_')
    if cleaned and cleaned[0].isdigit():
        cleaned = 'F_' + cleaned
    return cleaned or 'Feature_Unknown'

def fixed_baseline_validation():
    """Baseline validation using ACTUAL features, not target as feature"""
    
    print("🎯 Fixed Baseline Validation (Using Real Features)")
    print("="*60)
    
    # Load all data files
    print("Loading data files...")
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    target_df = pd.read_csv('PureEURTarget.csv')
    returns_df = pd.read_csv('PureEURFuture1WRet.csv')
    
    print(f"Features: {features_df.shape}")
    print(f"Target: {target_df.shape}")
    print(f"Returns: {returns_df.shape}")
    
    # Merge data properly
    print("Merging datasets...")
    merged_data = features_df.merge(target_df, on=['Date', 'P123 ID'], how='inner')
    merged_data = merged_data.merge(returns_df, on=['Date', 'P123 ID'], how='inner')
    
    print(f"Merged data: {merged_data.shape}")
    
    # Identify ACTUAL features (exclude target, returns, metadata)
    exclude_cols = ['Date', 'P123 ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']
    
    feature_cols = [col for col in merged_data.columns 
                   if col not in exclude_cols and 
                   merged_data[col].dtype in ['int64', 'float64']]
    
    print(f"Actual feature columns: {len(feature_cols)}")
    print(f"First 10 features: {feature_cols[:10]}")
    
    # Verify we're not using target as feature
    if 'Weighted_MixRel' in feature_cols:
        print("🚨 ERROR: Target is in features!")
        return
    
    if 'Future5DReturn' in feature_cols:
        print("🚨 ERROR: Future returns in features!")
        return
    
    print("✅ Target and returns properly excluded from features")
    
    # Take a smaller sample for memory efficiency
    print("Sampling data for memory efficiency...")
    sample_size = min(100000, len(merged_data))
    merged_data = merged_data.sample(n=sample_size, random_state=42).sort_values('Date')
    print(f"Using sample of {len(merged_data)} rows")
    
    # Clean feature names
    feature_mapping = {}
    for col in feature_cols:
        cleaned = clean_feature_name(col)
        feature_mapping[col] = cleaned
    
    # Prepare data
    X = merged_data[feature_cols].copy()
    X.columns = [feature_mapping[col] for col in X.columns]
    X = X.fillna(0)
    X = X.replace([np.inf, -np.inf], 0)
    
    y = merged_data['Weighted_MixRel']
    dates = pd.to_datetime(merged_data['Date'])
    returns = merged_data['Future5DReturn']
    
    print(f"Final dataset: {X.shape}")
    print(f"Target stats: mean={y.mean():.3f}, std={y.std():.3f}")
    print(f"Returns stats: mean={returns.mean():.3f}, std={returns.std():.3f}")
    
    # Simple time series validation
    unique_dates = sorted(dates.unique())
    print(f"Date range: {unique_dates[0]} to {unique_dates[-1]}")
    
    # Create 5 simple time-based splits
    n_splits = 5
    split_size = len(unique_dates) // (n_splits + 1)
    
    fold_results = []
    
    for fold_idx in range(n_splits):
        try:
            # Define train/val periods
            train_start_idx = fold_idx * split_size
            train_end_idx = train_start_idx + split_size
            val_start_idx = train_end_idx
            val_end_idx = val_start_idx + split_size
            
            if val_end_idx >= len(unique_dates):
                break
            
            train_dates = unique_dates[train_start_idx:train_end_idx]
            val_dates = unique_dates[val_start_idx:val_end_idx]
            
            # Create masks
            train_mask = dates.isin(train_dates)
            val_mask = dates.isin(val_dates)
            
            # Get fold data
            X_train = X[train_mask].copy()
            y_train = y[train_mask].copy()
            X_val = X[val_mask].copy()
            y_val = y[val_mask].copy()
            
            # Get corresponding returns and dates for portfolio calculation
            val_returns = returns[val_mask].copy()
            val_dates_series = dates[val_mask].copy()
            
            print(f"\nFold {fold_idx}: Train={len(X_train)}, Val={len(X_val)}")
            
            if len(X_train) < 100 or len(X_val) < 50:
                print("  Skipping - insufficient data")
                continue
            
            # Clean data
            X_train = X_train.fillna(X_train.median())
            X_val = X_val.fillna(X_train.median())
            
            # Remove NaN targets
            train_valid = ~y_train.isna()
            val_valid = ~y_val.isna()
            
            X_train = X_train[train_valid]
            y_train = y_train[train_valid]
            X_val = X_val[val_valid]
            y_val = y_val[val_valid]
            val_returns = val_returns[val_valid]
            val_dates_series = val_dates_series[val_valid]
            
            if len(X_train) < 50 or len(X_val) < 20:
                print("  Skipping - insufficient valid data")
                continue
            
            # Train LightGBM model
            train_dataset = lgb.Dataset(X_train, label=y_train)
            val_dataset = lgb.Dataset(X_val, label=y_val, reference=train_dataset)
            
            params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1
            }
            
            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[val_dataset],
                num_boost_round=100,
                callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
            )
            
            # Make predictions
            predictions = model.predict(X_val)
            
            # Calculate portfolio performance (EXACT same as working code)
            portfolio_returns = []
            
            for date in sorted(val_dates_series.unique()):
                date_mask = val_dates_series == date
                date_predictions = predictions[date_mask]
                date_returns = val_returns[date_mask]
                
                if len(date_predictions) >= 15:
                    # Select top 15 stocks by prediction
                    top_indices = np.argsort(date_predictions)[-15:]
                    top_returns = date_returns.iloc[top_indices]
                    
                    # Portfolio return - CRITICAL: Convert percentage to decimal
                    portfolio_return = top_returns.mean() / 100.0
                    portfolio_returns.append(portfolio_return)
            
            if len(portfolio_returns) > 0:
                portfolio_returns = np.array(portfolio_returns)
                
                # Calculate Sharpe (EXACT same as working code)
                mean_return = portfolio_returns.mean()
                volatility = portfolio_returns.std()
                
                # Annualize (weekly rebalancing)
                annual_return = mean_return * 52
                annual_volatility = volatility * np.sqrt(52)
                sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
                
                fold_results.append(sharpe)
                
                print(f"  Sharpe: {sharpe:.3f}, Annual Return: {annual_return:.1%}, Annual Vol: {annual_volatility:.1%}")
            else:
                print("  No portfolio returns calculated")
            
        except Exception as e:
            print(f"  Error in fold {fold_idx}: {str(e)}")
            continue
    
    # Final results
    if fold_results:
        mean_sharpe = np.mean(fold_results)
        std_sharpe = np.std(fold_results)
        
        print(f"\n🎯 FIXED BASELINE RESULTS:")
        print(f"="*50)
        print(f"Mean Sharpe: {mean_sharpe:.4f} ± {std_sharpe:.4f}")
        print(f"Folds: {len(fold_results)}")
        print(f"Min: {np.min(fold_results):.4f}")
        print(f"Max: {np.max(fold_results):.4f}")
        
        # Check if results are reasonable
        if 0.5 <= mean_sharpe <= 3.0:
            print("✅ Results look reasonable!")
        elif mean_sharpe > 5.0:
            print("⚠️  Sharpe still too high - check for remaining data leakage")
        else:
            print("⚠️  Sharpe very low - model may not be working")
        
        # Compare to LOFEP baseline
        lofep_baseline = 1.7765
        print(f"\nComparison to LOFEP:")
        print(f"LOFEP baseline: {lofep_baseline:.4f}")
        print(f"This validation: {mean_sharpe:.4f}")
        print(f"Difference: {mean_sharpe - lofep_baseline:+.4f}")
        
        return mean_sharpe
    else:
        print("❌ No successful folds")
        return 0

if __name__ == "__main__":
    sharpe = fixed_baseline_validation()
    
    print(f"\n📊 Summary:")
    print(f"Fixed baseline Sharpe: {sharpe:.4f}")
    
    if 0.5 <= sharpe <= 3.0:
        print("✅ Baseline working correctly - LOFEP improvements are meaningful!")
    else:
        print("❌ Baseline still has issues - need further investigation")
