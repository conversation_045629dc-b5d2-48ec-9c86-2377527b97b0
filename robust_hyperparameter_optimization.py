#!/usr/bin/env python3
"""
Robust Hyperparameter Optimization for LightGBM Ranking
- Nested Time Series Cross-Validation
- Bayesian Optimization with Optuna
- Multi-objective optimization (Sharpe, CAGR, Drawdown)
- Prevents data leakage
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import optuna
from sklearn.model_selection import TimeSeriesSplit
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class RobustHyperparameterOptimizer:
    def __init__(self, n_outer_folds=5, n_inner_folds=3, n_trials=100):
        self.n_outer_folds = n_outer_folds
        self.n_inner_folds = n_inner_folds
        self.n_trials = n_trials
        self.best_params = None
        self.optimization_results = []
        
    def load_and_prepare_data(self):
        """Load and merge all data files (same as main system)"""
        print("📊 Loading data for hyperparameter optimization...")
        
        # Load datasets
        features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
        target_df = pd.read_csv('PureEURTarget.csv')
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        
        # Clean feature names
        import re
        name_mapping = {}
        for col in features_df.columns:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')
            name_mapping[col] = clean_name
        features_df = features_df.rename(columns=name_mapping)
        
        # Merge datasets
        data = features_df.merge(
            target_df[['Date', 'P123 ID', 'Weighted_MixRel']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        data = data.merge(
            returns_df[['Date', 'P123 ID', 'Future5DReturn']], 
            left_on=['Date', 'P123_ID'], 
            right_on=['Date', 'P123 ID'],
            how='inner'
        )
        
        # Filter time period and sort
        data['Date'] = pd.to_datetime(data['Date'])
        start_date = pd.to_datetime('2010-01-01')  # Use recent data for faster optimization
        end_date = pd.to_datetime('2023-12-31')
        
        data = data[(data['Date'] >= start_date) & (data['Date'] <= end_date)]
        data = data.sort_values('Date').reset_index(drop=True)
        
        # Identify feature columns
        feature_cols = [col for col in data.columns if col not in 
                       ['Date', 'P123_ID', 'Ticker', 'Weighted_MixRel', 'Future5DReturn']]
        
        print(f"✓ Optimization data: {len(data)} samples, {len(feature_cols)} features")
        print(f"✓ Date range: {data['Date'].min().date()} to {data['Date'].max().date()}")
        
        self.data = data
        self.feature_cols = feature_cols
        
        return data
    
    def convert_to_relevance_scores(self, targets, dates, n_levels=5):
        """Convert continuous targets to relevance scores"""
        df = pd.DataFrame({'target': targets, 'date': dates})
        df['relevance'] = 0
        
        for date in df['date'].unique():
            date_mask = df['date'] == date
            date_targets = df.loc[date_mask, 'target']
            
            valid_mask = ~date_targets.isna()
            if valid_mask.sum() > 0:
                valid_targets = date_targets[valid_mask]
                relevance_scores = pd.qcut(valid_targets, q=n_levels, labels=False, duplicates='drop')
                df.loc[date_mask & valid_mask, 'relevance'] = relevance_scores
        
        return df['relevance'].values
    
    def prepare_ranking_data(self, dates):
        """Prepare groups for LightGBM ranking"""
        unique_dates = sorted(dates.unique())
        groups = []
        for date in unique_dates:
            group_size = len(dates[dates == date])
            groups.append(group_size)
        return groups
    
    def calculate_portfolio_metrics(self, predictions, returns, dates, top_k=20):
        """Calculate portfolio performance metrics"""
        portfolio_returns = []
        
        for date in sorted(dates.unique()):
            date_mask = dates == date
            date_predictions = predictions[date_mask]
            date_returns = returns[date_mask]
            
            if len(date_predictions) >= top_k:
                top_indices = np.argsort(date_predictions)[-top_k:]
                top_returns = date_returns.iloc[top_indices]
                portfolio_return = top_returns.mean() / 100.0
                portfolio_returns.append(portfolio_return)
        
        if len(portfolio_returns) == 0:
            return {'sharpe': 0, 'cagr': 0, 'max_drawdown': 0}
        
        portfolio_returns = np.array(portfolio_returns)
        
        # Calculate metrics
        mean_return = portfolio_returns.mean()
        volatility = portfolio_returns.std()
        
        # Annualize (weekly rebalancing)
        annual_return = mean_return * 52
        annual_volatility = volatility * np.sqrt(52)
        sharpe = annual_return / annual_volatility if annual_volatility > 0 else 0
        
        # CAGR
        cumulative_return = np.prod(1 + portfolio_returns) - 1
        n_years = len(portfolio_returns) / 52
        cagr = (1 + cumulative_return) ** (1/n_years) - 1 if n_years > 0 else 0
        
        # Max drawdown
        cumulative_wealth = np.cumprod(1 + portfolio_returns)
        running_max = np.maximum.accumulate(cumulative_wealth)
        drawdowns = (cumulative_wealth - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        return {
            'sharpe': sharpe,
            'cagr': cagr,
            'max_drawdown': max_drawdown
        }
    
    def create_nested_time_series_splits(self):
        """Create nested time series splits for robust optimization"""
        unique_dates = sorted(self.data['Date'].unique())
        total_dates = len(unique_dates)
        
        # Outer splits (for final evaluation)
        outer_splits = []
        dates_per_outer_fold = total_dates // (self.n_outer_folds + 1)
        
        for i in range(self.n_outer_folds):
            train_end_idx = (i + 1) * dates_per_outer_fold
            test_start_idx = train_end_idx + 26  # 6-month gap (26 weeks)
            test_end_idx = min(test_start_idx + dates_per_outer_fold, total_dates)
            
            if test_start_idx < total_dates:
                outer_splits.append({
                    'train_end': unique_dates[train_end_idx],
                    'test_start': unique_dates[test_start_idx],
                    'test_end': unique_dates[test_end_idx - 1] if test_end_idx < total_dates else unique_dates[-1]
                })
        
        self.outer_splits = outer_splits
        print(f"✓ Created {len(outer_splits)} outer folds for evaluation")
        
        return outer_splits
    
    def objective_function(self, trial, train_data, val_data):
        """Optuna objective function for hyperparameter optimization"""
        
        # Define hyperparameter search space
        params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'verbose': -1,
            'random_state': 42,
            
            # Core parameters to optimize
            'n_estimators': trial.suggest_int('n_estimators', 500, 5000, step=500),
            'max_depth': trial.suggest_int('max_depth', 6, 15),
            'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.1, log=True),
            'num_leaves': trial.suggest_int('num_leaves', 50, 500),
            'subsample': trial.suggest_float('subsample', 0.4, 0.8),
            'min_child_samples': trial.suggest_int('min_child_samples', 100, 1000),
            
            # Regularization
            'lambda_l1': trial.suggest_float('lambda_l1', 0.001, 0.1, log=True),
            'lambda_l2': trial.suggest_float('lambda_l2', 0.001, 0.1, log=True),
            'min_split_gain': trial.suggest_float('min_split_gain', 0.001, 0.1, log=True),
            
            # Feature selection
            'feature_fraction': trial.suggest_float('feature_fraction', 0.1, 0.8),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
            
            # Advanced parameters
            'max_bin': trial.suggest_int('max_bin', 200, 1000),
            'min_data_in_bin': trial.suggest_int('min_data_in_bin', 10, 100),
        }
        
        # Prepare training data
        X_train = train_data[self.feature_cols].fillna(0)
        y_train = self.convert_to_relevance_scores(train_data['Weighted_MixRel'], train_data['Date'])
        train_groups = self.prepare_ranking_data(train_data['Date'])
        
        # Prepare validation data
        X_val = val_data[self.feature_cols].fillna(0)
        y_val = self.convert_to_relevance_scores(val_data['Weighted_MixRel'], val_data['Date'])
        val_groups = self.prepare_ranking_data(val_data['Date'])
        
        try:
            # Train model
            train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
            val_dataset = lgb.Dataset(X_val, label=y_val, group=val_groups, reference=train_dataset)
            
            model = lgb.train(
                params,
                train_dataset,
                valid_sets=[val_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Make predictions
            val_predictions = model.predict(X_val)
            
            # Calculate portfolio metrics
            metrics = self.calculate_portfolio_metrics(
                val_predictions, 
                val_data['Future5DReturn'], 
                val_data['Date']
            )
            
            # Multi-objective: Primary = Sharpe, Secondary = CAGR, Penalty = Drawdown
            sharpe = metrics['sharpe']
            cagr = metrics['cagr']
            max_dd = abs(metrics['max_drawdown'])
            
            # Combined objective (maximize Sharpe, minimize drawdown)
            objective_value = sharpe - (max_dd * 0.5)  # Penalize high drawdown
            
            return objective_value
            
        except Exception as e:
            print(f"Trial failed: {e}")
            return -999  # Return very bad score for failed trials
    
    def optimize_hyperparameters(self):
        """Run nested cross-validation with Bayesian optimization"""
        print("\n" + "="*60)
        print("🚀 STARTING ROBUST HYPERPARAMETER OPTIMIZATION")
        print("="*60)
        
        # Load data and create splits
        self.load_and_prepare_data()
        self.create_nested_time_series_splits()
        
        all_fold_results = []
        
        # Outer loop: Evaluate different hyperparameters
        for fold_idx, split in enumerate(self.outer_splits):
            print(f"\n📊 OUTER FOLD {fold_idx + 1}/{len(self.outer_splits)}")
            print(f"Train until: {split['train_end'].date()}")
            print(f"Test: {split['test_start'].date()} to {split['test_end'].date()}")
            
            # Prepare outer fold data
            train_mask = self.data['Date'] <= split['train_end']
            test_mask = (self.data['Date'] >= split['test_start']) & (self.data['Date'] <= split['test_end'])
            
            outer_train_data = self.data[train_mask]
            outer_test_data = self.data[test_mask]
            
            print(f"Train samples: {len(outer_train_data)}, Test samples: {len(outer_test_data)}")
            
            # Inner optimization loop
            print(f"🔍 Running Bayesian optimization ({self.n_trials} trials)...")
            
            study = optuna.create_study(
                direction='maximize',
                sampler=optuna.samplers.TPESampler(seed=42)
            )
            
            # Create inner validation split for this outer fold
            inner_split_date = outer_train_data['Date'].quantile(0.8)  # Use 80% for inner train
            inner_train_data = outer_train_data[outer_train_data['Date'] <= inner_split_date]
            inner_val_data = outer_train_data[outer_train_data['Date'] > inner_split_date]
            
            # Optimize hyperparameters
            study.optimize(
                lambda trial: self.objective_function(trial, inner_train_data, inner_val_data),
                n_trials=self.n_trials,
                show_progress_bar=True
            )
            
            # Get best parameters for this fold
            best_params = study.best_params
            best_params.update({
                'objective': 'rank_xendcg',
                'metric': 'ndcg',
                'boosting_type': 'gbdt',
                'verbose': -1,
                'random_state': 42
            })
            
            print(f"✓ Best trial value: {study.best_value:.4f}")
            print(f"✓ Best parameters found for fold {fold_idx + 1}")
            
            # Evaluate best parameters on outer test set
            X_train = outer_train_data[self.feature_cols].fillna(0)
            y_train = self.convert_to_relevance_scores(outer_train_data['Weighted_MixRel'], outer_train_data['Date'])
            train_groups = self.prepare_ranking_data(outer_train_data['Date'])
            
            X_test = outer_test_data[self.feature_cols].fillna(0)
            y_test = self.convert_to_relevance_scores(outer_test_data['Weighted_MixRel'], outer_test_data['Date'])
            test_groups = self.prepare_ranking_data(outer_test_data['Date'])
            
            # Train final model with best parameters
            train_dataset = lgb.Dataset(X_train, label=y_train, group=train_groups)
            test_dataset = lgb.Dataset(X_test, label=y_test, group=test_groups, reference=train_dataset)
            
            final_model = lgb.train(
                best_params,
                train_dataset,
                valid_sets=[test_dataset],
                callbacks=[lgb.log_evaluation(0)]
            )
            
            # Evaluate on test set
            test_predictions = final_model.predict(X_test)
            test_metrics = self.calculate_portfolio_metrics(
                test_predictions,
                outer_test_data['Future5DReturn'],
                outer_test_data['Date']
            )
            
            fold_result = {
                'fold': fold_idx + 1,
                'best_params': best_params,
                'test_sharpe': test_metrics['sharpe'],
                'test_cagr': test_metrics['cagr'],
                'test_max_drawdown': test_metrics['max_drawdown'],
                'optimization_trials': len(study.trials)
            }
            
            all_fold_results.append(fold_result)
            
            print(f"📊 Fold {fold_idx + 1} Results:")
            print(f"  Sharpe: {test_metrics['sharpe']:.3f}")
            print(f"  CAGR: {test_metrics['cagr']*100:.1f}%")
            print(f"  Max DD: {test_metrics['max_drawdown']*100:.1f}%")
        
        self.optimization_results = all_fold_results
        return all_fold_results

def main():
    # Initialize optimizer
    optimizer = RobustHyperparameterOptimizer(
        n_outer_folds=3,  # Start with 3 folds for faster testing
        n_inner_folds=3,
        n_trials=50       # Start with 50 trials, can increase later
    )
    
    # Run optimization
    results = optimizer.optimize_hyperparameters()
    
    # Analyze results
    print("\n" + "="*60)
    print("📊 HYPERPARAMETER OPTIMIZATION RESULTS")
    print("="*60)
    
    sharpes = [r['test_sharpe'] for r in results]
    cagrs = [r['test_cagr'] for r in results]
    drawdowns = [r['test_max_drawdown'] for r in results]
    
    print(f"Average Test Sharpe: {np.mean(sharpes):.3f} ± {np.std(sharpes):.3f}")
    print(f"Average Test CAGR: {np.mean(cagrs)*100:.1f}% ± {np.std(cagrs)*100:.1f}%")
    print(f"Average Test Max DD: {np.mean(drawdowns)*100:.1f}% ± {np.std(drawdowns)*100:.1f}%")
    
    # Find best performing fold
    best_fold_idx = np.argmax(sharpes)
    best_params = results[best_fold_idx]['best_params']
    
    print(f"\n🏆 BEST PERFORMING HYPERPARAMETERS (Fold {best_fold_idx + 1}):")
    print(f"Test Sharpe: {sharpes[best_fold_idx]:.3f}")
    for param, value in best_params.items():
        if param not in ['objective', 'metric', 'boosting_type', 'verbose', 'random_state']:
            print(f"  {param}: {value}")

if __name__ == "__main__":
    main()
