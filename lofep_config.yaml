# LightGBM-Optimized Feature Engineering Pipeline (LOFEP) Configuration

# Data Configuration
data:
  features_file: "Final_Comprehensive_ML_Features_v10.csv"
  target_file: "PureEURTarget.csv"
  returns_file: "PureEURFuture1WRet.csv"
  target_column: "Weighted_MixRel"
  returns_column: "Future5DReturn"
  date_column: "Date"
  id_column: "P123_ID"
  ticker_column: "Ticker"

# Validation Configuration
validation:
  method: "time_series_cv"
  train_months: 12
  validation_months: 3
  min_periods: 8  # Minimum validation periods required
  significance_threshold: 0.02  # Minimum Sharpe improvement required
  stability_threshold: 0.8  # Feature must be beneficial in 80% of periods
  correlation_threshold: 0.8  # Maximum correlation with existing features

# Feature Generation Configuration
feature_generation:
  # Mathematical Transformations
  mathematical:
    enabled: true
    transformations:
      - "rank"
      - "log"
      - "zscore"

  # Cross-sectional Features
  cross_sectional:
    enabled: true
    grouping_columns:
      - "Market_Cap_Quintile"
    transformations:
      - "rank"
      - "zscore"

  # Temporal Features
  temporal:
    enabled: true
    windows: [6, 12]  # months (reduced from 4 to 2 windows)
    transformations:
      - "rolling_mean"
      - "momentum"
    
  # Ratio Features
  ratio:
    enabled: true
    normalization_methods:
      - "sector_median"
      - "industry_median"
      - "market_median"
      - "historical_median"
    
  # Selective Interactions
  interactions:
    enabled: false  # Start disabled, enable after basic features validated
    max_interactions: 50
    min_individual_importance: 0.01

# Feature Selection Configuration
feature_selection:
  method: "incremental_addition"
  max_features_per_batch: 10
  early_stopping_patience: 5
  redundancy_check: true
  stability_check: true
  
# LightGBM Configuration
lightgbm:
  params:
    objective: "regression"
    metric: "rmse"
    boosting_type: "gbdt"
    num_leaves: 31
    learning_rate: 0.1
    feature_fraction: 0.9
    bagging_fraction: 0.8
    bagging_freq: 5
    verbose: -1
    random_state: 42
  
  training:
    num_boost_round: 1000
    early_stopping_rounds: 100
    verbose_eval: False

# Performance Metrics
metrics:
  primary: "sharpe_ratio"
  secondary:
    - "cagr"
    - "max_drawdown"
    - "hit_rate"
    - "information_ratio"

# Monitoring Configuration
monitoring:
  enabled: true
  performance_tracking: true
  drift_detection: true
  feature_importance_tracking: true
  alert_thresholds:
    performance_drop: 0.1  # 10% performance drop triggers alert
    feature_drift: 0.3     # 30% feature importance change triggers alert

# Output Configuration
output:
  save_results: true
  results_dir: "lofep_results"
  save_features: true
  save_models: true
  save_validation_results: true
  generate_reports: true

# Computational Configuration
computation:
  n_jobs: -1  # Use all available cores
  chunk_size: 10000  # For large datasets
  memory_limit: "8GB"
  cache_transformations: true

# Logging Configuration
logging:
  level: "INFO"
  file: "lofep.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
