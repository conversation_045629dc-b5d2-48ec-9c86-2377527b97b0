#!/usr/bin/env python3
"""
Simple 100-level granularity test
"""

from lightgbm_validation_granularity_test import LightGBMGranularityTester
import numpy as np
from datetime import datetime

def main():
    print("🚀 Running 100-level granularity test...")
    
    try:
        # Create validator with 100 levels
        validator_100 = LightGBMGranularityTester(n_relevance_levels=100)
        
        # Run validation
        results_100 = validator_100.run_validation()
        
        # Save results
        filename_100 = validator_100.save_results(suffix='_100levels')
        
        # Calculate summary stats
        sharpes_100 = [r['sharpe'] for r in results_100]
        cagrs_100 = [r['cagr'] for r in results_100]
        ndcg_10s_100 = [r.get('ndcg_10', 0) for r in results_100]
        
        print(f'\n✅ 100 LEVELS COMPLETED:')
        print(f'   Average Sharpe: {np.mean(sharpes_100):.3f} ± {np.std(sharpes_100):.3f}')
        print(f'   Average CAGR: {np.mean(cagrs_100)*100:.1f}% ± {np.std(cagrs_100)*100:.1f}%')
        print(f'   Average NDCG@10: {np.mean(ndcg_10s_100):.3f}')
        print(f'   Results saved to: {filename_100}')
        
        # Compare with 5-level results (hardcoded from previous run)
        sharpe_5_avg = 2.090
        cagr_5_avg = 0.658
        
        sharpe_improvement = (np.mean(sharpes_100) - sharpe_5_avg) / sharpe_5_avg * 100
        cagr_improvement = (np.mean(cagrs_100) - cagr_5_avg) / cagr_5_avg * 100
        
        print(f'\n🏆 COMPARISON WITH 5 LEVELS:')
        print(f'   Sharpe improvement: {sharpe_improvement:+.1f}%')
        print(f'   CAGR improvement: {cagr_improvement:+.1f}%')
        
        if np.mean(sharpes_100) > sharpe_5_avg:
            print(f'   🎉 100 levels WIN!')
        else:
            print(f'   🤔 5 levels still better')
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
