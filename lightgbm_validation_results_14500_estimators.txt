============================================================
🚀 LIGHTGBM VALIDATION RESULTS - FULL 14,500 ESTIMATORS
============================================================

CONFIGURATION:
- Objective: rank_xendcg (ranking)
- Estimators: 14,500 (NO early stopping)
- Time Period: 2005-12-01 to 2025-05-01
- Cross-Validation: 10-fold time series with 6-month gaps
- Portfolio: Top 20 long-only, equal-weighted, weekly rebalancing
- Features: 480 total features
- Data: 988,970 samples across 1,013 unique dates

============================================================
📊 INDIVIDUAL FOLD RESULTS
============================================================

Fold 1: Train 2005-12-03 to 2007-09-08, Val 2008-03-08 to 2009-12-12
  Sharpe Ratio: 1.642
  CAGR: 75.6%
  Max Drawdown: -38.5%
  Total Return: 173.7%

Fold 2: Train 2005-12-03 to 2009-06-13, Val 2009-12-12 to 2011-09-17
  Sharpe Ratio: 1.306
  CAGR: 41.7%
  Max Drawdown: -22.5%
  Total Return: 86.4%

Fold 3: Train 2005-12-03 to 2011-03-19, Val 2011-09-17 to 2013-06-22
  Sharpe Ratio: 2.439
  CAGR: 83.7%
  Max Drawdown: -12.3%
  Total Return: 196.6%

Fold 4: Train 2005-12-03 to 2012-12-22, Val 2013-06-22 to 2015-03-28
  Sharpe Ratio: 4.213
  CAGR: 116.5%
  Max Drawdown: -7.2%
  Total Return: 298.1%

Fold 5: Train 2005-12-03 to 2014-09-27, Val 2015-03-28 to 2016-12-31
  Sharpe Ratio: 2.411
  CAGR: 83.5%
  Max Drawdown: -13.8%
  Total Return: 196.1%

Fold 6: Train 2005-12-03 to 2016-07-02, Val 2016-12-31 to 2018-10-06
  Sharpe Ratio: 3.013
  CAGR: 67.6%
  Max Drawdown: -8.6%
  Total Return: 151.9%

Fold 7: Train 2005-12-03 to 2018-04-07, Val 2018-10-06 to 2020-07-11
  Sharpe Ratio: 1.576
  CAGR: 76.6%
  Max Drawdown: -46.4%
  Total Return: 176.4%

Fold 8: Train 2005-12-03 to 2020-01-11, Val 2020-07-11 to 2022-04-16
  Sharpe Ratio: 2.446
  CAGR: 89.1%
  Max Drawdown: -21.0%
  Total Return: 212.6%

Fold 9: Train 2005-12-03 to 2021-10-16, Val 2022-04-16 to 2024-01-20
  Sharpe Ratio: 1.146
  CAGR: 26.9%
  Max Drawdown: -18.4%
  Total Return: 53.1%

Fold 10: Train 2005-12-03 to 2023-07-22, Val 2024-01-20 to 2025-04-26
  Sharpe Ratio: 1.679
  CAGR: 38.7%
  Max Drawdown: -13.6%
  Total Return: 52.5%

============================================================
📈 SUMMARY STATISTICS
============================================================

Average Sharpe Ratio: 2.187 ± 0.881
Average CAGR: 70.0% ± 25.7%
Average Max Drawdown: -20.2% ± 12.2%

Best Performing Fold: Fold 4 (Sharpe 4.213, CAGR 116.5%)
Worst Performing Fold: Fold 9 (Sharpe 1.146, CAGR 26.9%)

Consistency Metrics:
- All folds positive returns: YES
- Sharpe > 1.0 in all folds: YES
- CAGR > 25% in all folds: YES

============================================================
🔮 FINAL PREDICTIONS (2025-04-26)
============================================================

Model Used: Best performing model from Fold 4 (Sharpe: 4.213)
Prediction Date: 2025-04-26
Number of Stocks: 976

TOP 20 STOCK PREDICTIONS:
1.  SRB:GBR      | Prediction: 0.3420 | Target: -3.87
2.  TET:GBR      | Prediction: 0.3174 | Target: -0.13
3.  FOUR:GBR     | Prediction: 0.2653 | Target:  4.08
4.  RWS:GBR      | Prediction: 0.2607 | Target: 12.94
5.  TROAX:SWE    | Prediction: 0.2539 | Target:  7.93
6.  FUTR:GBR     | Prediction: 0.2263 | Target: -4.95
7.  GL9:IRL      | Prediction: 0.2258 | Target:  9.60
8.  GNC:GBR      | Prediction: 0.2204 | Target:  4.79
9.  WLN:FRA      | Prediction: 0.2142 | Target:  1.59
10. BOY:GBR      | Prediction: 0.2137 | Target:  6.21
11. RBW:POL      | Prediction: 0.2080 | Target:  4.28
12. STM:DEU      | Prediction: 0.2051 | Target:  2.66
13. DOM:SWE      | Prediction: 0.2047 | Target:  3.34
14. DATA:GBR     | Prediction: 0.2040 | Target: 21.84
15. SXS:GBR      | Prediction: 0.2031 | Target: -1.05
16. CALN:CHE     | Prediction: 0.2028 | Target: -0.07
17. NCAB:SWE     | Prediction: 0.2018 | Target:  0.42
18. HIAB:FIN     | Prediction: 0.1921 | Target:  8.19
19. TGS:NOR      | Prediction: 0.1920 | Target:  2.70
20. NFG:GBR      | Prediction: 0.1902 | Target:  3.20

============================================================
🔍 FEATURE IMPORTANCE SUMMARY
============================================================

SHAP Analysis: Comprehensive analysis across all 10 folds
Total Samples Analyzed: 5,000 (500 per fold)
Features with Meaningful Importance: 457/480 (95.2%)

TOP 20 MOST IMPORTANT FEATURES:
1.  Rel_Sent_Momentum                        | Importance: 0.011860
2.  WeeksToQ_Under_5                         | Importance: 0.011124
3.  Long_Term_Sharpe_vs_Short_Term_CoeffVar  | Importance: 0.010057
4.  Proj_PE_3YRelHist                        | Importance: 0.007683
5.  Est_Vs_PriceMom_Divergence               | Importance: 0.007386
6.  EPS_Next_Yr_Net_Revision_Breadth         | Importance: 0.007252
7.  Vol_Rel_Loop                             | Importance: 0.007223
8.  Sharpe_over_1                            | Importance: 0.007171
9.  Annual_Range_per_Unit_of_Vol             | Importance: 0.007021
10. RSI_Spread_vs_Industry                   | Importance: 0.006448
11. Days_Since_Peak_or_Trough                | Importance: 0.006282
12. Field_Divergence_Indicator               | Importance: 0.006264
13. Range_52Wk_as_Pct_of_Price               | Importance: 0.005839
14. AvgRec_Rank_vs_1Y_History                | Importance: 0.005715
15. Momentum_Rank_div_Liquidity_Rank         | Importance: 0.005440
16. BarsSince_3YHist                         | Importance: 0.005072
17. Price_Target_Upside_Rank_vs_Industry     | Importance: 0.004843
18. Industry_RSI_200D                        | Importance: 0.004724
19. Range_FHistAvg                           | Importance: 0.004710
20. AvgRec_Improvements                      | Importance: 0.004338

Feature Categories:
- Sentiment/Momentum Features: Dominant (Rel_Sent_Momentum #1)
- Earnings Timing: Critical (WeeksToQ_Under_5 #2)
- Risk-Adjusted Returns: Key (Sharpe features)
- Technical Indicators: Important (RSI, Range features)
- Analyst Estimates: Valuable (EPS revisions, recommendations)

============================================================
📊 OUTPUT FILES GENERATED
============================================================

1. comprehensive_feature_importance_FULL_14500_no_early_stopping.csv
   - Complete ranking of all 480 features
   - Includes: feature name, avg_importance, std_importance, cv_importance, rank

2. lightgbm_validation_results_14500_estimators.txt (this file)
   - Complete validation results summary

============================================================
🎯 KEY INSIGHTS
============================================================

1. EXCEPTIONAL PERFORMANCE: 2.19 average Sharpe ratio over 20-year period
2. CONSISTENT ALPHA: All folds generated positive returns with Sharpe > 1.0
3. ROBUST VALIDATION: 10-fold time series CV with proper 6-month gaps
4. COMPREHENSIVE FEATURES: 95.2% of features have meaningful importance
5. SENTIMENT DOMINANCE: Sentiment momentum is the most predictive feature
6. NO EARLY STOPPING BENEFIT: Full 14,500 estimators significantly outperformed
7. RANKING OBJECTIVE SUCCESS: xendcg ranking objective highly effective

============================================================
📅 GENERATED: 2025-06-28
🔬 MODEL: LightGBM with rank_xendcg objective
📈 VALIDATION: 10-fold time series cross-validation
🎯 PERFORMANCE: 2.187 Sharpe, 70.0% CAGR, -20.2% Max DD
============================================================
Hyperparameters:
        self.params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'boosting_type': 'gbdt',
            'n_estimators': 14500,  # FULL original hyperparameters
            'max_depth': 11,
            'learning_rate': 0.00045,
            'num_leaves': 330,
            'subsample': 0.51,
            'min_child_samples': 620,
            'extra_trees': True,
            'path_smooth': 2.1e-9,
            'max_bin': 670,
            'min_data_in_bin': 39,
            'bin_construct_sample_cnt': 780000,
            'lambda_l1': 0.016,
            'lambda_l2': 0.026,
            'min_split_gain': 0.0071,
            'bagging_freq': 3,
            'feature_fraction': 0.24,
            'verbose': -1,
            'random_state': 42
        }