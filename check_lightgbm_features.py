#!/usr/bin/env python3
"""
Check LightGBM feature usage and model characteristics
"""

import pandas as pd
import numpy as np
import lightgbm as lgb

def check_lightgbm_features():
    print("="*60)
    print("🔍 CHECKING LIGHTGBM FEATURE USAGE")
    print("="*60)
    
    # Load the feature importance results
    importance_df = pd.read_csv('comprehensive_feature_importance.csv')
    
    # Check our LightGBM parameters
    params = {
        'objective': 'rank_xendcg',
        'metric': 'ndcg',
        'boosting_type': 'gbdt',
        'n_estimators': 14500,
        'max_depth': 11,
        'learning_rate': 0.00045,
        'num_leaves': 330,
        'subsample': 0.51,
        'min_child_samples': 620,
        'extra_trees': True,
        'path_smooth': 2.1e-9,
        'max_bin': 670,
        'min_data_in_bin': 39,
        'bin_construct_sample_cnt': 780000,
        'lambda_l1': 0.016,
        'lambda_l2': 0.026,
        'min_split_gain': 0.0071,
        'bagging_freq': 3,
        'feature_fraction': 0.24,  # ← THIS IS KEY!
        'verbose': -1,
        'random_state': 42
    }
    
    print("📊 KEY LIGHTGBM PARAMETERS:")
    print(f"  feature_fraction: {params['feature_fraction']}")
    print(f"  lambda_l1: {params['lambda_l1']}")
    print(f"  lambda_l2: {params['lambda_l2']}")
    print(f"  min_split_gain: {params['min_split_gain']}")
    print(f"  early_stopping: 100 rounds")
    
    # Calculate expected feature usage
    total_features = 480
    feature_fraction = params['feature_fraction']
    expected_features_per_tree = int(total_features * feature_fraction)
    
    print(f"\n🌳 FEATURE SELECTION ANALYSIS:")
    print(f"  Total features: {total_features}")
    print(f"  Feature fraction: {feature_fraction} ({feature_fraction*100:.0f}%)")
    print(f"  Expected features per tree: {expected_features_per_tree}")
    print(f"  Features with non-zero importance: {len(importance_df[importance_df['avg_importance'] > 0])}")
    
    # Check if this explains the zero importance
    nonzero_count = len(importance_df[importance_df['avg_importance'] > 0])
    expected_ratio = nonzero_count / total_features
    
    print(f"\n🎯 EXPLANATION:")
    print(f"  Actual non-zero features: {nonzero_count}/{total_features} ({expected_ratio*100:.1f}%)")
    print(f"  Expected from feature_fraction: ~{feature_fraction*100:.0f}%")
    
    if abs(expected_ratio - feature_fraction) < 0.1:
        print("  ✅ MATCH! feature_fraction explains the zero importance features")
    else:
        print("  ❓ Partial match - other factors also involved")
    
    # Additional factors
    print(f"\n🔍 OTHER FACTORS CAUSING ZERO IMPORTANCE:")
    print("1. **Feature Fraction (24%)**: Only 24% of features used per tree")
    print("2. **L1/L2 Regularization**: Sparse feature selection")
    print("3. **Early Stopping**: Models stop before using all features")
    print("4. **Min Split Gain**: Features need minimum gain to be used")
    print("5. **Feature Redundancy**: Correlated features compete")
    
    # Check the zero importance features
    zero_features = importance_df[importance_df['avg_importance'] == 0]['feature'].tolist()
    
    print(f"\n📋 SAMPLE ZERO IMPORTANCE FEATURES:")
    for i, feature in enumerate(zero_features[:10]):
        print(f"  {i+1:2d}. {feature}")
    
    # Check if certain types of features are more likely to be zero
    print(f"\n🏷️ FEATURE TYPE ANALYSIS:")
    
    # Categorize features by name patterns
    categories = {
        'Sector': [f for f in zero_features if 'Sector' in f],
        'Industry': [f for f in zero_features if 'Industry' in f or 'industry' in f],
        'Growth': [f for f in zero_features if 'Growth' in f],
        'Ratio': [f for f in zero_features if 'Ratio' in f],
        'TTM': [f for f in zero_features if 'TTM' in f],
        'Margin': [f for f in zero_features if 'Margin' in f]
    }
    
    for category, features in categories.items():
        if len(features) > 0:
            print(f"  {category}: {len(features)} features")
            if len(features) <= 3:
                for f in features:
                    print(f"    - {f}")
    
    print(f"\n💡 CONCLUSION:")
    print("The zero importance features are primarily due to:")
    print("1. **feature_fraction=0.24** - Only 24% of features used per tree")
    print("2. **Regularization** - L1/L2 penalties create sparse models")
    print("3. **Early stopping** - Models converge before using all features")
    print("4. **Feature competition** - Best features dominate, others get zero usage")
    print("\nThis is NORMAL and EXPECTED behavior for LightGBM with these parameters!")

if __name__ == "__main__":
    check_lightgbm_features()
