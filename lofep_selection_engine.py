#!/usr/bin/env python3
"""
LOFEP Selection Engine
Intelligent feature selection using validation-driven approach
"""

import pandas as pd
import numpy as np
import logging
import os
from typing import Dict, List, Tuple, Any
from lofep_validation_engine import ValidationEngine


class SelectionEngine:
    """
    Engine for intelligent feature selection based on validation performance
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger('LOFEP.SelectionEngine')
        self.validation_engine = ValidationEngine(config)
        
        self.logger.info("SelectionEngine initialized")

    def _clean_feature_name(self, name: str) -> str:
        """Clean feature names for LightGBM compatibility - remove ALL special characters"""
        import re

        # Convert to string and handle any encoding issues
        cleaned = str(name)

        # Replace common problematic characters with meaningful substitutions
        replacements = {
            ' ': '_', '-': '_', '&': 'And', '%': 'Pct', '/': '_div_',
            '*': '_mult_', '+': '_plus_', '=': '_eq_', '<': '_lt_',
            '>': '_gt_', '!': '_not_', '@': '_at_', '$': '_dollar_',
            '(': '', ')': '', '[': '', ']': '', '{': '', '}': '',
            '"': '', "'": '', '`': '', '~': '', '^': '', '|': '_',
            '\\': '_', '?': '', ':': '_', ';': '_', ',': '_', '.': '_'
        }

        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)

        # Remove any remaining special characters (keep only alphanumeric and underscore)
        cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', cleaned)

        # Remove consecutive underscores
        cleaned = re.sub(r'_+', '_', cleaned)

        # Remove leading/trailing underscores
        cleaned = cleaned.strip('_')

        # Ensure it starts with letter or underscore (not digit)
        if cleaned and cleaned[0].isdigit():
            cleaned = 'F_' + cleaned

        # Ensure it's not empty
        if not cleaned:
            cleaned = 'Feature_Unknown'

        return cleaned

    def incremental_feature_selection(self, X: pd.DataFrame, y: pd.Series, dates: pd.Series,
                                    baseline_performance: Dict[str, float]) -> Tuple[List[str], Dict[str, Dict[str, Any]]]:
        """
        Incremental feature selection: add features one by one based on performance improvement
        """

        self.logger.info("Starting incremental feature selection...")

        try:
            self.logger.info(f"Input shapes - X: {X.shape}, y: {y.shape}, dates: {dates.shape}")

            # Get base features (original features)
            exclude_cols = [
                self.config['data']['date_column'],
                self.config['data']['id_column'],
                self.config['data']['ticker_column'],
                self.config['data']['target_column'],
                self.config['data']['returns_column']
            ]

            self.logger.info(f"Exclude columns: {exclude_cols}")

        except Exception as e:
            self.logger.error(f"Error in feature selection initialization: {str(e)}")
            import traceback
            self.logger.error(f"Initialization traceback: {traceback.format_exc()}")
            raise
        
        # Identify original vs generated features
        original_features = []
        generated_features = []
        
        for col in X.columns:
            if col not in exclude_cols:
                # Check if it's a generated feature (contains transformation indicators)
                transformation_indicators = ['_rank', '_log', '_sqrt', '_zscore', '_percentile', 
                                           '_rolling_', '_momentum_', '_mean_reversion_', '_volatility_',
                                           '_vs_', '_ratio', '_x_', '_div_']
                
                if any(indicator in col for indicator in transformation_indicators):
                    generated_features.append(col)
                else:
                    original_features.append(col)
        
        self.logger.info(f"Found {len(original_features)} original features and {len(generated_features)} generated features")
        
        # Start with original features as base
        selected_features = original_features.copy()
        current_performance = baseline_performance
        feature_performance_history = {}
        
        # Candidate features to evaluate (generated features)
        candidate_features = generated_features.copy()
        
        # Selection parameters
        max_features_per_batch = self.config['feature_selection']['max_features_per_batch']
        early_stopping_patience = self.config['feature_selection']['early_stopping_patience']
        
        no_improvement_count = 0
        iteration = 0
        
        while candidate_features and no_improvement_count < early_stopping_patience:
            iteration += 1
            self.logger.info(f"\n--- Selection Iteration {iteration} ---")
            self.logger.info(f"Current features: {len(selected_features)}")
            self.logger.info(f"Candidate features: {len(candidate_features)}")
            self.logger.info(f"Current Sharpe: {current_performance.get('mean_sharpe_ratio', 0):.4f}")
            
            # Evaluate batch of candidate features
            batch_size = min(max_features_per_batch, len(candidate_features))
            batch_candidates = candidate_features[:batch_size]
            
            best_feature = None
            best_improvement = 0
            best_performance = None
            
            for feature_name in batch_candidates:
                try:
                    # Test adding this feature to current selection
                    test_features = selected_features + [feature_name]
                    X_test = X[test_features].fillna(0)
                    
                    # Validate performance
                    feature_performance = self.validation_engine.time_series_cross_validation(
                        X_test, y, dates, f"TEST_{feature_name}"
                    )
                    
                    # Calculate improvement
                    current_sharpe = current_performance.get('mean_sharpe_ratio', 0)
                    test_sharpe = feature_performance.get('mean_sharpe_ratio', 0)
                    improvement = test_sharpe - current_sharpe
                    
                    # Check if this is the best improvement so far
                    if improvement > best_improvement:
                        # Additional validation checks
                        significance_result = self.validation_engine.validate_feature_significance(
                            current_performance, feature_performance
                        )
                        
                        if significance_result['is_accepted']:
                            best_feature = feature_name
                            best_improvement = improvement
                            best_performance = feature_performance
                    
                    # Store performance history
                    feature_performance_history[feature_name] = {
                        'performance': feature_performance,
                        'sharpe_improvement': improvement,
                        'stability_score': feature_performance.get('stability_score', 0),
                        'iteration': iteration
                    }
                    
                    self.logger.debug(f"  {feature_name}: +{improvement:.4f} Sharpe")
                    
                except Exception as e:
                    self.logger.warning(f"Error evaluating {feature_name}: {str(e)}")
                    continue
            
            # Remove evaluated features from candidates
            for feature in batch_candidates:
                if feature in candidate_features:
                    candidate_features.remove(feature)
            
            # Add best feature if found
            if best_feature is not None:
                selected_features.append(best_feature)
                current_performance = best_performance
                no_improvement_count = 0
                
                self.logger.info(f"✓ Added {best_feature}: +{best_improvement:.4f} Sharpe improvement")
                self.logger.info(f"  New Sharpe: {current_performance.get('mean_sharpe_ratio', 0):.4f}")
                
                # Check for redundancy with newly added feature
                if self.config['feature_selection']['redundancy_check']:
                    redundant_features = self._check_feature_redundancy(X, selected_features)
                    if redundant_features:
                        self.logger.info(f"Removing {len(redundant_features)} redundant features")
                        for redundant_feature in redundant_features:
                            if redundant_feature in selected_features:
                                selected_features.remove(redundant_feature)
            else:
                no_improvement_count += 1
                self.logger.info(f"No improvement found in this batch ({no_improvement_count}/{early_stopping_patience})")
        
        # Final cleanup and optimization
        if self.config['feature_selection']['redundancy_check']:
            final_selected = self._final_feature_cleanup(X, selected_features, y, dates, current_performance)
        else:
            final_selected = selected_features
        
        self.logger.info(f"\nFeature selection completed:")
        self.logger.info(f"  Final features: {len(final_selected)}")
        self.logger.info(f"  Iterations: {iteration}")
        self.logger.info(f"  Features evaluated: {len(feature_performance_history)}")
        
        return final_selected, feature_performance_history
    
    def _check_feature_redundancy(self, X: pd.DataFrame, features: List[str]) -> List[str]:
        """Check for redundant features based on correlation"""
        
        correlation_threshold = self.config['validation']['correlation_threshold']
        redundant_features = []
        
        if len(features) < 2:
            return redundant_features
        
        # Calculate correlation matrix
        feature_data = X[features].fillna(0)
        correlation_matrix = feature_data.corr().abs()
        
        # Find highly correlated pairs
        for i in range(len(features)):
            for j in range(i + 1, len(features)):
                feature1 = features[i]
                feature2 = features[j]
                
                correlation = correlation_matrix.iloc[i, j]
                
                if correlation > correlation_threshold:
                    # Keep the feature that was added earlier (lower index)
                    # Remove the later feature
                    if feature2 not in redundant_features:
                        redundant_features.append(feature2)
                        self.logger.debug(f"Marking {feature2} as redundant (corr={correlation:.3f} with {feature1})")
        
        return redundant_features
    
    def _final_feature_cleanup(self, X: pd.DataFrame, features: List[str], y: pd.Series, 
                             dates: pd.Series, current_performance: Dict[str, float]) -> List[str]:
        """Final cleanup: remove features that don't contribute to final model"""
        
        self.logger.info("Performing final feature cleanup...")
        
        # Start with all selected features
        final_features = features.copy()
        
        # Try removing each feature and see if performance drops significantly
        features_to_remove = []
        
        for feature in features:
            # Skip original features (keep them)
            transformation_indicators = ['_rank', '_log', '_sqrt', '_zscore', '_percentile', 
                                       '_rolling_', '_momentum_', '_mean_reversion_', '_volatility_',
                                       '_vs_', '_ratio', '_x_', '_div_']
            
            if not any(indicator in feature for indicator in transformation_indicators):
                continue  # Keep original features
            
            # Test performance without this feature
            test_features = [f for f in final_features if f != feature]
            
            if len(test_features) < 10:  # Don't remove if we have too few features
                continue
            
            try:
                X_test = X[test_features].fillna(0)
                test_performance = self.validation_engine.time_series_cross_validation(
                    X_test, y, dates, f"WITHOUT_{feature}"
                )
                
                current_sharpe = current_performance.get('mean_sharpe_ratio', 0)
                test_sharpe = test_performance.get('mean_sharpe_ratio', 0)
                performance_drop = current_sharpe - test_sharpe
                
                # If performance doesn't drop significantly, remove the feature
                if performance_drop < 0.005:  # Less than 0.005 Sharpe drop
                    features_to_remove.append(feature)
                    self.logger.debug(f"Marking {feature} for removal (performance drop: {performance_drop:.4f})")
                
            except Exception as e:
                self.logger.warning(f"Error testing removal of {feature}: {str(e)}")
                continue
        
        # Remove identified features
        for feature in features_to_remove:
            if feature in final_features:
                final_features.remove(feature)
        
        if features_to_remove:
            self.logger.info(f"Removed {len(features_to_remove)} non-contributing features")
        
        return final_features

    def memory_efficient_feature_selection(self, merged_data: pd.DataFrame, all_features: List[str],
                                          y: pd.Series, dates: pd.Series, returns: pd.Series,
                                          baseline_performance: Dict[str, float]) -> Tuple[List[str], Dict[str, Dict[str, Any]]]:
        """
        Memory-efficient feature selection that doesn't create the full feature matrix
        """

        self.logger.info("Starting memory-efficient feature selection...")
        self.logger.info(f"Total features to evaluate: {len(all_features)}")

        # Start with original features only (much smaller set)
        exclude_cols = [
            self.config['data']['date_column'],
            self.config['data']['id_column'],
            self.config['data']['ticker_column'],
            self.config['data']['target_column'],
            self.config['data']['returns_column']
        ]

        # Identify original vs generated features
        original_features = []
        generated_features = []

        for col in all_features:
            # Check if it's a generated feature (contains transformation indicators)
            transformation_indicators = ['_rank', '_log', '_sqrt', '_zscore', '_percentile',
                                       '_rolling_', '_momentum_', '_mean_reversion_', '_volatility_',
                                       '_vs_', '_ratio', '_x_', '_div_', '_Market_Cap_Quintile']

            if any(indicator in col for indicator in transformation_indicators):
                generated_features.append(col)
            else:
                original_features.append(col)

        self.logger.info(f"Original features: {len(original_features)}")
        self.logger.info(f"Generated features: {len(generated_features)}")

        # Start with original features as baseline
        selected_features = original_features.copy()
        feature_performance_history = {}

        # Evaluate generated features in small batches to avoid memory issues
        batch_size = 50  # Small batch size to control memory
        best_features_found = []

        self.logger.info(f"Evaluating {len(generated_features)} generated features in batches of {batch_size}")

        # Create intermediate results file for crash recovery
        import json
        from datetime import datetime
        results_dir = "lofep_results"
        os.makedirs(results_dir, exist_ok=True)
        intermediate_file = f"{results_dir}/intermediate_feature_selection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        for batch_start in range(0, len(generated_features), batch_size):
            batch_end = min(batch_start + batch_size, len(generated_features))
            batch_features = generated_features[batch_start:batch_end]

            self.logger.info(f"Evaluating batch {batch_start//batch_size + 1}: features {batch_start+1}-{batch_end}")
            batch_results = {}

            # Test each feature in this batch
            for feature_name in batch_features:
                try:
                    # Create small test dataset with current selected features + this new feature
                    test_features = selected_features + [feature_name]

                    # Extract only the needed columns (memory efficient)
                    X_test = merged_data[test_features].fillna(0)

                    # Clean column names for LightGBM compatibility
                    X_test.columns = [self._clean_feature_name(col) for col in X_test.columns]

                    # Quick validation
                    test_performance = self.validation_engine.time_series_cross_validation(
                        X_test, y, dates, f"TEST_{self._clean_feature_name(feature_name)}", returns=returns
                    )

                    # Calculate improvement
                    current_sharpe = baseline_performance.get('mean_sharpe_ratio', 0)
                    test_sharpe = test_performance.get('mean_sharpe_ratio', 0)
                    improvement = test_sharpe - current_sharpe

                    # Store performance
                    feature_performance_history[feature_name] = {
                        'performance': test_performance,
                        'sharpe_improvement': improvement,
                        'stability_score': test_performance.get('stability_score', 0)
                    }

                    # Store in batch results for intermediate saving
                    batch_results[feature_name] = feature_performance_history[feature_name]

                    # If significant improvement, add to best features
                    if improvement > 0.02:  # Minimum improvement threshold
                        best_features_found.append((feature_name, improvement))
                        self.logger.info(f"✓ {feature_name}: +{improvement:.4f} Sharpe improvement")

                    # Clean up to free memory
                    del X_test

                except Exception as e:
                    self.logger.warning(f"Error evaluating {feature_name}: {str(e)}")
                    continue

            # Save intermediate results after each batch
            try:
                # Load existing results if file exists
                if os.path.exists(intermediate_file):
                    with open(intermediate_file, 'r') as f:
                        existing_results = json.load(f)
                else:
                    existing_results = {}

                # Update with batch results
                existing_results.update(batch_results)

                # Save updated results
                with open(intermediate_file, 'w') as f:
                    json.dump(existing_results, f, indent=2, default=str)

                self.logger.info(f"Saved intermediate results for batch {batch_start//batch_size + 1} to {intermediate_file}")

            except Exception as e:
                self.logger.warning(f"Failed to save intermediate results: {str(e)}")

        # Sort best features by improvement and select top ones
        best_features_found.sort(key=lambda x: x[1], reverse=True)
        top_features = [feat[0] for feat in best_features_found[:100]]  # Top 100 features max

        final_selected = selected_features + top_features

        self.logger.info(f"Feature selection completed:")
        self.logger.info(f"  Original features: {len(selected_features)}")
        self.logger.info(f"  Best generated features: {len(top_features)}")
        self.logger.info(f"  Total selected: {len(final_selected)}")

        # Save final results
        try:
            final_results = {
                'selected_features': final_selected,
                'feature_performance_history': feature_performance_history,
                'best_features_found': best_features_found,
                'baseline_performance': baseline_performance,
                'completion_time': datetime.now().isoformat()
            }

            final_file = f"{results_dir}/final_feature_selection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(final_file, 'w') as f:
                json.dump(final_results, f, indent=2, default=str)

            self.logger.info(f"Final results saved to {final_file}")

        except Exception as e:
            self.logger.warning(f"Failed to save final results: {str(e)}")

        return final_selected, feature_performance_history
    
    def backward_elimination(self, X: pd.DataFrame, features: List[str], y: pd.Series, 
                           dates: pd.Series) -> List[str]:
        """Backward elimination: start with all features and remove least important"""
        
        self.logger.info("Starting backward elimination...")
        
        current_features = features.copy()
        
        while len(current_features) > 10:  # Keep at least 10 features
            worst_feature = None
            smallest_drop = float('inf')
            
            # Test removing each feature
            for feature in current_features:
                test_features = [f for f in current_features if f != feature]
                
                try:
                    X_test = X[test_features].fillna(0)
                    test_performance = self.validation_engine.time_series_cross_validation(
                        X_test, y, dates, f"BACKWARD_TEST"
                    )
                    
                    # Calculate performance with all features
                    X_full = X[current_features].fillna(0)
                    full_performance = self.validation_engine.time_series_cross_validation(
                        X_full, y, dates, f"BACKWARD_FULL"
                    )
                    
                    performance_drop = full_performance.get('mean_sharpe_ratio', 0) - test_performance.get('mean_sharpe_ratio', 0)
                    
                    if performance_drop < smallest_drop:
                        smallest_drop = performance_drop
                        worst_feature = feature
                
                except Exception as e:
                    self.logger.warning(f"Error in backward elimination for {feature}: {str(e)}")
                    continue
            
            # Remove worst feature if performance drop is acceptable
            if worst_feature and smallest_drop < 0.01:  # Less than 0.01 Sharpe drop
                current_features.remove(worst_feature)
                self.logger.info(f"Removed {worst_feature} (performance drop: {smallest_drop:.4f})")
            else:
                break  # Stop if no more features can be removed
        
        self.logger.info(f"Backward elimination completed: {len(current_features)} features remaining")
        return current_features
