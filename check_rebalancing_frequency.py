#!/usr/bin/env python3
"""
Check the actual rebalancing frequency in the validation
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def check_rebalancing_frequency():
    print("="*60)
    print("🔍 CHECKING REBALANCING FREQUENCY")
    print("="*60)
    
    # Load the data to check date frequency
    features_df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    
    # Clean feature names
    import re
    name_mapping = {}
    for col in features_df.columns:
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col)
        clean_name = re.sub(r'_+', '_', clean_name)
        clean_name = clean_name.strip('_')
        name_mapping[col] = clean_name
    features_df = features_df.rename(columns=name_mapping)
    
    # Convert dates
    features_df['Date'] = pd.to_datetime(features_df['Date'])
    
    # Filter to validation period
    start_date = pd.to_datetime('2005-12-01')
    end_date = pd.to_datetime('2025-05-01')
    
    data = features_df[(features_df['Date'] >= start_date) & (features_df['Date'] <= end_date)]
    
    # Get unique dates
    unique_dates = sorted(data['Date'].unique())
    
    print(f"Total unique dates: {len(unique_dates)}")
    print(f"Date range: {unique_dates[0].date()} to {unique_dates[-1].date()}")
    
    # Calculate time differences
    date_diffs = []
    for i in range(1, len(unique_dates)):
        diff = (unique_dates[i] - unique_dates[i-1]).days
        date_diffs.append(diff)
    
    date_diffs = np.array(date_diffs)
    
    print(f"\nDate interval analysis:")
    print(f"  Mean days between dates: {date_diffs.mean():.1f}")
    print(f"  Median days between dates: {np.median(date_diffs):.1f}")
    print(f"  Min days between dates: {date_diffs.min()}")
    print(f"  Max days between dates: {date_diffs.max()}")
    
    # Check frequency distribution
    print(f"\nFrequency distribution:")
    unique_diffs, counts = np.unique(date_diffs, return_counts=True)
    for diff, count in zip(unique_diffs, counts):
        percentage = count / len(date_diffs) * 100
        print(f"  {diff:2d} days: {count:4d} occurrences ({percentage:5.1f}%)")
    
    # Determine most likely frequency
    most_common_diff = unique_diffs[np.argmax(counts)]
    
    print(f"\n📊 REBALANCING FREQUENCY ANALYSIS:")
    print(f"Most common interval: {most_common_diff} days")
    
    if most_common_diff == 7:
        frequency = "WEEKLY"
        periods_per_year = 52
    elif most_common_diff == 1:
        frequency = "DAILY"
        periods_per_year = 252
    elif 28 <= most_common_diff <= 31:
        frequency = "MONTHLY"
        periods_per_year = 12
    else:
        frequency = f"CUSTOM ({most_common_diff} days)"
        periods_per_year = 365 / most_common_diff
    
    print(f"Frequency: {frequency}")
    print(f"Periods per year: {periods_per_year}")
    
    # Check what the code assumes
    print(f"\n🔍 CODE ANALYSIS:")
    print("In calculate_portfolio_metrics():")
    print("  Line 245: # Annualize (assuming weekly rebalancing)")
    print("  Line 246: annual_return = mean_return * 52")
    print("  Line 247: annual_volatility = volatility * np.sqrt(52)")
    print("  Line 252: n_years = len(portfolio_returns) / 52")
    
    print(f"\n🎯 CONCLUSION:")
    if most_common_diff == 7:
        print("✅ CODE ASSUMPTION MATCHES DATA: Weekly rebalancing (7 days)")
        print("✅ Annualization factor of 52 is CORRECT")
    else:
        print(f"❌ CODE ASSUMPTION MISMATCH:")
        print(f"   Code assumes: Weekly (52 periods/year)")
        print(f"   Data shows: {frequency} ({periods_per_year:.1f} periods/year)")
        print(f"   This affects performance calculations!")
    
    # Sample some dates to verify
    print(f"\n📅 SAMPLE DATES:")
    for i in range(0, min(20, len(unique_dates)), 1):
        if i < len(unique_dates) - 1:
            diff = (unique_dates[i+1] - unique_dates[i]).days
            print(f"  {unique_dates[i].date()} -> {unique_dates[i+1].date()} ({diff} days)")
        else:
            print(f"  {unique_dates[i].date()}")

if __name__ == "__main__":
    check_rebalancing_frequency()
