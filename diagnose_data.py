#!/usr/bin/env python3
"""
Diagnose what's wrong with the data/calculations
"""

import pandas as pd
import numpy as np

def diagnose_data():
    """Check the data for obvious issues"""
    
    print("🔍 DIAGNOSING DATA ISSUES")
    print("="*50)
    
    # Load the main dataset
    print("Loading data...")
    df = pd.read_csv('Final_Comprehensive_ML_Features_v10.csv')
    print(f"Dataset shape: {df.shape}")
    print(f"Date range: {df['Date'].min()} to {df['Date'].max()}")
    
    # Check for target files
    try:
        target_df = pd.read_csv('PureEURTarget.csv')
        returns_df = pd.read_csv('PureEURFuture1WRet.csv')
        
        print(f"\nTarget file: {target_df.shape}")
        print(f"Returns file: {returns_df.shape}")
        
        print(f"Target columns: {list(target_df.columns)}")
        print(f"Returns columns: {list(returns_df.columns)}")
        
        # Check target values
        target_col = [col for col in target_df.columns if col not in ['Date', 'P123_ID']][0]
        returns_col = [col for col in returns_df.columns if col not in ['Date', 'P123_ID']][0]
        
        print(f"\nTarget column: {target_col}")
        print(f"Returns column: {returns_col}")
        
        print(f"\nTarget stats:")
        print(target_df[target_col].describe())
        
        print(f"\nReturns stats:")
        print(returns_df[returns_col].describe())
        
        # Check for obvious data leakage
        print(f"\nChecking for data leakage...")
        
        # Merge data
        merged = df.merge(target_df, left_on=['Date', 'P123 ID'], right_on=['Date', 'P123_ID'], how='inner')
        merged = merged.merge(returns_df, left_on=['Date', 'P123 ID'], right_on=['Date', 'P123_ID'], how='inner')
        
        print(f"Merged data: {merged.shape}")
        
        # Check correlation between target and returns
        target_values = merged[target_col]
        return_values = merged[returns_col]
        
        correlation = np.corrcoef(target_values, return_values)[0, 1]
        print(f"Correlation between target and returns: {correlation:.4f}")
        
        if correlation > 0.9:
            print("⚠️  WARNING: Very high correlation suggests possible data leakage!")
        
        # Check return magnitudes
        print(f"\nReturn value analysis:")
        print(f"Mean return: {return_values.mean():.6f}")
        print(f"Std return: {return_values.std():.6f}")
        print(f"Min return: {return_values.min():.6f}")
        print(f"Max return: {return_values.max():.6f}")
        
        # Check if returns are in decimal or percentage form
        if return_values.mean() > 0.1:
            print("⚠️  Returns appear to be in percentage form (>10%)")
        elif return_values.mean() > 0.01:
            print("⚠️  Returns appear to be in decimal form but very high (>1%)")
        else:
            print("✓ Returns appear to be in reasonable decimal form")
        
        # Sample some data
        print(f"\nSample data (first 10 rows):")
        sample = merged[['Date', 'P123 ID', 'Ticker', target_col, returns_col]].head(10)
        print(sample.to_string())
        
        # Check for time alignment issues
        print(f"\nChecking time alignment...")
        dates = pd.to_datetime(merged['Date'])
        unique_dates = sorted(dates.unique())
        print(f"Number of unique dates: {len(unique_dates)}")
        print(f"First few dates: {unique_dates[:5]}")
        print(f"Last few dates: {unique_dates[-5:]}")
        
        # Check stocks per date
        stocks_per_date = merged.groupby('Date').size()
        print(f"Stocks per date - mean: {stocks_per_date.mean():.0f}, min: {stocks_per_date.min()}, max: {stocks_per_date.max()}")
        
        # Simple portfolio test
        print(f"\nSimple portfolio test...")
        
        # Take one date
        test_date = unique_dates[len(unique_dates)//2]  # Middle date
        test_data = merged[merged['Date'] == test_date].copy()
        
        if len(test_data) >= 15:
            # Sort by target and take top 15
            top_15 = test_data.nlargest(15, target_col)
            portfolio_return = top_15[returns_col].mean()
            
            print(f"Test date: {test_date}")
            print(f"Stocks available: {len(test_data)}")
            print(f"Top 15 portfolio return: {portfolio_return:.6f}")
            print(f"Individual returns in portfolio: {top_15[returns_col].tolist()}")
            
            if abs(portfolio_return) > 0.1:
                print("⚠️  Portfolio return > 10% for one period - this is unrealistic!")
        
        # Check for obvious feature leakage
        print(f"\nChecking for feature leakage...")
        feature_cols = [col for col in merged.columns 
                       if col not in ['Date', 'P123 ID', 'P123_ID', 'Ticker', target_col, returns_col]
                       and merged[col].dtype in ['int64', 'float64']]
        
        # Check correlation between features and future returns
        high_corr_features = []
        for col in feature_cols[:50]:  # Check first 50 features
            try:
                corr = np.corrcoef(merged[col].fillna(0), return_values)[0, 1]
                if abs(corr) > 0.5:
                    high_corr_features.append((col, corr))
            except:
                continue
        
        if high_corr_features:
            print(f"Features with suspiciously high correlation to returns:")
            for feat, corr in sorted(high_corr_features, key=lambda x: abs(x[1]), reverse=True)[:10]:
                print(f"  {feat}: {corr:.4f}")
        else:
            print("✓ No features with suspiciously high correlation found")
        
    except Exception as e:
        print(f"Error loading target files: {e}")
        
        # Try to find returns in main dataset
        print(f"\nLooking for return columns in main dataset...")
        return_candidates = []
        for col in df.columns:
            if 'return' in col.lower() and any(period in col.lower() for period in ['1w', '1m', '5d']):
                return_candidates.append(col)
        
        print(f"Return candidates: {return_candidates}")
        
        if return_candidates:
            for col in return_candidates[:3]:
                print(f"\n{col} stats:")
                print(df[col].describe())

if __name__ == "__main__":
    diagnose_data()
