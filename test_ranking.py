#!/usr/bin/env python3
"""
Test ranking functionality for LightGBM
"""

import pandas as pd
import numpy as np
import lightgbm as lgb

def test_ranking():
    print("Testing LightGBM ranking setup...")
    
    # Create simple test data
    np.random.seed(42)
    
    # 3 dates, 5 stocks each
    dates = ['2020-01-01', '2020-01-02', '2020-01-03']
    n_stocks_per_date = 5
    
    data = []
    for date in dates:
        for i in range(n_stocks_per_date):
            data.append({
                'date': date,
                'stock_id': f'stock_{i}',
                'feature1': np.random.randn(),
                'feature2': np.random.randn(),
                'target_score': np.random.randn()
            })
    
    df = pd.DataFrame(data)
    print(f"Test data shape: {df.shape}")
    print(df.head(10))
    
    # Convert to ranks within each date
    df['rank'] = 0
    for date in df['date'].unique():
        date_mask = df['date'] == date
        date_scores = df.loc[date_mask, 'target_score']
        # Create 0-indexed ranks (0 = worst, 4 = best for 5 stocks)
        ranks = date_scores.rank(method='dense', ascending=True) - 1  # Make 0-indexed
        df.loc[date_mask, 'rank'] = ranks.astype(int)
    
    print("\nRanks by date:")
    for date in df['date'].unique():
        date_data = df[df['date'] == date]
        print(f"{date}: ranks = {date_data['rank'].tolist()}")
    
    # Prepare for LightGBM
    X = df[['feature1', 'feature2']]
    y = df['rank']
    
    # Create groups (number of samples per group)
    groups = [n_stocks_per_date] * len(dates)
    print(f"Groups: {groups}")
    
    # Test LightGBM
    try:
        train_data = lgb.Dataset(X, label=y, group=groups)
        
        params = {
            'objective': 'rank_xendcg',
            'metric': 'ndcg',
            'verbose': -1
        }
        
        model = lgb.train(params, train_data, num_boost_round=10)
        print("✓ LightGBM ranking training successful!")
        
        # Test predictions
        preds = model.predict(X)
        print(f"Predictions shape: {preds.shape}")
        print(f"Sample predictions: {preds[:5]}")
        
    except Exception as e:
        print(f"❌ LightGBM error: {e}")

if __name__ == "__main__":
    test_ranking()
